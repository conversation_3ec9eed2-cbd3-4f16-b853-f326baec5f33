"use client"

import { useState } from "react"
import Link from "next/link"
import { AdminLayout } from "@/components/layout/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Truck,
  Package,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Download,
  RefreshCw,
  Mail,
  Phone,
  MapPin,
  CreditCard,
  Calendar,
  User,
} from "lucide-react"

// Mock orders data
const mockOrders = [
  {
    id: "ORD-2024-001",
    orderNumber: "#2024-001",
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      avatar: "/customer-1.jpg"
    },
    status: "completed",
    paymentStatus: "paid",
    fulfillmentStatus: "delivered",
    total: 299.99,
    subtotal: 249.99,
    tax: 20.00,
    shipping: 30.00,
    items: [
      {
        id: "1",
        name: "Premium Wireless Headphones",
        sku: "PWH-001",
        quantity: 1,
        price: 199.99,
        image: "/product-1.jpg"
      },
      {
        id: "2",
        name: "Wireless Charging Pad",
        sku: "WCP-004",
        quantity: 1,
        price: 49.99,
        image: "/product-4.jpg"
      }
    ],
    shippingAddress: {
      name: "John Doe",
      street: "123 Main St",
      city: "New York",
      state: "NY",
      zip: "10001",
      country: "USA"
    },
    paymentMethod: "Credit Card (**** 4242)",
    createdAt: new Date("2024-01-20T10:30:00"),
    updatedAt: new Date("2024-01-22T14:20:00"),
    notes: "Customer requested expedited shipping"
  },
  {
    id: "ORD-2024-002",
    orderNumber: "#2024-002",
    customer: {
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+****************",
      avatar: "/customer-2.jpg"
    },
    status: "processing",
    paymentStatus: "paid",
    fulfillmentStatus: "preparing",
    total: 159.99,
    subtotal: 139.99,
    tax: 11.20,
    shipping: 8.80,
    items: [
      {
        id: "3",
        name: "Smart Fitness Watch",
        sku: "SFW-002",
        quantity: 1,
        price: 139.99,
        image: "/product-2.jpg"
      }
    ],
    shippingAddress: {
      name: "Jane Smith",
      street: "456 Oak Ave",
      city: "Los Angeles",
      state: "CA",
      zip: "90210",
      country: "USA"
    },
    paymentMethod: "PayPal",
    createdAt: new Date("2024-01-19T15:45:00"),
    updatedAt: new Date("2024-01-20T09:15:00"),
    notes: ""
  },
  {
    id: "ORD-2024-003",
    orderNumber: "#2024-003",
    customer: {
      name: "Mike Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      avatar: "/customer-3.jpg"
    },
    status: "shipped",
    paymentStatus: "paid",
    fulfillmentStatus: "shipped",
    total: 89.99,
    subtotal: 79.99,
    tax: 6.40,
    shipping: 3.60,
    items: [
      {
        id: "4",
        name: "Wireless Charging Pad",
        sku: "WCP-004",
        quantity: 1,
        price: 79.99,
        image: "/product-4.jpg"
      }
    ],
    shippingAddress: {
      name: "Mike Johnson",
      street: "789 Pine St",
      city: "Chicago",
      state: "IL",
      zip: "60601",
      country: "USA"
    },
    paymentMethod: "Credit Card (**** 1234)",
    createdAt: new Date("2024-01-18T11:20:00"),
    updatedAt: new Date("2024-01-19T16:30:00"),
    notes: "Fragile items - handle with care"
  },
  {
    id: "ORD-2024-004",
    orderNumber: "#2024-004",
    customer: {
      name: "Sarah Wilson",
      email: "<EMAIL>",
      phone: "+****************",
      avatar: "/customer-4.jpg"
    },
    status: "pending",
    paymentStatus: "pending",
    fulfillmentStatus: "pending",
    total: 449.99,
    subtotal: 399.99,
    tax: 32.00,
    shipping: 18.00,
    items: [
      {
        id: "5",
        name: "Ergonomic Office Chair",
        sku: "EOC-003",
        quantity: 1,
        price: 399.99,
        image: "/product-3.jpg"
      }
    ],
    shippingAddress: {
      name: "Sarah Wilson",
      street: "321 Elm St",
      city: "Miami",
      state: "FL",
      zip: "33101",
      country: "USA"
    },
    paymentMethod: "Bank Transfer",
    createdAt: new Date("2024-01-17T13:10:00"),
    updatedAt: new Date("2024-01-17T13:10:00"),
    notes: "Waiting for payment confirmation"
  },
  {
    id: "ORD-2024-005",
    orderNumber: "#2024-005",
    customer: {
      name: "David Brown",
      email: "<EMAIL>",
      phone: "+****************",
      avatar: "/customer-5.jpg"
    },
    status: "cancelled",
    paymentStatus: "refunded",
    fulfillmentStatus: "cancelled",
    total: 199.99,
    subtotal: 179.99,
    tax: 14.40,
    shipping: 5.60,
    items: [
      {
        id: "6",
        name: "Professional Camera Lens",
        sku: "PCL-005",
        quantity: 1,
        price: 179.99,
        image: "/product-5.jpg"
      }
    ],
    shippingAddress: {
      name: "David Brown",
      street: "654 Maple Ave",
      city: "Seattle",
      state: "WA",
      zip: "98101",
      country: "USA"
    },
    paymentMethod: "Credit Card (**** 5678)",
    createdAt: new Date("2024-01-16T09:30:00"),
    updatedAt: new Date("2024-01-17T11:45:00"),
    notes: "Customer requested cancellation due to change of mind"
  }
]

const orderStatuses = ["All Status", "pending", "processing", "shipped", "completed", "cancelled"]
const paymentStatuses = ["All Payment", "pending", "paid", "failed", "refunded"]
const fulfillmentStatuses = ["All Fulfillment", "pending", "preparing", "shipped", "delivered", "cancelled"]

const getStatusColor = (status: string, type: "order" | "payment" | "fulfillment") => {
  const colors = {
    order: {
      pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      processing: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      shipped: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      completed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    },
    payment: {
      pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      paid: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      failed: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      refunded: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    },
    fulfillment: {
      pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      preparing: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      shipped: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      delivered: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    }
  }
  return colors[type][status as keyof typeof colors[typeof type]] || "bg-gray-100 text-gray-800"
}

const getStatusIcon = (status: string, type: "order" | "payment" | "fulfillment") => {
  const icons = {
    order: {
      pending: Clock,
      processing: RefreshCw,
      shipped: Truck,
      completed: CheckCircle,
      cancelled: XCircle,
    },
    payment: {
      pending: Clock,
      paid: CheckCircle,
      failed: XCircle,
      refunded: RefreshCw,
    },
    fulfillment: {
      pending: Clock,
      preparing: Package,
      shipped: Truck,
      delivered: CheckCircle,
      cancelled: XCircle,
    }
  }
  return icons[type][status as keyof typeof icons[typeof type]] || Clock
}

export default function OrdersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedOrderStatus, setSelectedOrderStatus] = useState("All Status")
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState("All Payment")
  const [selectedFulfillmentStatus, setSelectedFulfillmentStatus] = useState("All Fulfillment")
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  
  const { t } = useLanguage()
  const { toast } = useToast()

  const filteredOrders = mockOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customer.email.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesOrderStatus = selectedOrderStatus === "All Status" || order.status === selectedOrderStatus
    const matchesPaymentStatus = selectedPaymentStatus === "All Payment" || order.paymentStatus === selectedPaymentStatus
    const matchesFulfillmentStatus = selectedFulfillmentStatus === "All Fulfillment" || order.fulfillmentStatus === selectedFulfillmentStatus
    
    return matchesSearch && matchesOrderStatus && matchesPaymentStatus && matchesFulfillmentStatus
  })

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    )
  }

  const handleSelectAll = () => {
    setSelectedOrders(
      selectedOrders.length === filteredOrders.length 
        ? [] 
        : filteredOrders.map(o => o.id)
    )
  }

  const handleBulkAction = (action: string) => {
    toast({
      title: "Bulk Action",
      description: `${action} applied to ${selectedOrders.length} orders`,
    })
    setSelectedOrders([])
  }

  const handleUpdateOrderStatus = (orderId: string, newStatus: string) => {
    toast({
      title: "Order Updated",
      description: `Order status updated to ${newStatus}`,
    })
  }

  const OrderCard = ({ order }: { order: typeof mockOrders[0] }) => {
    const OrderStatusIcon = getStatusIcon(order.status, "order")
    const PaymentStatusIcon = getStatusIcon(order.paymentStatus, "payment")
    const FulfillmentStatusIcon = getStatusIcon(order.fulfillmentStatus, "fulfillment")

    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              <input
                type="checkbox"
                checked={selectedOrders.includes(order.id)}
                onChange={() => handleSelectOrder(order.id)}
                className="mt-1"
              />
              
              <div className="flex-1 min-w-0">
                {/* Order Header */}
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">
                      {order.orderNumber}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(order.createdAt)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-xl">
                      {formatPrice(order.total)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>

                {/* Customer Info */}
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-gray-500" />
                  </div>
                  <div>
                    <p className="font-medium">{order.customer.name}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {order.customer.email}
                    </p>
                  </div>
                </div>

                {/* Status Badges */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge className={`flex items-center space-x-1 ${getStatusColor(order.status, "order")}`}>
                    <OrderStatusIcon className="h-3 w-3" />
                    <span className="capitalize">{order.status}</span>
                  </Badge>
                  <Badge className={`flex items-center space-x-1 ${getStatusColor(order.paymentStatus, "payment")}`}>
                    <PaymentStatusIcon className="h-3 w-3" />
                    <span className="capitalize">{order.paymentStatus}</span>
                  </Badge>
                  <Badge className={`flex items-center space-x-1 ${getStatusColor(order.fulfillmentStatus, "fulfillment")}`}>
                    <FulfillmentStatusIcon className="h-3 w-3" />
                    <span className="capitalize">{order.fulfillmentStatus}</span>
                  </Badge>
                </div>

                {/* Order Items Preview */}
                <div className="mb-4">
                  <p className="text-sm font-medium mb-2">Items:</p>
                  <div className="flex flex-wrap gap-2">
                    {order.items.slice(0, 3).map((item) => (
                      <div key={item.id} className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-2">
                        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                          <Package className="h-4 w-4 text-gray-500" />
                        </div>
                        <div>
                          <p className="text-xs font-medium line-clamp-1">{item.name}</p>
                          <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                        </div>
                      </div>
                    ))}
                    {order.items.length > 3 && (
                      <div className="flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg p-2 text-xs text-gray-500">
                        +{order.items.length - 3} more
                      </div>
                    )}
                  </div>
                </div>

                {/* Shipping Address */}
                <div className="mb-4">
                  <p className="text-sm font-medium mb-1">Shipping to:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {order.shippingAddress.street}, {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zip}
                  </p>
                </div>

                {/* Notes */}
                {order.notes && (
                  <div className="mb-4">
                    <p className="text-sm font-medium mb-1">Notes:</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded">
                      {order.notes}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Mail className="h-4 w-4 mr-1" />
                      Email
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Phone className="h-4 w-4 mr-1" />
                      Call
                    </Button>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Link href={`/admin/orders/${order.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </Link>
                    <select
                      value={order.status}
                      onChange={(e) => handleUpdateOrderStatus(order.id, e.target.value)}
                      className="px-2 py-1 text-sm border rounded"
                    >
                      <option value="pending">Pending</option>
                      <option value="processing">Processing</option>
                      <option value="shipped">Shipped</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Orders
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage customer orders and fulfillment
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Orders
                  </p>
                  <p className="text-2xl font-bold">
                    {mockOrders.length}
                  </p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Pending Orders
                  </p>
                  <p className="text-2xl font-bold">
                    {mockOrders.filter(o => o.status === 'pending').length}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Processing
                  </p>
                  <p className="text-2xl font-bold">
                    {mockOrders.filter(o => o.status === 'processing').length}
                  </p>
                </div>
                <RefreshCw className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Completed
                  </p>
                  <p className="text-2xl font-bold">
                    {mockOrders.filter(o => o.status === 'completed').length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search orders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-4">
                <select
                  value={selectedOrderStatus}
                  onChange={(e) => setSelectedOrderStatus(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {orderStatuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedPaymentStatus}
                  onChange={(e) => setSelectedPaymentStatus(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {paymentStatuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedFulfillmentStatus}
                  onChange={(e) => setSelectedFulfillmentStatus(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {fulfillmentStatuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedOrders.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">
                    {selectedOrders.length} orders selected
                  </span>
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {selectedOrders.length === filteredOrders.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Mark as Processing')}>
                    Mark Processing
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Mark as Shipped')}>
                    Mark Shipped
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Export Selected')}>
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order) => (
            <OrderCard key={order.id} order={order} />
          ))}
        </div>

        {/* Empty State */}
        {filteredOrders.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No orders found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search or filter criteria
              </p>
              <Button onClick={() => {
                setSearchQuery("")
                setSelectedOrderStatus("All Status")
                setSelectedPaymentStatus("All Payment")
                setSelectedFulfillmentStatus("All Fulfillment")
              }}>
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {filteredOrders.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredOrders.length} of {mockOrders.length} orders
            </p>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" className="bg-primary-50 dark:bg-primary-900">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
