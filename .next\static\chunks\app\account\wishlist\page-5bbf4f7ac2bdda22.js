(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2116],{2921:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var a=r(5453),s=r(6786);let i=(0,a.v)()((0,s.Zr)((e,t)=>({items:[],isOpen:!1,addItem:r=>{let a=t().items,s=a.findIndex(e=>{var t,a;return e.productId===r.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(a=r.variant)?void 0:a.id)});if(s>-1){let t=a[s],i=t.quantity+(r.quantity||1),n=Math.min(r.maxQuantity,t.maxQuantity);e({items:a.map((e,t)=>t===s?{...e,quantity:Math.min(i,n)}:e)})}else{var i;e({items:[...a,{...r,id:"".concat(r.productId,"-").concat((null==(i=r.variant)?void 0:i.id)||"default","-").concat(Date.now()),quantity:r.quantity||1}]})}},removeItem:r=>{e({items:t().items.filter(e=>e.id!==r)})},updateQuantity:(r,a)=>{if(a<=0)return void t().removeItem(r);e({items:t().items.map(e=>e.id===r?{...e,quantity:Math.min(a,e.maxQuantity)}:e)})},clearCart:()=>{e({items:[]})},toggleCart:()=>{e({isOpen:!t().isOpen})},openCart:()=>{e({isOpen:!0})},closeCart:()=>{e({isOpen:!1})},get totalItems(){return t().items.reduce((e,t)=>e+t.quantity,0)},get subtotal(){return t().items.reduce((e,t)=>{var r;return e+((null==(r=t.variant)?void 0:r.price)||t.price)*t.quantity},0)},get tax(){return .08*t().subtotal},get shipping(){return t().subtotal>=100?0:10},get total(){return t().subtotal+t().tax+t().shipping}}),{name:"cart-storage",partialize:e=>({items:e.items})}))},3950:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(5155),s=r(2115),i=r(6874),n=r.n(i),d=r(4997),l=r(285),c=r(6695),o=r(6468),m=r(2921),x=r(4817),h=r(7481),u=r(9434),p=r(7809),g=r(1976),v=r(8564),y=r(2525),f=r(6516),j=r(4653),N=r(5968);function b(){let{items:e,removeItem:t,clearWishlist:r,totalItems:i}=(0,o.q)(),{addItem:b,openCart:w}=(0,m.x)(),{t:k}=(0,x.o)(),{toast:A}=(0,h.dj)(),[C,I]=(0,s.useState)("grid"),[S,O]=(0,s.useState)("newest"),[q,M]=(0,s.useState)("all"),W=e=>{b({productId:e.productId,name:e.name,slug:e.slug,price:e.price,image:e.image,inStock:e.inStock,maxQuantity:10,variant:e.variant}),A({title:"Added to Cart",description:"".concat(e.name," has been added to your cart")}),setTimeout(()=>w(),100)},$=(e,r)=>{t(e),A({title:"Removed from Wishlist",description:"".concat(r," has been removed from your wishlist")})},z=["all",...Array.from(new Set(e.map(e=>e.category)))],D=e.filter(e=>"all"===q||e.category===q).sort((e,t)=>{var r,a,s,i;switch(S){case"name":return e.name.localeCompare(t.name);case"price-low":return((null==(r=e.variant)?void 0:r.price)||e.price)-((null==(a=t.variant)?void 0:a.price)||t.price);case"price-high":return((null==(s=t.variant)?void 0:s.price)||t.price)-((null==(i=e.variant)?void 0:i.price)||e.price);default:return new Date(t.addedAt).getTime()-new Date(e.addedAt).getTime()}}),P=e=>{var t;let{item:r}=e;return(0,a.jsxs)(c.Zp,{className:"group hover:shadow-lg transition-all duration-300",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden",children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-400 text-4xl",children:"\uD83D\uDCE6"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2",children:(0,a.jsxs)(l.$,{size:"sm",onClick:()=>W(r),disabled:!r.inStock,children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),r.inStock?"Add to Cart":"Out of Stock"]})}),(0,a.jsxs)("div",{className:"absolute top-2 left-2 space-y-1",children:[r.comparePrice&&(0,a.jsx)("span",{className:"bg-red-500 text-white px-2 py-1 text-xs rounded",children:"Sale"}),!r.inStock&&(0,a.jsx)("span",{className:"bg-gray-500 text-white px-2 py-1 text-xs rounded",children:"Out of Stock"})]}),(0,a.jsx)("button",{onClick:()=>$(r.id,r.name),className:"absolute top-2 right-2 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-500 fill-current"})})]}),(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm line-clamp-2",children:(0,a.jsx)(n(),{href:"/product/".concat(r.slug),className:"hover:text-primary-600 transition-colors",children:r.name})}),r.variant&&(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:r.variant.name}),r.rating&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)(v.A,{className:"h-3 w-3 ".concat(t<Math.floor(r.rating)?"text-yellow-400 fill-current":"text-gray-300")},t))}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:r.rating})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-bold",children:(0,u.$g)((null==(t=r.variant)?void 0:t.price)||r.price)}),r.comparePrice&&(0,a.jsx)("span",{className:"text-sm text-gray-500 line-through",children:(0,u.$g)(r.comparePrice)})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Added ",(0,u.Yq)(r.addedAt)]}),(0,a.jsxs)("div",{className:"flex space-x-2 pt-2",children:[(0,a.jsxs)(l.$,{size:"sm",onClick:()=>W(r),disabled:!r.inStock,className:"flex-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),r.inStock?"Add to Cart":"Out of Stock"]}),(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>$(r.id,r.name),children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]})})]})};return(0,a.jsx)(d.O,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"My Wishlist"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:[i," ",1===i?"item":"items"," saved for later"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{navigator.share?navigator.share({title:"My Wishlist",text:"Check out my wishlist!",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),A({title:"Link Copied",description:"Wishlist link copied to clipboard"}))},children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Share"]}),e.length>0&&(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{confirm("Are you sure you want to clear your entire wishlist?")&&(r(),A({title:"Wishlist Cleared",description:"All items have been removed from your wishlist"}))},className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Clear All"]})]})]}),0===e.length?(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Your wishlist is empty"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Save items you love to your wishlist and shop them later"}),(0,a.jsx)(n(),{href:"/shop",children:(0,a.jsxs)(l.$,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Start Shopping"]})})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 mb-6",children:[(0,a.jsx)("select",{value:q,onChange:e=>M(e.target.value),className:"px-3 py-2 border rounded-md bg-background",children:z.map(e=>(0,a.jsx)("option",{value:e,children:"all"===e?"All Categories":e},e))}),(0,a.jsxs)("select",{value:S,onChange:e=>O(e.target.value),className:"px-3 py-2 border rounded-md bg-background",children:[(0,a.jsx)("option",{value:"newest",children:"Newest First"}),(0,a.jsx)("option",{value:"name",children:"Name A-Z"}),(0,a.jsx)("option",{value:"price-low",children:"Price: Low to High"}),(0,a.jsx)("option",{value:"price-high",children:"Price: High to Low"})]}),(0,a.jsxs)("div",{className:"flex border rounded-md ml-auto",children:[(0,a.jsx)(l.$,{variant:"grid"===C?"default":"ghost",size:"sm",onClick:()=>I("grid"),className:"rounded-r-none",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"list"===C?"default":"ghost",size:"sm",onClick:()=>I("list"),className:"rounded-l-none",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"grid gap-6 ".concat("grid"===C?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"),children:D.map(e=>(0,a.jsx)(P,{item:e},e.id))}),D.some(e=>e.inStock)&&(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(l.$,{size:"lg",onClick:()=>{let e=D.filter(e=>e.inStock);e.forEach(e=>W(e)),A({title:"Added to Cart",description:"".concat(e.length," items added to your cart")})},children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Add All Available Items to Cart"]})})]})]})})}},4997:(e,t,r)=>{"use strict";r.d(t,{O:()=>f});var a=r(5155),s=r(6874),i=r.n(s),n=r(5695),d=r(9434),l=r(4817),c=r(1007),o=r(4516),m=r(7108),x=r(1976),h=r(3861),u=r(1586),p=r(5525),g=r(381),v=r(9947),y=r(4835);function f(e){let{children:t}=e,r=(0,n.usePathname)(),{t:s}=(0,l.o)(),f=[{name:"Profile",href:"/account/profile",icon:c.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:o.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:m.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:x.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:h.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:u.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:p.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:g.A,description:"Account preferences and settings"}],j=[{name:"Help Center",href:"/help",icon:v.A},{name:"Contact Support",href:"/contact",icon:v.A}];return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,a.jsxs)("nav",{className:"p-2",children:[(0,a.jsx)("div",{className:"space-y-1",children:f.map(e=>{let t=r===e.href;return(0,a.jsxs)(i(),{href:e.href,className:(0,d.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",t?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(e.icon,{className:(0,d.cn)("mr-3 h-5 w-5",t?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{children:e.name}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:e.description})]})]},e.href)})}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"space-y-1",children:[j.map(e=>(0,a.jsxs)(i(),{href:e.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,a.jsx)(e.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),e.name]},e.href)),(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,a.jsx)(y.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:t})})]})})})}},6083:(e,t,r)=>{Promise.resolve().then(r.bind(r,3950))},6468:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});var a=r(5453),s=r(6786);let i=(0,a.v)()((0,s.Zr)((e,t)=>({items:[],addItem:r=>{let a=t().items;if(-1===a.findIndex(e=>{var t,a;return e.productId===r.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(a=r.variant)?void 0:a.id)})){var s;e({items:[...a,{...r,id:"".concat(r.productId,"-").concat((null==(s=r.variant)?void 0:s.id)||"default","-").concat(Date.now()),addedAt:new Date}]})}},removeItem:r=>{e({items:t().items.filter(e=>e.id!==r)})},toggleItem:r=>{let a=t().items,s=a.findIndex(e=>{var t,a;return e.productId===r.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(a=r.variant)?void 0:a.id)});return s>-1?(e({items:a.filter((e,t)=>t!==s)}),!1):(t().addItem(r),!0)},clearWishlist:()=>{e({items:[]})},isInWishlist:(e,r)=>t().items.some(t=>{var a;return t.productId===e&&(null==(a=t.variant)?void 0:a.id)===r}),get totalItems(){return t().items.length}}),{name:"wishlist-storage",partialize:e=>({items:e.items})}))},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>d,wL:()=>m});var a=r(5155),s=r(2115),i=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...s})});d.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...s})});o.displayName="CardContent";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...s})});m.displayName="CardFooter"}},e=>{e.O(0,[3274,9231,9197,8441,5964,7358],()=>e(e.s=6083)),_N_E=e.O()}]);