"use client"

import { ReactNode } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useLanguage } from "@/components/providers/language-provider"
import {
  User,
  MapPin,
  Package,
  Heart,
  Bell,
  Shield,
  CreditCard,
  Settings,
  HelpCircle,
  LogOut,
} from "lucide-react"

interface AccountLayoutProps {
  children: ReactNode
}

export function AccountLayout({ children }: AccountLayoutProps) {
  const pathname = usePathname()
  const { t } = useLanguage()

  const navigation = [
    {
      name: "Profile",
      href: "/account/profile",
      icon: User,
      description: "Manage your personal information",
    },
    {
      name: "Addresses",
      href: "/account/addresses",
      icon: MapPin,
      description: "Manage shipping and billing addresses",
    },
    {
      name: "Orders",
      href: "/account/orders",
      icon: Package,
      description: "View your order history and track shipments",
    },
    {
      name: "Wishlist",
      href: "/account/wishlist",
      icon: Heart,
      description: "Items you've saved for later",
    },
    {
      name: "Notifications",
      href: "/account/notifications",
      icon: Bell,
      description: "Manage your notification preferences",
    },
    {
      name: "Payment Methods",
      href: "/account/payment-methods",
      icon: CreditCard,
      description: "Manage your saved payment methods",
    },
    {
      name: "Security",
      href: "/account/security",
      icon: Shield,
      description: "Password and security settings",
    },
    {
      name: "Settings",
      href: "/account/settings",
      icon: Settings,
      description: "Account preferences and settings",
    },
  ]

  const supportLinks = [
    {
      name: "Help Center",
      href: "/help",
      icon: HelpCircle,
    },
    {
      name: "Contact Support",
      href: "/contact",
      icon: HelpCircle,
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Profile Header */}
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                    <User className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      John Doe
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <nav className="p-2">
                <div className="space-y-1">
                  {navigation.map((item) => {
                    const isActive = pathname === item.href
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={cn(
                          "flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",
                          isActive
                            ? "bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500"
                            : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                        )}
                      >
                        <item.icon
                          className={cn(
                            "mr-3 h-5 w-5",
                            isActive
                              ? "text-primary-500"
                              : "text-gray-400 dark:text-gray-500"
                          )}
                        />
                        <div className="flex-1">
                          <div>{item.name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                            {item.description}
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>

                {/* Support Section */}
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <div className="space-y-1">
                    {supportLinks.map((item) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <item.icon className="mr-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        {item.name}
                      </Link>
                    ))}
                    
                    <button className="flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                      <LogOut className="mr-3 h-4 w-4" />
                      Sign Out
                    </button>
                  </div>
                </div>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
