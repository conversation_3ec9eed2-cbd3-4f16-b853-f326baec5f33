"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[327],{285:(e,r,t)=>{t.d(r,{$:()=>d});var o=t(5155),a=t(2115),n=t(9708),s=t(2085),c=t(9434);let i=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:s,asChild:d=!1,...m}=e,l=d?n.DX:"button";return(0,o.jsx)(l,{className:(0,c.cn)(i({variant:a,size:s,className:t})),ref:r,...m})});d.displayName="Button"},4817:(e,r,t)=>{t.d(r,{LanguageProvider:()=>i,o:()=>d});var o=t(5155),a=t(2115);let n={EN:{code:"en",name:"English",dir:"ltr"},AR:{code:"ar",name:"العربية",dir:"rtl"}},s=(0,a.createContext)(void 0),c={en:{"nav.home":"Home","nav.shop":"Shop","nav.services":"Services","nav.production-lines":"Production Lines","nav.blog":"Blog","nav.about":"About","nav.contact":"Contact","auth.login":"Login","auth.register":"Register","auth.logout":"Logout","common.loading":"Loading...","common.error":"Error","common.success":"Success","common.cancel":"Cancel","common.save":"Save","common.edit":"Edit","common.delete":"Delete","common.search":"Search","common.filter":"Filter","common.sort":"Sort","common.view-all":"View All","common.read-more":"Read More","common.add-to-cart":"Add to Cart","common.buy-now":"Buy Now","common.out-of-stock":"Out of Stock","common.in-stock":"In Stock","common.price":"Price","common.quantity":"Quantity","common.total":"Total","common.subtotal":"Subtotal","common.shipping":"Shipping","common.tax":"Tax","common.discount":"Discount"},ar:{"nav.home":"الرئيسية","nav.shop":"المتجر","nav.services":"الخدمات","nav.production-lines":"خطوط الإنتاج","nav.blog":"المدونة","nav.about":"من نحن","nav.contact":"اتصل بنا","auth.login":"تسجيل الدخول","auth.register":"إنشاء حساب","auth.logout":"تسجيل الخروج","common.loading":"جاري التحميل...","common.error":"خطأ","common.success":"نجح","common.cancel":"إلغاء","common.save":"حفظ","common.edit":"تعديل","common.delete":"حذف","common.search":"بحث","common.filter":"تصفية","common.sort":"ترتيب","common.view-all":"عرض الكل","common.read-more":"اقرأ المزيد","common.add-to-cart":"أضف للسلة","common.buy-now":"اشتري الآن","common.out-of-stock":"غير متوفر","common.in-stock":"متوفر","common.price":"السعر","common.quantity":"الكمية","common.total":"المجموع","common.subtotal":"المجموع الفرعي","common.shipping":"الشحن","common.tax":"الضريبة","common.discount":"الخصم"}};function i(e){let{children:r}=e,[t,i]=(0,a.useState)("en"),[d,m]=(0,a.useState)("ltr"),l=e=>{i(e),m(n[e].dir),document.documentElement.lang=e,document.documentElement.dir=n[e].dir,localStorage.setItem("language",e)};return(0,a.useEffect)(()=>{let e=localStorage.getItem("language"),r=navigator.language.startsWith("ar")?"ar":"en";l(e||r)},[]),(0,o.jsx)(s.Provider,{value:{language:t,direction:d,setLanguage:l,t:(e,r)=>{let o=c[t][e]||e;return r&&Object.entries(r).forEach(e=>{let[r,t]=e;o=o.replace("{{".concat(r,"}}"),t)}),o}},children:(0,o.jsx)("div",{dir:d,className:"rtl"===d?"font-arabic":"font-sans",children:r})})}function d(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}},4997:(e,r,t)=>{t.d(r,{O:()=>y});var o=t(5155),a=t(6874),n=t.n(a),s=t(5695),c=t(9434),i=t(4817),d=t(1007),m=t(4516),l=t(7108),u=t(1976),g=t(3861),h=t(1586),f=t(5525),x=t(381),p=t(9947),v=t(4835);function y(e){let{children:r}=e,t=(0,s.usePathname)(),{t:a}=(0,i.o)(),y=[{name:"Profile",href:"/account/profile",icon:d.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:m.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:l.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:u.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:g.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:h.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:f.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:x.A,description:"Account preferences and settings"}],b=[{name:"Help Center",href:"/help",icon:p.A},{name:"Contact Support",href:"/contact",icon:p.A}];return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,o.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,o.jsx)("div",{className:"lg:col-span-1",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,o.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,o.jsx)(d.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,o.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,o.jsxs)("nav",{className:"p-2",children:[(0,o.jsx)("div",{className:"space-y-1",children:y.map(e=>{let r=t===e.href;return(0,o.jsxs)(n(),{href:e.href,className:(0,c.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",r?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,o.jsx)(e.icon,{className:(0,c.cn)("mr-3 h-5 w-5",r?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("div",{children:e.name}),(0,o.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:e.description})]})]},e.href)})}),(0,o.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,o.jsxs)("div",{className:"space-y-1",children:[b.map(e=>(0,o.jsxs)(n(),{href:e.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,o.jsx)(e.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),e.name]},e.href)),(0,o.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,o.jsx)(v.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,o.jsx)("div",{className:"lg:col-span-3",children:(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:r})})]})})})}},6695:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>m,ZB:()=>i,Zp:()=>s,aR:()=>c,wL:()=>l});var o=t(5155),a=t(2115),n=t(9434);let s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,o.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});s.displayName="Card";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,o.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,o.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,o.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,o.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});m.displayName="CardContent";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,o.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})});l.displayName="CardFooter"},9434:(e,r,t)=>{t.d(r,{$g:()=>s,Yq:()=>c,cn:()=>n});var o=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,o.$)(r))}function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{currency:t="USD",notation:o="standard",locale:a="en-US"}=r;return new Intl.NumberFormat(a,{style:"currency",currency:t,notation:o,maximumFractionDigits:2}).format(e)}function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";return new Intl.DateTimeFormat(t,{month:"long",day:"numeric",year:"numeric",...r}).format(new Date(e))}}}]);