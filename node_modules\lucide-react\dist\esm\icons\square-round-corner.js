/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M21 11a8 8 0 0 0-8-8", key: "1lxwo5" }],
  ["path", { d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4", key: "1dv2y5" }]
];
const SquareRoundCorner = createLucideIcon("square-round-corner", __iconNode);

export { __iconNode, SquareRoundCorner as default };
//# sourceMappingURL=square-round-corner.js.map
