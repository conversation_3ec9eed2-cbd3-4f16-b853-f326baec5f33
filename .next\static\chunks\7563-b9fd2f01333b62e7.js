"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7563],{3117:(e,r,a)=>{a.d(r,{U:()=>E});var s=a(5155),n=a(2115),t=a(6874),i=a.n(t),d=a(5695),l=a(285),c=a(6126),m=a(4817),o=a(1362),h=a(3783),f=a(7108),x=a(7809),g=a(7580),u=a(8313),p=a(1746),y=a(2659),b=a(2713),v=a(381),j=a(6474),N=a(5525),w=a(4416),k=a(1007),A=a(2098),C=a(3509),R=a(4869),z=a(4835),S=a(4783),P=a(7924),$=a(3861);let q=[{name:"Dashboard",href:"/admin",icon:h.A,current:!1},{name:"Products",href:"/admin/products",icon:f.A,current:!1,children:[{name:"All Products",href:"/admin/products"},{name:"Categories",href:"/admin/products/categories"},{name:"Inventory",href:"/admin/products/inventory"}]},{name:"Orders",href:"/admin/orders",icon:x.A,current:!1,badge:"12",children:[{name:"All Orders",href:"/admin/orders"},{name:"Pending",href:"/admin/orders/pending"},{name:"Shipped",href:"/admin/orders/shipped"},{name:"Returns",href:"/admin/orders/returns"}]},{name:"Users",href:"/admin/users",icon:g.A,current:!1,children:[{name:"All Users",href:"/admin/users"},{name:"Customers",href:"/admin/users/customers"},{name:"Admins",href:"/admin/users/admins"},{name:"Roles",href:"/admin/users/roles"}]},{name:"Services",href:"/admin/services",icon:u.A,current:!1,children:[{name:"All Services",href:"/admin/services"},{name:"Categories",href:"/admin/services/categories"},{name:"Inquiries",href:"/admin/services/inquiries"}]},{name:"Production Lines",href:"/admin/production-lines",icon:p.A,current:!1,children:[{name:"All Lines",href:"/admin/production-lines"},{name:"Categories",href:"/admin/production-lines/categories"},{name:"Inquiries",href:"/admin/production-lines/inquiries"}]},{name:"Blog",href:"/admin/blog",icon:y.A,current:!1,children:[{name:"All Posts",href:"/admin/blog"},{name:"Categories",href:"/admin/blog/categories"},{name:"Comments",href:"/admin/blog/comments"},{name:"Authors",href:"/admin/blog/authors"}]},{name:"Analytics",href:"/admin/analytics",icon:b.A,current:!1,children:[{name:"Overview",href:"/admin/analytics"},{name:"Sales",href:"/admin/analytics/sales"},{name:"Traffic",href:"/admin/analytics/traffic"},{name:"Reports",href:"/admin/analytics/reports"}]},{name:"Settings",href:"/admin/settings",icon:v.A,current:!1,children:[{name:"General",href:"/admin/settings"},{name:"Payments",href:"/admin/settings/payments"},{name:"Shipping",href:"/admin/settings/shipping"},{name:"Notifications",href:"/admin/settings/notifications"}]}];function E(e){let{children:r}=e,[a,t]=(0,n.useState)(!1),[h,x]=(0,n.useState)([]),g=(0,d.usePathname)(),{theme:u,setTheme:p}=(0,o.D)(),{language:y,setLanguage:b,t:v}=(0,m.o)(),E=e=>g===e||g.startsWith(e+"/"),U=e=>{var r;let{item:a}=e,n=a.children&&a.children.length>0,t=h.includes(a.href),d=E(a.href);return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"group flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors cursor-pointer ".concat(d?"bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"),onClick:()=>{var e;return n?(e=a.href,void x(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])):null},children:[(0,s.jsxs)(i(),{href:n?"#":a.href,className:"flex items-center flex-1",children:[(0,s.jsx)(a.icon,{className:"mr-3 h-5 w-5 flex-shrink-0"}),(0,s.jsx)("span",{children:a.name}),a.badge&&(0,s.jsx)(c.E,{variant:"secondary",className:"ml-auto text-xs",children:a.badge})]}),n&&(0,s.jsx)(j.A,{className:"h-4 w-4 transition-transform ".concat(t?"rotate-180":"")})]}),n&&t&&(0,s.jsx)("div",{className:"ml-8 mt-1 space-y-1",children:null==(r=a.children)?void 0:r.map(e=>(0,s.jsx)(i(),{href:e.href,className:"block px-3 py-2 text-sm rounded-lg transition-colors ".concat(E(e.href)?"bg-primary-50 dark:bg-primary-950 text-primary-600 dark:text-primary-400":"text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800"),children:e.name},e.href))})]})};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[a&&(0,s.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 lg:hidden",onClick:()=>t(!1)}),(0,s.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform lg:translate-x-0 ".concat(a?"translate-x-0":"-translate-x-full"," lg:static lg:inset-0"),children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)(i(),{href:"/admin",className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(N.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Admin"})]}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>t(!1),children:(0,s.jsx)(w.A,{className:"h-5 w-5"})})]}),(0,s.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2 overflow-y-auto",children:q.map(e=>(0,s.jsx)(U,{item:e},e.href))}),(0,s.jsxs)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center",children:(0,s.jsx)(k.A,{className:"h-4 w-4 text-gray-500"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:"Admin User"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:"<EMAIL>"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>p("dark"===u?"light":"dark"),className:"flex-1",children:"dark"===u?(0,s.jsx)(A.A,{className:"h-4 w-4"}):(0,s.jsx)(C.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>b("en"===y?"ar":"en"),className:"flex-1",children:(0,s.jsx)(R.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"flex-1",children:(0,s.jsx)(z.A,{className:"h-4 w-4"})})]})]})]})}),(0,s.jsxs)("div",{className:"lg:ml-64",children:[(0,s.jsx)("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-full px-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>t(!0),children:(0,s.jsx)(S.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"relative hidden md:block",children:[(0,s.jsx)(P.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search...",className:"pl-10 pr-4 py-2 w-64 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(l.$,{variant:"ghost",size:"sm",className:"relative",children:[(0,s.jsx)($.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"3"})]}),(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-2",children:(0,s.jsx)(i(),{href:"/admin/products/new",children:(0,s.jsxs)(l.$,{size:"sm",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})})})]})]})}),(0,s.jsx)("main",{className:"p-6",children:r})]})]})}},6126:(e,r,a)=>{a.d(r,{E:()=>d});var s=a(5155);a(2115);var n=a(2085),t=a(9434);let i=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:a,...n}=e;return(0,s.jsx)("div",{className:(0,t.cn)(i({variant:a}),r),...n})}},6695:(e,r,a)=>{a.d(r,{BT:()=>c,Wu:()=>m,ZB:()=>l,Zp:()=>i,aR:()=>d,wL:()=>o});var s=a(5155),n=a(2115),t=a(9434);let i=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,t.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...n})});i.displayName="Card";let d=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",a),...n})});d.displayName="CardHeader";let l=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,s.jsx)("h3",{ref:r,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",a),...n})});l.displayName="CardTitle";let c=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,s.jsx)("p",{ref:r,className:(0,t.cn)("text-sm text-muted-foreground",a),...n})});c.displayName="CardDescription";let m=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,t.cn)("p-6 pt-0",a),...n})});m.displayName="CardContent";let o=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,t.cn)("flex items-center p-6 pt-0",a),...n})});o.displayName="CardFooter"}}]);