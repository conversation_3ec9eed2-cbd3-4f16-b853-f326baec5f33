"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Star,
  Clock,
  Users,
  CheckCircle,
  ArrowRight,
  Wrench,
  Code,
  Palette,
  BarChart3,
  Shield,
  Zap,
} from "lucide-react"

// Mock services data
const mockServices = [
  {
    id: "1",
    title: "Web Development",
    slug: "web-development",
    description: "Custom web applications built with modern technologies and best practices.",
    shortDescription: "Modern web applications with cutting-edge technology",
    price: 2999,
    duration: "4-8 weeks",
    category: "Development",
    rating: 4.9,
    reviewCount: 45,
    image: "/service-web-dev.jpg",
    featured: true,
    popular: true,
    tags: ["React", "Next.js", "TypeScript", "Node.js"],
    features: [
      "Responsive Design",
      "SEO Optimization",
      "Performance Optimization",
      "Security Implementation",
      "API Integration",
      "Database Design"
    ],
    deliverables: [
      "Complete Web Application",
      "Source Code",
      "Documentation",
      "Deployment Guide",
      "3 Months Support"
    ]
  },
  {
    id: "2",
    title: "Mobile App Development",
    slug: "mobile-app-development",
    description: "Native and cross-platform mobile applications for iOS and Android.",
    shortDescription: "Native and cross-platform mobile solutions",
    price: 4999,
    duration: "6-12 weeks",
    category: "Development",
    rating: 4.8,
    reviewCount: 32,
    image: "/service-mobile-dev.jpg",
    featured: true,
    popular: false,
    tags: ["React Native", "Flutter", "iOS", "Android"],
    features: [
      "Cross-platform Development",
      "Native Performance",
      "App Store Optimization",
      "Push Notifications",
      "Offline Functionality",
      "Analytics Integration"
    ],
    deliverables: [
      "Mobile Application",
      "App Store Submission",
      "Source Code",
      "User Manual",
      "6 Months Support"
    ]
  },
  {
    id: "3",
    title: "UI/UX Design",
    slug: "ui-ux-design",
    description: "User-centered design solutions that enhance user experience and drive engagement.",
    shortDescription: "User-centered design for better engagement",
    price: 1999,
    duration: "2-4 weeks",
    category: "Design",
    rating: 4.9,
    reviewCount: 67,
    image: "/service-design.jpg",
    featured: false,
    popular: true,
    tags: ["Figma", "Adobe XD", "Prototyping", "User Research"],
    features: [
      "User Research",
      "Wireframing",
      "Prototyping",
      "Visual Design",
      "Usability Testing",
      "Design System"
    ],
    deliverables: [
      "Design Mockups",
      "Interactive Prototypes",
      "Design System",
      "Style Guide",
      "Asset Files"
    ]
  },
  {
    id: "4",
    title: "Digital Marketing",
    slug: "digital-marketing",
    description: "Comprehensive digital marketing strategies to grow your online presence.",
    shortDescription: "Comprehensive strategies for online growth",
    price: 1499,
    duration: "Ongoing",
    category: "Marketing",
    rating: 4.7,
    reviewCount: 89,
    image: "/service-marketing.jpg",
    featured: false,
    popular: false,
    tags: ["SEO", "SEM", "Social Media", "Content Marketing"],
    features: [
      "SEO Optimization",
      "Social Media Management",
      "Content Creation",
      "PPC Campaigns",
      "Analytics & Reporting",
      "Brand Strategy"
    ],
    deliverables: [
      "Marketing Strategy",
      "Content Calendar",
      "Campaign Setup",
      "Monthly Reports",
      "Ongoing Support"
    ]
  },
  {
    id: "5",
    title: "Cloud Solutions",
    slug: "cloud-solutions",
    description: "Scalable cloud infrastructure and migration services for modern businesses.",
    shortDescription: "Scalable cloud infrastructure solutions",
    price: 3499,
    duration: "3-6 weeks",
    category: "Infrastructure",
    rating: 4.8,
    reviewCount: 23,
    image: "/service-cloud.jpg",
    featured: true,
    popular: false,
    tags: ["AWS", "Azure", "Docker", "Kubernetes"],
    features: [
      "Cloud Migration",
      "Infrastructure Setup",
      "Auto Scaling",
      "Security Configuration",
      "Monitoring & Alerts",
      "Backup Solutions"
    ],
    deliverables: [
      "Cloud Infrastructure",
      "Migration Plan",
      "Security Setup",
      "Monitoring Dashboard",
      "Documentation"
    ]
  },
  {
    id: "6",
    title: "Data Analytics",
    slug: "data-analytics",
    description: "Transform your data into actionable insights with advanced analytics solutions.",
    shortDescription: "Transform data into actionable insights",
    price: 2499,
    duration: "4-6 weeks",
    category: "Analytics",
    rating: 4.6,
    reviewCount: 34,
    image: "/service-analytics.jpg",
    featured: false,
    popular: true,
    tags: ["Python", "R", "Tableau", "Power BI"],
    features: [
      "Data Collection",
      "Data Processing",
      "Statistical Analysis",
      "Visualization",
      "Predictive Modeling",
      "Reporting Automation"
    ],
    deliverables: [
      "Analytics Dashboard",
      "Data Models",
      "Reports & Insights",
      "Training Materials",
      "Ongoing Support"
    ]
  }
]

const categories = [
  "All Services",
  "Development",
  "Design",
  "Marketing",
  "Infrastructure",
  "Analytics",
]

const sortOptions = [
  { value: "featured", label: "Featured" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "rating", label: "Highest Rated" },
  { value: "popular", label: "Most Popular" },
]

export default function ServicesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Services")
  const [sortBy, setSortBy] = useState("featured")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  
  const { t } = useLanguage()
  const { toast } = useToast()

  const handleContactService = (service: typeof mockServices[0]) => {
    toast({
      title: "Contact Request Sent",
      description: `We'll get back to you about ${service.title} within 24 hours`,
    })
  }

  const filteredServices = mockServices.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All Services" || service.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const sortedServices = filteredServices.sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price
      case "price-high":
        return b.price - a.price
      case "rating":
        return b.rating - a.rating
      case "popular":
        return (b.popular ? 1 : 0) - (a.popular ? 1 : 0)
      case "featured":
      default:
        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0)
    }
  })

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Development": return Code
      case "Design": return Palette
      case "Marketing": return BarChart3
      case "Infrastructure": return Shield
      case "Analytics": return BarChart3
      default: return Wrench
    }
  }

  const ServiceCard = ({ service }: { service: typeof mockServices[0] }) => {
    const CategoryIcon = getCategoryIcon(service.category)
    
    return (
      <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
        <div className="relative">
          {/* Service Image */}
          <div className="aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900 dark:to-secondary-900 flex items-center justify-center">
            <CategoryIcon className="h-16 w-16 text-primary-600 dark:text-primary-400" />
          </div>
          
          {/* Badges */}
          <div className="absolute top-3 left-3 space-y-1">
            {service.featured && (
              <Badge className="bg-primary-500 text-white">Featured</Badge>
            )}
            {service.popular && (
              <Badge className="bg-orange-500 text-white">Popular</Badge>
            )}
          </div>
        </div>

        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Header */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline" className="text-xs">
                  {service.category}
                </Badge>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium">{service.rating}</span>
                  <span className="text-sm text-gray-500">({service.reviewCount})</span>
                </div>
              </div>
              
              <h3 className="text-xl font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                <Link href={`/services/${service.slug}`}>
                  {service.title}
                </Link>
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2">
                {service.shortDescription}
              </p>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-1">
              {service.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {service.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{service.tags.length - 3}
                </Badge>
              )}
            </div>

            {/* Details */}
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{service.duration}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{service.reviewCount} clients</span>
              </div>
            </div>

            {/* Price and CTA */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div>
                <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {formatPrice(service.price)}
                </span>
                <span className="text-sm text-gray-500 ml-1">starting</span>
              </div>
              
              <div className="flex space-x-2">
                <Link href={`/services/${service.slug}`}>
                  <Button variant="outline" size="sm">
                    Learn More
                  </Button>
                </Link>
                <Button 
                  size="sm"
                  onClick={() => handleContactService(service)}
                >
                  Get Quote
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <Wrench className="h-4 w-4 mr-2" />
            Professional Services
          </Badge>
          <h1 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Expert Services
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Professional services designed to accelerate your business growth with cutting-edge solutions and expert guidance.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* Filter Bar */}
          <div className="flex flex-wrap items-center justify-center gap-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              {sortedServices.length} services found
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className={`grid gap-8 ${
          viewMode === "grid" 
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
            : "grid-cols-1"
        }`}>
          {sortedServices.map((service) => (
            <ServiceCard key={service.id} service={service} />
          ))}
        </div>

        {/* Empty State */}
        {sortedServices.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No services found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search or filter criteria
            </p>
            <Button onClick={() => {
              setSearchQuery("")
              setSelectedCategory("All Services")
            }}>
              Clear Filters
            </Button>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16 text-center bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8">
          <h3 className="text-2xl font-bold mb-4">
            Need a Custom Solution?
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
            Can't find exactly what you're looking for? We specialize in creating custom solutions tailored to your unique business requirements.
          </p>
          <Button size="lg" className="group">
            <Zap className="h-5 w-5 mr-2" />
            Contact Our Experts
            <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </div>
    </div>
  )
}
