"use client"

import { useState } from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  ArrowLeft,
  Star,
  Clock,
  Users,
  CheckCircle,
  MessageCircle,
  Calendar,
  Download,
  Share2,
  Code,
  Palette,
  BarChart3,
  Shield,
  Wrench,
  Phone,
  Mail,
  MapPin,
} from "lucide-react"

// Mock service data (in a real app, this would be fetched based on slug)
const mockService = {
  id: "1",
  title: "Web Development",
  slug: "web-development",
  description: "Custom web applications built with modern technologies and best practices. We create scalable, secure, and high-performance web solutions that drive business growth and enhance user experience.",
  shortDescription: "Modern web applications with cutting-edge technology",
  price: 2999,
  duration: "4-8 weeks",
  category: "Development",
  rating: 4.9,
  reviewCount: 45,
  image: "/service-web-dev.jpg",
  featured: true,
  popular: true,
  tags: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
  features: [
    "Responsive Design",
    "SEO Optimization", 
    "Performance Optimization",
    "Security Implementation",
    "API Integration",
    "Database Design",
    "Testing & QA",
    "Deployment & DevOps"
  ],
  deliverables: [
    "Complete Web Application",
    "Source Code Repository",
    "Technical Documentation",
    "Deployment Guide",
    "User Manual",
    "3 Months Support",
    "Training Session",
    "Performance Report"
  ],
  process: [
    {
      step: 1,
      title: "Discovery & Planning",
      description: "We analyze your requirements and create a detailed project plan.",
      duration: "1 week"
    },
    {
      step: 2,
      title: "Design & Prototyping",
      description: "Create wireframes, mockups, and interactive prototypes.",
      duration: "1-2 weeks"
    },
    {
      step: 3,
      title: "Development",
      description: "Build the application using modern technologies and best practices.",
      duration: "2-4 weeks"
    },
    {
      step: 4,
      title: "Testing & Deployment",
      description: "Comprehensive testing and deployment to production environment.",
      duration: "1 week"
    }
  ],
  technologies: [
    { name: "React", description: "Modern UI library for building interactive interfaces" },
    { name: "Next.js", description: "Full-stack React framework with SSR and SSG" },
    { name: "TypeScript", description: "Type-safe JavaScript for better code quality" },
    { name: "Node.js", description: "Server-side JavaScript runtime" },
    { name: "PostgreSQL", description: "Robust relational database system" },
    { name: "AWS", description: "Cloud infrastructure and services" }
  ],
  faqs: [
    {
      question: "What's included in the web development service?",
      answer: "Our web development service includes complete application development, responsive design, SEO optimization, security implementation, testing, deployment, and 3 months of support."
    },
    {
      question: "How long does the development process take?",
      answer: "Typically 4-8 weeks depending on the complexity and scope of your project. We'll provide a detailed timeline during the planning phase."
    },
    {
      question: "Do you provide ongoing maintenance?",
      answer: "Yes, we offer various maintenance packages including bug fixes, security updates, feature enhancements, and technical support."
    },
    {
      question: "Can you work with our existing systems?",
      answer: "Absolutely! We specialize in integrating with existing systems and can work with your current infrastructure and third-party services."
    }
  ]
}

const mockTestimonials = [
  {
    id: "1",
    name: "Sarah Johnson",
    company: "TechStart Inc.",
    role: "CEO",
    content: "Exceptional work! The team delivered a high-quality web application that exceeded our expectations. The attention to detail and technical expertise was outstanding.",
    rating: 5,
    image: "/testimonial-1.jpg"
  },
  {
    id: "2", 
    name: "Michael Chen",
    company: "Digital Solutions",
    role: "CTO",
    content: "Professional service from start to finish. They understood our requirements perfectly and delivered a scalable solution on time and within budget.",
    rating: 5,
    image: "/testimonial-2.jpg"
  }
]

interface ServicePageProps {
  params: {
    slug: string
  }
}

export default function ServicePage({ params }: ServicePageProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const { t } = useLanguage()
  const { toast } = useToast()

  // In a real app, you would fetch the service based on the slug
  const service = mockService
  
  if (!service) {
    notFound()
  }

  const handleContactService = () => {
    toast({
      title: "Contact Request Sent",
      description: "We'll get back to you within 24 hours to discuss your project",
    })
  }

  const handleDownloadBrochure = () => {
    toast({
      title: "Download Started",
      description: "Service brochure is being downloaded",
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: service.title,
        text: service.description,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link Copied",
        description: "Service link copied to clipboard",
      })
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Development": return Code
      case "Design": return Palette
      case "Marketing": return BarChart3
      case "Infrastructure": return Shield
      default: return Wrench
    }
  }

  const CategoryIcon = getCategoryIcon(service.category)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-8">
          <Link href="/" className="hover:text-foreground">Home</Link>
          <span>/</span>
          <Link href="/services" className="hover:text-foreground">Services</Link>
          <span>/</span>
          <span className="text-foreground">{service.title}</span>
        </nav>

        {/* Back Button */}
        <Link href="/services" className="inline-flex items-center text-sm text-gray-500 hover:text-foreground mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Services
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Service Header */}
            <div className="space-y-6">
              {/* Service Image */}
              <div className="aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900 dark:to-secondary-900 rounded-xl flex items-center justify-center">
                <CategoryIcon className="h-24 w-24 text-primary-600 dark:text-primary-400" />
              </div>

              {/* Service Info */}
              <div>
                <div className="flex items-center space-x-4 mb-4">
                  <Badge variant="outline">{service.category}</Badge>
                  {service.featured && <Badge className="bg-primary-500">Featured</Badge>}
                  {service.popular && <Badge className="bg-orange-500">Popular</Badge>}
                </div>
                
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  {service.title}
                </h1>

                <div className="flex items-center space-x-6 mb-6">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${
                            i < Math.floor(service.rating)
                              ? "text-yellow-400 fill-current"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="font-medium">{service.rating}</span>
                    <span className="text-gray-500">({service.reviewCount} reviews)</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-gray-500">
                    <Clock className="h-4 w-4" />
                    <span>{service.duration}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-gray-500">
                    <Users className="h-4 w-4" />
                    <span>{service.reviewCount} clients</span>
                  </div>
                </div>

                <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed">
                  {service.description}
                </p>
              </div>
            </div>

            {/* Service Details Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="process">Process</TabsTrigger>
                <TabsTrigger value="technologies">Technologies</TabsTrigger>
                <TabsTrigger value="faq">FAQ</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Features */}
                  <Card>
                    <CardHeader>
                      <CardTitle>What's Included</CardTitle>
                      <CardDescription>
                        Key features and capabilities of this service
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {service.features.map((feature, index) => (
                          <li key={index} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>

                  {/* Deliverables */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Deliverables</CardTitle>
                      <CardDescription>
                        What you'll receive upon project completion
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {service.deliverables.map((deliverable, index) => (
                          <li key={index} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                            <span>{deliverable}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="process" className="mt-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Our Process</CardTitle>
                    <CardDescription>
                      Step-by-step approach to delivering your project
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {service.process.map((step, index) => (
                        <div key={index} className="flex items-start space-x-4">
                          <div className="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                            <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">
                              {step.step}
                            </span>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold">{step.title}</h4>
                              <Badge variant="outline" className="text-xs">
                                {step.duration}
                              </Badge>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400">
                              {step.description}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="technologies" className="mt-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Technologies We Use</CardTitle>
                    <CardDescription>
                      Modern tools and frameworks for optimal results
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {service.technologies.map((tech, index) => (
                        <div key={index} className="p-4 border rounded-lg">
                          <h4 className="font-semibold mb-2">{tech.name}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {tech.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="faq" className="mt-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Frequently Asked Questions</CardTitle>
                    <CardDescription>
                      Common questions about this service
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {service.faqs.map((faq, index) => (
                        <div key={index}>
                          <h4 className="font-semibold mb-2">{faq.question}</h4>
                          <p className="text-gray-600 dark:text-gray-400">
                            {faq.answer}
                          </p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Testimonials */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Client Testimonials</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockTestimonials.map((testimonial) => (
                  <Card key={testimonial.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-1 mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < testimonial.rating
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        "{testimonial.content}"
                      </p>
                      <div>
                        <div className="font-semibold">{testimonial.name}</div>
                        <div className="text-sm text-gray-500">
                          {testimonial.role} at {testimonial.company}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Pricing Card */}
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Get Started</CardTitle>
                <CardDescription>
                  Ready to begin your project?
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Price */}
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                    {formatPrice(service.price)}
                  </div>
                  <div className="text-sm text-gray-500">starting price</div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    size="lg"
                    onClick={handleContactService}
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Get Free Quote
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleDownloadBrochure}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Brochure
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleShare}
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Service
                  </Button>
                </div>

                {/* Contact Info */}
                <div className="pt-6 border-t space-y-3">
                  <div className="flex items-center space-x-3 text-sm">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>+****************</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>Available Worldwide</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle>Technologies</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {service.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
