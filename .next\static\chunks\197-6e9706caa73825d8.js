"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[197],{285:(o,t,e)=>{e.d(t,{$:()=>m});var n=e(5155),r=e(2115),a=e(9708),c=e(2085),s=e(9434);let i=(0,c.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=r.forwardRef((o,t)=>{let{className:e,variant:r,size:c,asChild:m=!1,...u}=o,d=m?a.DX:"button";return(0,n.jsx)(d,{className:(0,s.cn)(i({variant:r,size:c,className:e})),ref:t,...u})});m.displayName="Button"},4817:(o,t,e)=>{e.d(t,{LanguageProvider:()=>i,o:()=>m});var n=e(5155),r=e(2115);let a={EN:{code:"en",name:"English",dir:"ltr"},AR:{code:"ar",name:"العربية",dir:"rtl"}},c=(0,r.createContext)(void 0),s={en:{"nav.home":"Home","nav.shop":"Shop","nav.services":"Services","nav.production-lines":"Production Lines","nav.blog":"Blog","nav.about":"About","nav.contact":"Contact","auth.login":"Login","auth.register":"Register","auth.logout":"Logout","common.loading":"Loading...","common.error":"Error","common.success":"Success","common.cancel":"Cancel","common.save":"Save","common.edit":"Edit","common.delete":"Delete","common.search":"Search","common.filter":"Filter","common.sort":"Sort","common.view-all":"View All","common.read-more":"Read More","common.add-to-cart":"Add to Cart","common.buy-now":"Buy Now","common.out-of-stock":"Out of Stock","common.in-stock":"In Stock","common.price":"Price","common.quantity":"Quantity","common.total":"Total","common.subtotal":"Subtotal","common.shipping":"Shipping","common.tax":"Tax","common.discount":"Discount"},ar:{"nav.home":"الرئيسية","nav.shop":"المتجر","nav.services":"الخدمات","nav.production-lines":"خطوط الإنتاج","nav.blog":"المدونة","nav.about":"من نحن","nav.contact":"اتصل بنا","auth.login":"تسجيل الدخول","auth.register":"إنشاء حساب","auth.logout":"تسجيل الخروج","common.loading":"جاري التحميل...","common.error":"خطأ","common.success":"نجح","common.cancel":"إلغاء","common.save":"حفظ","common.edit":"تعديل","common.delete":"حذف","common.search":"بحث","common.filter":"تصفية","common.sort":"ترتيب","common.view-all":"عرض الكل","common.read-more":"اقرأ المزيد","common.add-to-cart":"أضف للسلة","common.buy-now":"اشتري الآن","common.out-of-stock":"غير متوفر","common.in-stock":"متوفر","common.price":"السعر","common.quantity":"الكمية","common.total":"المجموع","common.subtotal":"المجموع الفرعي","common.shipping":"الشحن","common.tax":"الضريبة","common.discount":"الخصم"}};function i(o){let{children:t}=o,[e,i]=(0,r.useState)("en"),[m,u]=(0,r.useState)("ltr"),d=o=>{i(o),u(a[o].dir),document.documentElement.lang=o,document.documentElement.dir=a[o].dir,localStorage.setItem("language",o)};return(0,r.useEffect)(()=>{let o=localStorage.getItem("language"),t=navigator.language.startsWith("ar")?"ar":"en";d(o||t)},[]),(0,n.jsx)(c.Provider,{value:{language:e,direction:m,setLanguage:d,t:(o,t)=>{let n=s[e][o]||o;return t&&Object.entries(t).forEach(o=>{let[t,e]=o;n=n.replace("{{".concat(t,"}}"),e)}),n}},children:(0,n.jsx)("div",{dir:m,className:"rtl"===m?"font-arabic":"font-sans",children:t})})}function m(){let o=(0,r.useContext)(c);if(void 0===o)throw Error("useLanguage must be used within a LanguageProvider");return o}},7481:(o,t,e)=>{e.d(t,{dj:()=>d});var n=e(2115);let r=0,a=new Map,c=o=>{if(a.has(o))return;let t=setTimeout(()=>{a.delete(o),m({type:"REMOVE_TOAST",toastId:o})},1e6);a.set(o,t)},s=[],i={toasts:[]};function m(o){i=((o,t)=>{switch(t.type){case"ADD_TOAST":return{...o,toasts:[t.toast,...o.toasts].slice(0,1)};case"UPDATE_TOAST":return{...o,toasts:o.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case"DISMISS_TOAST":{let{toastId:e}=t;return e?c(e):o.toasts.forEach(o=>{c(o.id)}),{...o,toasts:o.toasts.map(o=>o.id===e||void 0===e?{...o,open:!1}:o)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...o,toasts:[]};return{...o,toasts:o.toasts.filter(o=>o.id!==t.toastId)}}})(i,o),s.forEach(o=>{o(i)})}function u(o){let{...t}=o,e=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>m({type:"DISMISS_TOAST",toastId:e});return m({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:o=>{o||n()}}}),{id:e,dismiss:n,update:o=>m({type:"UPDATE_TOAST",toast:{...o,id:e}})}}function d(){let[o,t]=n.useState(i);return n.useEffect(()=>(s.push(t),()=>{let o=s.indexOf(t);o>-1&&s.splice(o,1)}),[o]),{...o,toast:u,dismiss:o=>m({type:"DISMISS_TOAST",toastId:o})}}},9434:(o,t,e)=>{e.d(t,{cn:()=>a});var n=e(2596),r=e(9688);function a(){for(var o=arguments.length,t=Array(o),e=0;e<o;e++)t[e]=arguments[e];return(0,r.QP)((0,n.$)(t))}}}]);