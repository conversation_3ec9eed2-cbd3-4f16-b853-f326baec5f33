(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{2523:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var a=s(5155),t=s(2115),l=s(9434);let i=t.forwardRef((e,r)=>{let{className:s,type:t,...i}=e;return(0,a.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...i})});i.displayName="Input"},2722:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var a=s(5155),t=s(2115),l=s(6874),i=s.n(l),n=s(2177),d=s(221),c=s(8309),o=s(285),m=s(2523),u=s(5057),f=s(6695),x=s(7481),p=s(4817);let h=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var y=s(5169),N=s(8883);let g=c.Ik({email:c.Yj().email("Invalid email address")});function j(){let[e,r]=(0,t.useState)(!1),[s,l]=(0,t.useState)(!1),{toast:c}=(0,x.dj)(),{t:j}=(0,p.o)(),{register:v,handleSubmit:w,formState:{errors:b},getValues:k}=(0,n.mN)({resolver:(0,d.u)(g)}),C=async e=>{r(!0);try{let r=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await r.json();s.success?(l(!0),c({title:j("common.success"),description:"Password reset instructions sent to your email"})):c({title:j("common.error"),description:s.error||"Failed to send reset email",variant:"destructive"})}catch(e){c({title:j("common.error"),description:"An error occurred while sending reset email",variant:"destructive"})}finally{r(!1)}};return s?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsxs)(f.Zp,{className:"w-full",children:[(0,a.jsxs)(f.aR,{className:"space-y-1 text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900",children:(0,a.jsx)(h,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),(0,a.jsx)(f.ZB,{className:"text-2xl",children:"Check your email"}),(0,a.jsxs)(f.BT,{children:["We've sent password reset instructions to"," ",(0,a.jsx)("span",{className:"font-medium",children:k("email")})]})]}),(0,a.jsx)(f.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,a.jsx)("p",{children:"Didn't receive the email? Check your spam folder or"}),(0,a.jsx)("button",{onClick:()=>l(!1),className:"text-primary hover:underline",children:"try again"})]})}),(0,a.jsx)(f.wL,{children:(0,a.jsx)(i(),{href:"/auth/login",className:"w-full",children:(0,a.jsx)(o.$,{variant:"outline",className:"w-full",children:"Back to login"})})})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(i(),{href:"/auth/login",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Back to login"]})}),(0,a.jsxs)(f.Zp,{className:"w-full",children:[(0,a.jsxs)(f.aR,{className:"space-y-1",children:[(0,a.jsx)(f.ZB,{className:"text-2xl text-center",children:"Forgot password?"}),(0,a.jsx)(f.BT,{className:"text-center",children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,a.jsx)(f.Wu,{className:"space-y-4",children:(0,a.jsxs)("form",{onSubmit:w(C),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(m.p,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...v("email")})]}),b.email&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:b.email.message})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full",disabled:e,children:e?"Sending...":"Send reset instructions"})]})}),(0,a.jsx)(f.wL,{children:(0,a.jsxs)("p",{className:"text-center text-sm text-muted-foreground w-full",children:["Remember your password?"," ",(0,a.jsx)(i(),{href:"/auth/login",className:"text-primary hover:underline",children:j("auth.login")})]})})]})]})})}},5057:(e,r,s)=>{"use strict";s.d(r,{J:()=>c});var a=s(5155),t=s(2115),l=s(968),i=s(2085),n=s(9434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.b,{ref:r,className:(0,n.cn)(d(),s),...t})});c.displayName=l.b.displayName},6350:(e,r,s)=>{Promise.resolve().then(s.bind(s,2722))},6695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>m});var a=s(5155),t=s(2115),l=s(9434);let i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...t})});i.displayName="Card";let n=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...t})});n.displayName="CardHeader";let d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...t})});d.displayName="CardTitle";let c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",s),...t})});c.displayName="CardDescription";let o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",s),...t})});o.displayName="CardContent";let m=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",s),...t})});m.displayName="CardFooter"}},e=>{e.O(0,[455,56,110,197,441,964,358],()=>e(e.s=6350)),_N_E=e.O()}]);