(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[298],{381:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1586:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1976:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},3861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4098:(e,s,t)=>{Promise.resolve().then(t.bind(t,5402))},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4835:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5402:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(5155),r=t(6874),l=t.n(r),c=t(4997),i=t(285),d=t(6695),n=t(4817),x=t(9434),h=t(7108),m=t(9946);let o=(0,m.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var y=t(1976),j=t(8564);let u=(0,m.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var p=t(646),g=t(4186),N=t(6151),f=t(4516),v=t(1586),k=t(3861);let w=(0,m.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),A={totalOrders:12,totalSpent:1299.99,wishlistItems:8,savedAddresses:3,loyaltyPoints:2450,memberSince:new Date("2023-06-15")},b=[{id:"ORD-2024-001",orderNumber:"ORD-2024-001",status:"delivered",total:299.99,currency:"USD",createdAt:new Date("2024-01-15"),itemCount:3},{id:"ORD-2024-002",orderNumber:"ORD-2024-002",status:"shipped",total:89.99,currency:"USD",createdAt:new Date("2024-01-20"),itemCount:2}],M=[{id:"1",name:"Premium Wireless Headphones",price:199.99,currency:"USD",image:"/placeholder-product.jpg",inStock:!0},{id:"2",name:"Smart Fitness Watch",price:299.99,currency:"USD",image:"/placeholder-product.jpg",inStock:!1}],S=[{id:"1",type:"order",title:"Order Delivered",message:"Your order #ORD-2024-001 has been delivered",createdAt:new Date("2024-01-18"),read:!1},{id:"2",type:"promotion",title:"Special Offer",message:"Get 20% off on your next purchase",createdAt:new Date("2024-01-17"),read:!0}];function P(){let{t:e}=(0,n.o)();return(0,a.jsx)(c.O,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Welcome back, John!"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Here's what's happening with your account"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Orders"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:A.totalOrders})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-primary-500"})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Spent"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(0,x.$g)(A.totalSpent)})]}),(0,a.jsx)(o,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Wishlist Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:A.wishlistItems})]}),(0,a.jsx)(y.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Loyalty Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:A.loyaltyPoints.toLocaleString()})]}),(0,a.jsx)(j.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(d.ZB,{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Recent Orders"]}),(0,a.jsx)(l(),{href:"/account/orders",children:(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",children:["View All",(0,a.jsx)(u,{className:"h-4 w-4 ml-1"})]})})]}),(0,a.jsx)(d.BT,{children:"Your latest order activity"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[b.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center",children:"delivered"===e.status?(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-500"}):(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-sm",children:["#",e.orderNumber]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.itemCount," items • ",(0,x.Yq)(e.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-medium",children:(0,x.$g)(e.total,{currency:e.currency})}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 capitalize",children:e.status})]})]},e.id)),0===b.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(N.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No orders yet"}),(0,a.jsx)(l(),{href:"/shop",children:(0,a.jsx)(i.$,{className:"mt-2",children:"Start Shopping"})})]})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(d.ZB,{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Wishlist"]}),(0,a.jsx)(l(),{href:"/account/wishlist",children:(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",children:["View All",(0,a.jsx)(u,{className:"h-4 w-4 ml-1"})]})})]}),(0,a.jsx)(d.BT,{children:"Items you've saved for later"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[M.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(0,x.$g)(e.price,{currency:e.currency})})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:e.inStock?(0,a.jsx)(i.$,{size:"sm",children:"Add to Cart"}):(0,a.jsx)(i.$,{size:"sm",variant:"outline",disabled:!0,children:"Out of Stock"})})]},e.id)),0===M.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No wishlist items yet"}),(0,a.jsx)(l(),{href:"/shop",children:(0,a.jsx)(i.$,{className:"mt-2",children:"Browse Products"})})]})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"Quick Actions"}),(0,a.jsx)(d.BT,{children:"Manage your account settings"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(l(),{href:"/account/profile",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})}),(0,a.jsx)(l(),{href:"/account/addresses",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Addresses"]})}),(0,a.jsx)(l(),{href:"/account/payment-methods",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Payment"]})}),(0,a.jsx)(l(),{href:"/account/security",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Security"]})})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(d.ZB,{className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 mr-2"}),"Recent Notifications"]}),(0,a.jsx)(l(),{href:"/account/notifications",children:(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",children:["View All",(0,a.jsx)(u,{className:"h-4 w-4 ml-1"})]})})]}),(0,a.jsx)(d.BT,{children:"Latest updates and alerts"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[S.map(e=>(0,a.jsx)("div",{className:"p-4 border rounded-lg ".concat(e.read?"":"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-2",children:(0,x.Yq)(e.createdAt)})]}),!e.read&&(0,a.jsx)("div",{className:"h-2 w-2 bg-blue-500 rounded-full ml-2 mt-1"})]})},e.id)),0===S.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(k.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No notifications"})]})]})})]})]}),(0,a.jsxs)(d.Zp,{className:"mt-8",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center",children:[(0,a.jsx)(w,{className:"h-5 w-5 mr-2"}),"Loyalty Program"]}),(0,a.jsxs)(d.BT,{children:["You're a valued member since ",(0,x.Yq)(A.memberSince)]})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:[A.loyaltyPoints.toLocaleString()," Points"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"Earn 1 point for every $1 spent"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)(i.$,{children:"Redeem Points"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"Next reward at 3,000 points"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-2",children:[(0,a.jsxs)("span",{children:["Current: ",A.loyaltyPoints]}),(0,a.jsx)("span",{children:"Next Reward: 3,000"})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(A.loyaltyPoints/3e3*100,"%")}})})]})]})]})]})})}},5525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"notFound")&&t.d(s,{notFound:function(){return a.notFound}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6151:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},7108:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},8564:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9947:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}},e=>{e.O(0,[455,874,327,441,964,358],()=>e(e.s=4098)),_N_E=e.O()}]);