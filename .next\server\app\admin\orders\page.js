(()=>{var a={};a.id=798,a.ids=[798],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15243:(a,b,c)=>{Promise.resolve().then(c.bind(c,74652))},18903:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,58322)),"E:\\aidevcommerce\\src\\app\\admin\\orders\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,15515)),"E:\\aidevcommerce\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\aidevcommerce\\src\\app\\admin\\orders\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/orders/page",pathname:"/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/orders/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:a=>{"use strict";a.exports=require("path")},35071:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41550:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48340:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58322:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\aidevcommerce\\\\src\\\\app\\\\admin\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\app\\admin\\orders\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74652:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(88528),i=c(29523),j=c(44493),k=c(96834),l=c(98436),m=c(29867),n=c(4780),o=c(48730),p=c(78122),q=c(88059),r=c(5336),s=c(35071),t=c(19080),u=c(58869),v=c(41550),w=c(48340),x=c(13861),y=c(31158),z=c(99270);let A=[{id:"ORD-2024-001",orderNumber:"#2024-001",customer:{name:"John Doe",email:"<EMAIL>",phone:"+****************",avatar:"/customer-1.jpg"},status:"completed",paymentStatus:"paid",fulfillmentStatus:"delivered",total:299.99,subtotal:249.99,tax:20,shipping:30,items:[{id:"1",name:"Premium Wireless Headphones",sku:"PWH-001",quantity:1,price:199.99,image:"/product-1.jpg"},{id:"2",name:"Wireless Charging Pad",sku:"WCP-004",quantity:1,price:49.99,image:"/product-4.jpg"}],shippingAddress:{name:"John Doe",street:"123 Main St",city:"New York",state:"NY",zip:"10001",country:"USA"},paymentMethod:"Credit Card (**** 4242)",createdAt:new Date("2024-01-20T10:30:00"),updatedAt:new Date("2024-01-22T14:20:00"),notes:"Customer requested expedited shipping"},{id:"ORD-2024-002",orderNumber:"#2024-002",customer:{name:"Jane Smith",email:"<EMAIL>",phone:"+****************",avatar:"/customer-2.jpg"},status:"processing",paymentStatus:"paid",fulfillmentStatus:"preparing",total:159.99,subtotal:139.99,tax:11.2,shipping:8.8,items:[{id:"3",name:"Smart Fitness Watch",sku:"SFW-002",quantity:1,price:139.99,image:"/product-2.jpg"}],shippingAddress:{name:"Jane Smith",street:"456 Oak Ave",city:"Los Angeles",state:"CA",zip:"90210",country:"USA"},paymentMethod:"PayPal",createdAt:new Date("2024-01-19T15:45:00"),updatedAt:new Date("2024-01-20T09:15:00"),notes:""},{id:"ORD-2024-003",orderNumber:"#2024-003",customer:{name:"Mike Johnson",email:"<EMAIL>",phone:"+****************",avatar:"/customer-3.jpg"},status:"shipped",paymentStatus:"paid",fulfillmentStatus:"shipped",total:89.99,subtotal:79.99,tax:6.4,shipping:3.6,items:[{id:"4",name:"Wireless Charging Pad",sku:"WCP-004",quantity:1,price:79.99,image:"/product-4.jpg"}],shippingAddress:{name:"Mike Johnson",street:"789 Pine St",city:"Chicago",state:"IL",zip:"60601",country:"USA"},paymentMethod:"Credit Card (**** 1234)",createdAt:new Date("2024-01-18T11:20:00"),updatedAt:new Date("2024-01-19T16:30:00"),notes:"Fragile items - handle with care"},{id:"ORD-2024-004",orderNumber:"#2024-004",customer:{name:"Sarah Wilson",email:"<EMAIL>",phone:"+****************",avatar:"/customer-4.jpg"},status:"pending",paymentStatus:"pending",fulfillmentStatus:"pending",total:449.99,subtotal:399.99,tax:32,shipping:18,items:[{id:"5",name:"Ergonomic Office Chair",sku:"EOC-003",quantity:1,price:399.99,image:"/product-3.jpg"}],shippingAddress:{name:"Sarah Wilson",street:"321 Elm St",city:"Miami",state:"FL",zip:"33101",country:"USA"},paymentMethod:"Bank Transfer",createdAt:new Date("2024-01-17T13:10:00"),updatedAt:new Date("2024-01-17T13:10:00"),notes:"Waiting for payment confirmation"},{id:"ORD-2024-005",orderNumber:"#2024-005",customer:{name:"David Brown",email:"<EMAIL>",phone:"+****************",avatar:"/customer-5.jpg"},status:"cancelled",paymentStatus:"refunded",fulfillmentStatus:"cancelled",total:199.99,subtotal:179.99,tax:14.4,shipping:5.6,items:[{id:"6",name:"Professional Camera Lens",sku:"PCL-005",quantity:1,price:179.99,image:"/product-5.jpg"}],shippingAddress:{name:"David Brown",street:"654 Maple Ave",city:"Seattle",state:"WA",zip:"98101",country:"USA"},paymentMethod:"Credit Card (**** 5678)",createdAt:new Date("2024-01-16T09:30:00"),updatedAt:new Date("2024-01-17T11:45:00"),notes:"Customer requested cancellation due to change of mind"}],B=["All Status","pending","processing","shipped","completed","cancelled"],C=["All Payment","pending","paid","failed","refunded"],D=["All Fulfillment","pending","preparing","shipped","delivered","cancelled"],E=(a,b)=>({order:{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",processing:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",shipped:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"},payment:{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",paid:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",failed:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",refunded:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},fulfillment:{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",preparing:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",shipped:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",delivered:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}})[b][a]||"bg-gray-100 text-gray-800",F=(a,b)=>({order:{pending:o.A,processing:p.A,shipped:q.A,completed:r.A,cancelled:s.A},payment:{pending:o.A,paid:r.A,failed:s.A,refunded:p.A},fulfillment:{pending:o.A,preparing:t.A,shipped:q.A,delivered:r.A,cancelled:s.A}})[b][a]||o.A;function G(){let[a,b]=(0,e.useState)(""),[c,f]=(0,e.useState)("All Status"),[q,s]=(0,e.useState)("All Payment"),[G,H]=(0,e.useState)("All Fulfillment"),[I,J]=(0,e.useState)([]),{t:K}=(0,l.o)(),{toast:L}=(0,m.dj)(),M=A.filter(b=>{let d=b.orderNumber.toLowerCase().includes(a.toLowerCase())||b.customer.name.toLowerCase().includes(a.toLowerCase())||b.customer.email.toLowerCase().includes(a.toLowerCase()),e="All Status"===c||b.status===c,f="All Payment"===q||b.paymentStatus===q,g="All Fulfillment"===G||b.fulfillmentStatus===G;return d&&e&&f&&g}),N=a=>{L({title:"Bulk Action",description:`${a} applied to ${I.length} orders`}),J([])},O=({order:a})=>{let b=F(a.status,"order"),c=F(a.paymentStatus,"payment"),e=F(a.fulfillmentStatus,"fulfillment");return(0,d.jsx)(j.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,d.jsx)("input",{type:"checkbox",checked:I.includes(a.id),onChange:()=>{var b;return b=a.id,void J(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])},className:"mt-1"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-lg",children:a.orderNumber}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(0,n.Yq)(a.createdAt)})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"font-bold text-xl",children:(0,n.$g)(a.total)}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:[a.items.length," item",1!==a.items.length?"s":""]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center",children:(0,d.jsx)(u.A,{className:"h-5 w-5 text-gray-500"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:a.customer.name}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:a.customer.email})]})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[(0,d.jsxs)(k.E,{className:`flex items-center space-x-1 ${E(a.status,"order")}`,children:[(0,d.jsx)(b,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"capitalize",children:a.status})]}),(0,d.jsxs)(k.E,{className:`flex items-center space-x-1 ${E(a.paymentStatus,"payment")}`,children:[(0,d.jsx)(c,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"capitalize",children:a.paymentStatus})]}),(0,d.jsxs)(k.E,{className:`flex items-center space-x-1 ${E(a.fulfillmentStatus,"fulfillment")}`,children:[(0,d.jsx)(e,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"capitalize",children:a.fulfillmentStatus})]})]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-2",children:"Items:"}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.items.slice(0,3).map(a=>(0,d.jsxs)("div",{className:"flex items-center space-x-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center",children:(0,d.jsx)(t.A,{className:"h-4 w-4 text-gray-500"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-xs font-medium line-clamp-1",children:a.name}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Qty: ",a.quantity]})]})]},a.id)),a.items.length>3&&(0,d.jsxs)("div",{className:"flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg p-2 text-xs text-gray-500",children:["+",a.items.length-3," more"]})]})]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-1",children:"Shipping to:"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[a.shippingAddress.street,", ",a.shippingAddress.city,", ",a.shippingAddress.state," ",a.shippingAddress.zip]})]}),a.notes&&(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-1",children:"Notes:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded",children:a.notes})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(i.$,{variant:"ghost",size:"sm",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Email"]}),(0,d.jsxs)(i.$,{variant:"ghost",size:"sm",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Call"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g(),{href:`/admin/orders/${a.id}`,children:(0,d.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"View"]})}),(0,d.jsxs)("select",{value:a.status,onChange:b=>{var c;return a.id,c=b.target.value,void L({title:"Order Updated",description:`Order status updated to ${c}`})},className:"px-2 py-1 text-sm border rounded",children:[(0,d.jsx)("option",{value:"pending",children:"Pending"}),(0,d.jsx)("option",{value:"processing",children:"Processing"}),(0,d.jsx)("option",{value:"shipped",children:"Shipped"}),(0,d.jsx)("option",{value:"completed",children:"Completed"}),(0,d.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]})]})]})]})})})})};return(0,d.jsx)(h.U,{children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Orders"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage customer orders and fulfillment"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)(i.$,{variant:"outline",children:[(0,d.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,d.jsxs)(i.$,{children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Orders"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:A.length})]}),(0,d.jsx)(t.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Pending Orders"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:A.filter(a=>"pending"===a.status).length})]}),(0,d.jsx)(o.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Processing"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:A.filter(a=>"processing"===a.status).length})]}),(0,d.jsx)(p.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Completed"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:A.filter(a=>"completed"===a.status).length})]}),(0,d.jsx)(r.A,{className:"h-8 w-8 text-green-500"})]})})})]}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)("input",{type:"text",placeholder:"Search orders...",value:a,onChange:a=>b(a.target.value),className:"pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("select",{value:c,onChange:a=>f(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:B.map(a=>(0,d.jsx)("option",{value:a,children:a},a))}),(0,d.jsx)("select",{value:q,onChange:a=>s(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:C.map(a=>(0,d.jsx)("option",{value:a,children:a},a))}),(0,d.jsx)("select",{value:G,onChange:a=>H(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:D.map(a=>(0,d.jsx)("option",{value:a,children:a},a))})]})]})})}),I.length>0&&(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("span",{className:"text-sm font-medium",children:[I.length," orders selected"]}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{J(I.length===M.length?[]:M.map(a=>a.id))},children:I.length===M.length?"Deselect All":"Select All"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>N("Mark as Processing"),children:"Mark Processing"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>N("Mark as Shipped"),children:"Mark Shipped"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>N("Export Selected"),children:"Export"})]})]})})}),(0,d.jsx)("div",{className:"space-y-4",children:M.map(a=>(0,d.jsx)(O,{order:a},a.id))}),0===M.length&&(0,d.jsx)(j.Zp,{children:(0,d.jsxs)(j.Wu,{className:"text-center py-12",children:[(0,d.jsx)(t.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"No orders found"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Try adjusting your search or filter criteria"}),(0,d.jsx)(i.$,{onClick:()=>{b(""),f("All Status"),s("All Payment"),H("All Fulfillment")},children:"Clear Filters"})]})}),M.length>0&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Showing ",M.length," of ",A.length," orders"]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",disabled:!0,children:"Previous"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",className:"bg-primary-50 dark:bg-primary-900",children:"1"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",children:"2"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",children:"Next"})]})]})]})})}},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78291:(a,b,c)=>{Promise.resolve().then(c.bind(c,58322))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88059:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,54,776,300],()=>b(b.s=18903));module.exports=c})();