(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[947],{288:(e,t,r)=>{"use strict";r.d(t,{rc:()=>ev,bm:()=>eb,VY:()=>em,Kq:()=>ed,bL:()=>ep,hE:()=>ey,LM:()=>ef});var s,n,i=r(2115),a=r.t(i,2),o=r(7650);function u(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}var l=r(6101);function c(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function h(e,t){var r=c(e,t,"get");return r.get?r.get.call(e):r.value}function d(e,t,r){var s=c(e,t,"set");if(s.set)s.set.call(e,r);else{if(!s.writable)throw TypeError("attempted to set read only private field");s.value=r}return r}var f=r(5155);function p(e,t=[]){let r=[],s=()=>{let t=r.map(e=>i.createContext(e));return function(r){let s=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return s.scopeName=e,[function(t,s){let n=i.createContext(s),a=r.length;r=[...r,s];let o=t=>{let{scope:r,children:s,...o}=t,u=r?.[e]?.[a]||n,l=i.useMemo(()=>o,Object.values(o));return(0,f.jsx)(u.Provider,{value:l,children:s})};return o.displayName=t+"Provider",[o,function(r,o){let u=o?.[e]?.[a]||n,l=i.useContext(u);if(l)return l;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((t,{useScope:r,scopeName:s})=>{let n=r(e)[`__scope${s}`];return{...t,...n}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}(s,...t)]}var y=r(9708),m=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,s=b(t),n=s>=0?s:r+s;return n<0||n>=r?-1:n}(e,t);return -1===r?void 0:e[r]}function b(e){return e!=e||0===e?0:Math.trunc(e)}s=new WeakMap,class e extends Map{set(e,t){return m.get(this)&&(this.has(e)?h(this,s)[h(this,s).indexOf(e)]=e:h(this,s).push(e)),super.set(e,t),this}insert(e,t,r){let n,i=this.has(t),a=h(this,s).length,o=b(e),u=o>=0?o:a+o,l=u<0||u>=a?-1:u;if(l===this.size||i&&l===this.size-1||-1===l)return this.set(t,r),this;let c=this.size+ +!i;o<0&&u++;let d=[...h(this,s)],f=!1;for(let e=u;e<c;e++)if(u===e){let s=d[e];d[e]===t&&(s=d[e+1]),i&&this.delete(t),n=this.get(s),this.set(t,r)}else{f||d[e-1]!==t||(f=!0);let r=d[f?e:e-1],s=n;n=this.get(r),this.delete(r),this.set(r,s)}return this}with(t,r,s){let n=new e(this);return n.insert(t,r,s),n}before(e){let t=h(this,s).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let n=h(this,s).indexOf(e);return -1===n?this:this.insert(n,t,r)}after(e){let t=h(this,s).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let n=h(this,s).indexOf(e);return -1===n?this:this.insert(n+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return d(this,s,[]),super.clear()}delete(e){let t=super.delete(e);return t&&h(this,s).splice(h(this,s).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=v(h(this,s),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=v(h(this,s),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return h(this,s).indexOf(e)}keyAt(e){return v(h(this,s),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let s=r+t;return s<0&&(s=0),s>=this.size&&(s=this.size-1),this.at(s)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let s=r+t;return s<0&&(s=0),s>=this.size&&(s=this.size-1),this.keyAt(s)}find(e,t){let r=0;for(let s of this){if(Reflect.apply(e,t,[s,r,this]))return s;r++}}findIndex(e,t){let r=0;for(let s of this){if(Reflect.apply(e,t,[s,r,this]))return r;r++}return -1}filter(t,r){let s=[],n=0;for(let e of this)Reflect.apply(t,r,[e,n,this])&&s.push(e),n++;return new e(s)}map(t,r){let s=[],n=0;for(let e of this)s.push([e[0],Reflect.apply(t,r,[e,n,this])]),n++;return new e(s)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[s,n]=t,i=0,a=null!=n?n:this.at(0);for(let e of this)a=0===i&&1===t.length?e:Reflect.apply(s,this,[a,e,i,this]),i++;return a}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[s,n]=t,i=null!=n?n:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);i=e===this.size-1&&1===t.length?r:Reflect.apply(s,this,[i,r,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),s=this.get(r);t.set(r,s)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];let n=[...this.entries()];return n.splice(...r),new e(n)}slice(t,r){let s=new e,n=this.size-1;if(void 0===t)return s;t<0&&(t+=this.size),void 0!==r&&r>0&&(n=r-1);for(let e=t;e<=n;e++){let t=this.keyAt(e),r=this.get(t);s.set(t,r)}return s}every(e,t){let r=0;for(let s of this){if(!Reflect.apply(e,t,[s,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let s of this){if(Reflect.apply(e,t,[s,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,s,{writable:!0,value:void 0}),d(this,s,[...super.keys()]),m.set(this,!0)}};var g=r(3655);function w(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var E="dismissableLayer.update",C=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=i.forwardRef((e,t)=>{var r,s;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:h,onInteractOutside:d,onDismiss:p,...y}=e,m=i.useContext(C),[v,b]=i.useState(null),x=null!=(s=null==v?void 0:v.ownerDocument)?s:null==(r=globalThis)?void 0:r.document,[,T]=i.useState({}),P=(0,l.s)(t,e=>b(e)),A=Array.from(m.layers),[R]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),k=A.indexOf(R),M=v?A.indexOf(v):-1,D=m.layersWithOutsidePointerEventsDisabled.size>0,F=M>=k,q=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,s=w(e),n=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let t=function(){S("dismissableLayer.pointerDownOutside",s,n,{discrete:!0})},n={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);n.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,s]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));F&&!r&&(null==c||c(e),null==d||d(e),e.defaultPrevented||null==p||p())},x),L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,s=w(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&S("dismissableLayer.focusOutside",s,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,s]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...m.branches].some(e=>e.contains(t))&&(null==h||h(e),null==d||d(e),e.defaultPrevented||null==p||p())},x);return!function(e,t=globalThis?.document){let r=w(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{M===m.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},x),i.useEffect(()=>{if(v)return a&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(n=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(v)),m.layers.add(v),O(),()=>{a&&1===m.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=n)}},[v,x,a,m]),i.useEffect(()=>()=>{v&&(m.layers.delete(v),m.layersWithOutsidePointerEventsDisabled.delete(v),O())},[v,m]),i.useEffect(()=>{let e=()=>T({});return document.addEventListener(E,e),()=>document.removeEventListener(E,e)},[]),(0,f.jsx)(g.sG.div,{...y,ref:P,style:{pointerEvents:D?F?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,L.onFocusCapture),onBlurCapture:u(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,q.onPointerDownCapture)})});x.displayName="DismissableLayer";var T=i.forwardRef((e,t)=>{let r=i.useContext(C),s=i.useRef(null),n=(0,l.s)(t,s);return i.useEffect(()=>{let e=s.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,f.jsx)(g.sG.div,{...e,ref:n})});function O(){let e=new CustomEvent(E);document.dispatchEvent(e)}function S(e,t,r,s){let{discrete:n}=s,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,g.hO)(i,a):i.dispatchEvent(a)}T.displayName="DismissableLayerBranch";var P=globalThis?.document?i.useLayoutEffect:()=>{},A=i.forwardRef((e,t)=>{var r,s;let{container:n,...a}=e,[u,l]=i.useState(!1);P(()=>l(!0),[]);let c=n||u&&(null==(s=globalThis)||null==(r=s.document)?void 0:r.body);return c?o.createPortal((0,f.jsx)(g.sG.div,{...a,ref:t}),c):null});A.displayName="Portal";var R=e=>{let{present:t,children:r}=e,s=function(e){var t,r;let[s,n]=i.useState(),a=i.useRef(null),o=i.useRef(e),u=i.useRef("none"),[l,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let s=r[e][t];return null!=s?s:e},t));return i.useEffect(()=>{let e=k(a.current);u.current="mounted"===l?e:"none"},[l]),P(()=>{let t=a.current,r=o.current;if(r!==e){let s=u.current,n=k(t);e?c("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&s!==n?c("ANIMATION_OUT"):c("UNMOUNT"),o.current=e}},[e,c]),P(()=>{if(s){var e;let t,r=null!=(e=s.ownerDocument.defaultView)?e:window,n=e=>{let n=k(a.current).includes(e.animationName);if(e.target===s&&n&&(c("ANIMATION_END"),!o.current)){let e=s.style.animationFillMode;s.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=e)})}},i=e=>{e.target===s&&(u.current=k(a.current))};return s.addEventListener("animationstart",i),s.addEventListener("animationcancel",n),s.addEventListener("animationend",n),()=>{r.clearTimeout(t),s.removeEventListener("animationstart",i),s.removeEventListener("animationcancel",n),s.removeEventListener("animationend",n)}}c("ANIMATION_END")},[s,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:i.useCallback(e=>{a.current=e?getComputedStyle(e):null,n(e)},[])}}(t),n="function"==typeof r?r({present:s.isPresent}):i.Children.only(r),a=(0,l.s)(s.ref,function(e){var t,r;let s=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,n=s&&"isReactWarning"in s&&s.isReactWarning;return n?e.ref:(n=(s=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in s&&s.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n));return"function"==typeof r||s.isPresent?i.cloneElement(n,{ref:a}):null};function k(e){return(null==e?void 0:e.animationName)||"none"}R.displayName="Presence";var M=a[" useInsertionEffect ".trim().toString()]||P;Symbol("RADIX:SYNC_STATE");var D=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),F=i.forwardRef((e,t)=>(0,f.jsx)(g.sG.span,{...e,ref:t,style:{...D,...e.style}}));F.displayName="VisuallyHidden";var q="ToastProvider",[L,N,j]=function(e){let t=e+"CollectionProvider",[r,s]=p(t),[n,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:r}=e,s=i.useRef(null),a=i.useRef(new Map).current;return(0,f.jsx)(n,{scope:t,itemMap:a,collectionRef:s,children:r})};o.displayName=t;let u=e+"CollectionSlot",c=(0,y.TL)(u),h=i.forwardRef((e,t)=>{let{scope:r,children:s}=e,n=a(u,r),i=(0,l.s)(t,n.collectionRef);return(0,f.jsx)(c,{ref:i,children:s})});h.displayName=u;let d=e+"CollectionItemSlot",m="data-radix-collection-item",v=(0,y.TL)(d),b=i.forwardRef((e,t)=>{let{scope:r,children:s,...n}=e,o=i.useRef(null),u=(0,l.s)(t,o),c=a(d,r);return i.useEffect(()=>(c.itemMap.set(o,{ref:o,...n}),()=>void c.itemMap.delete(o))),(0,f.jsx)(v,{...{[m]:""},ref:u,children:s})});return b.displayName=d,[{Provider:o,Slot:h,ItemSlot:b},function(t){let r=a(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}("Toast"),[I,_]=p("Toast",[j]),[K,Q]=I(q),U=e=>{let{__scopeToast:t,label:r="Notification",duration:s=5e3,swipeDirection:n="right",swipeThreshold:a=50,children:o}=e,[u,l]=i.useState(null),[c,h]=i.useState(0),d=i.useRef(!1),p=i.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(q,"`. Expected non-empty `string`.")),(0,f.jsx)(L.Provider,{scope:t,children:(0,f.jsx)(K,{scope:t,label:r,duration:s,swipeDirection:n,swipeThreshold:a,toastCount:c,viewport:u,onViewportChange:l,onToastAdd:i.useCallback(()=>h(e=>e+1),[]),onToastRemove:i.useCallback(()=>h(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:p,children:o})})};U.displayName=q;var z="ToastViewport",G=["F8"],H="toast.viewportPause",W="toast.viewportResume",V=i.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:s=G,label:n="Notifications ({hotkey})",...a}=e,o=Q(z,r),u=N(r),c=i.useRef(null),h=i.useRef(null),d=i.useRef(null),p=i.useRef(null),y=(0,l.s)(t,p,o.onViewportChange),m=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=o.toastCount>0;i.useEffect(()=>{let e=e=>{var t;0!==s.length&&s.every(t=>e[t]||e.code===t)&&(null==(t=p.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[s]),i.useEffect(()=>{let e=c.current,t=p.current;if(v&&e&&t){let r=()=>{if(!o.isClosePausedRef.current){let e=new CustomEvent(H);t.dispatchEvent(e),o.isClosePausedRef.current=!0}},s=()=>{if(o.isClosePausedRef.current){let e=new CustomEvent(W);t.dispatchEvent(e),o.isClosePausedRef.current=!1}},n=t=>{e.contains(t.relatedTarget)||s()},i=()=>{e.contains(document.activeElement)||s()};return e.addEventListener("focusin",r),e.addEventListener("focusout",n),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",s),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",n),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",s)}}},[v,o.isClosePausedRef]);let b=i.useCallback(e=>{let{tabbingDirection:t}=e,r=u().map(e=>{let r=e.ref.current,s=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?s:s.reverse()});return("forwards"===t?r.reverse():r).flat()},[u]);return i.useEffect(()=>{let e=p.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var s,n,i;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null==(s=h.current)||s.focus();return}let o=b({tabbingDirection:a?"backwards":"forwards"}),u=o.findIndex(e=>e===r);eh(o.slice(u+1))?t.preventDefault():a?null==(n=h.current)||n.focus():null==(i=d.current)||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[u,b]),(0,f.jsxs)(T,{ref:c,role:"region","aria-label":n.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&(0,f.jsx)($,{ref:h,onFocusFromOutsideViewport:()=>{eh(b({tabbingDirection:"forwards"}))}}),(0,f.jsx)(L.Slot,{scope:r,children:(0,f.jsx)(g.sG.ol,{tabIndex:-1,...a,ref:y})}),v&&(0,f.jsx)($,{ref:d,onFocusFromOutsideViewport:()=>{eh(b({tabbingDirection:"backwards"}))}})]})});V.displayName=z;var B="ToastFocusProxy",$=i.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:s,...n}=e,i=Q(B,r);return(0,f.jsx)(F,{"aria-hidden":!0,tabIndex:0,...n,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null==(t=i.viewport)?void 0:t.contains(r))||s()}})});$.displayName=B;var X="Toast",Y=i.forwardRef((e,t)=>{let{forceMount:r,open:s,defaultOpen:n,onOpenChange:a,...o}=e,[l,c]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:s}){let[n,a,o]=function({defaultProp:e,onChange:t}){let[r,s]=i.useState(e),n=i.useRef(r),a=i.useRef(t);return M(()=>{a.current=t},[t]),i.useEffect(()=>{n.current!==r&&(a.current?.(r),n.current=r)},[r,n]),[r,s,a]}({defaultProp:t,onChange:r}),u=void 0!==e,l=u?e:n;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${s} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,s])}return[l,i.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&o.current?.(r)}else a(t)},[u,e,a,o])]}({prop:s,defaultProp:null==n||n,onChange:a,caller:X});return(0,f.jsx)(R,{present:r||l,children:(0,f.jsx)(ee,{open:l,...o,ref:t,onClose:()=>c(!1),onPause:w(e.onPause),onResume:w(e.onResume),onSwipeStart:u(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:u(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:u(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:u(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),c(!1)})})})});Y.displayName=X;var[J,Z]=I(X,{onClose(){}}),ee=i.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:n,open:a,onClose:c,onEscapeKeyDown:h,onPause:d,onResume:p,onSwipeStart:y,onSwipeMove:m,onSwipeCancel:v,onSwipeEnd:b,...E}=e,C=Q(X,r),[T,O]=i.useState(null),S=(0,l.s)(t,e=>O(e)),P=i.useRef(null),A=i.useRef(null),R=n||C.duration,k=i.useRef(0),M=i.useRef(R),D=i.useRef(0),{onToastAdd:F,onToastRemove:q}=C,N=w(()=>{var e;(null==T?void 0:T.contains(document.activeElement))&&(null==(e=C.viewport)||e.focus()),c()}),j=i.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(D.current),k.current=new Date().getTime(),D.current=window.setTimeout(N,e))},[N]);i.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{j(M.current),null==p||p()},r=()=>{let e=new Date().getTime()-k.current;M.current=M.current-e,window.clearTimeout(D.current),null==d||d()};return e.addEventListener(H,r),e.addEventListener(W,t),()=>{e.removeEventListener(H,r),e.removeEventListener(W,t)}}},[C.viewport,R,d,p,j]),i.useEffect(()=>{a&&!C.isClosePausedRef.current&&j(R)},[a,R,C.isClosePausedRef,j]),i.useEffect(()=>(F(),()=>q()),[F,q]);let I=i.useMemo(()=>T?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var s;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(s=t).nodeType===s.ELEMENT_NODE){let s=t.ariaHidden||t.hidden||"none"===t.style.display,n=""===t.dataset.radixToastAnnounceExclude;if(!s)if(n){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}),r}(T):null,[T]);return C.viewport?(0,f.jsxs)(f.Fragment,{children:[I&&(0,f.jsx)(et,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:I}),(0,f.jsx)(J,{scope:r,onClose:N,children:o.createPortal((0,f.jsx)(L.ItemSlot,{scope:r,children:(0,f.jsx)(x,{asChild:!0,onEscapeKeyDown:u(h,()=>{C.isFocusedToastEscapeKeyDownRef.current||N(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,f.jsx)(g.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":a?"open":"closed","data-swipe-direction":C.swipeDirection,...E,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:u(e.onKeyDown,e=>{"Escape"===e.key&&(null==h||h(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,N()))}),onPointerDown:u(e.onPointerDown,e=>{0===e.button&&(P.current={x:e.clientX,y:e.clientY})}),onPointerMove:u(e.onPointerMove,e=>{if(!P.current)return;let t=e.clientX-P.current.x,r=e.clientY-P.current.y,s=!!A.current,n=["left","right"].includes(C.swipeDirection),i=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,a=n?i(0,t):0,o=n?0:i(0,r),u="touch"===e.pointerType?10:2,l={x:a,y:o},c={originalEvent:e,delta:l};s?(A.current=l,el("toast.swipeMove",m,c,{discrete:!1})):ec(l,C.swipeDirection,u)?(A.current=l,el("toast.swipeStart",y,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(P.current=null)}),onPointerUp:u(e.onPointerUp,e=>{let t=A.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),A.current=null,P.current=null,t){let r=e.currentTarget,s={originalEvent:e,delta:t};ec(t,C.swipeDirection,C.swipeThreshold)?el("toast.swipeEnd",b,s,{discrete:!0}):el("toast.swipeCancel",v,s,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),et=e=>{let{__scopeToast:t,children:r,...s}=e,n=Q(X,t),[a,o]=i.useState(!1),[u,l]=i.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=w(e);P(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>o(!0)),i.useEffect(()=>{let e=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,f.jsx)(A,{asChild:!0,children:(0,f.jsx)(F,{...s,children:a&&(0,f.jsxs)(f.Fragment,{children:[n.label," ",r]})})})},er=i.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e;return(0,f.jsx)(g.sG.div,{...s,ref:t})});er.displayName="ToastTitle";var es=i.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e;return(0,f.jsx)(g.sG.div,{...s,ref:t})});es.displayName="ToastDescription";var en="ToastAction",ei=i.forwardRef((e,t)=>{let{altText:r,...s}=e;return r.trim()?(0,f.jsx)(eu,{altText:r,asChild:!0,children:(0,f.jsx)(eo,{...s,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(en,"`. Expected non-empty `string`.")),null)});ei.displayName=en;var ea="ToastClose",eo=i.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e,n=Z(ea,r);return(0,f.jsx)(eu,{asChild:!0,children:(0,f.jsx)(g.sG.button,{type:"button",...s,ref:t,onClick:u(e.onClick,n.onClose)})})});eo.displayName=ea;var eu=i.forwardRef((e,t)=>{let{__scopeToast:r,altText:s,...n}=e;return(0,f.jsx)(g.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":s||void 0,...n,ref:t})});function el(e,t,r,s){let{discrete:n}=s,i=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,g.hO)(i,a):i.dispatchEvent(a)}var ec=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=Math.abs(e.x),n=Math.abs(e.y),i=s>n;return"left"===t||"right"===t?i&&s>r:!i&&n>r};function eh(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ed=U,ef=V,ep=Y,ey=er,em=es,ev=ei,eb=eo},381:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},749:e=>{e.exports={style:{fontFamily:"'Tajawal', 'Tajawal Fallback'",fontStyle:"normal"},className:"__className_4beeb2",variable:"__variable_4beeb2"}},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,N:()=>c});var s=r(2115),n=(e,t,r,s,n,i,a,o)=>{let u=document.documentElement,l=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,s=r&&i?n.map(e=>i[e]||e):n;r?(u.classList.remove(...s),u.classList.add(i&&i[t]?i[t]:t)):u.setAttribute(e,t)}),r=t,o&&l.includes(r)&&(u.style.colorScheme=r)}if(s)c(s);else try{let e=localStorage.getItem(t)||r,s=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(s)}catch(e){}},i=["light","dark"],a="(prefers-color-scheme: dark)",o=s.createContext(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=s.useContext(o))?e:u},c=e=>s.useContext(o)?s.createElement(s.Fragment,null,e.children):s.createElement(d,{...e}),h=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:u=!0,storageKey:l="theme",themes:c=h,defaultTheme:d=n?"system":"light",attribute:v="data-theme",value:b,children:g,nonce:w,scriptProps:E}=e,[C,x]=s.useState(()=>p(l,d)),[T,O]=s.useState(()=>"system"===C?m():C),S=b?Object.values(b):c,P=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=m());let s=b?b[t]:t,a=r?y(w):null,o=document.documentElement,l=e=>{"class"===e?(o.classList.remove(...S),s&&o.classList.add(s)):e.startsWith("data-")&&(s?o.setAttribute(e,s):o.removeAttribute(e))};if(Array.isArray(v)?v.forEach(l):l(v),u){let e=i.includes(d)?d:null,r=i.includes(t)?t:e;o.style.colorScheme=r}null==a||a()},[w]),A=s.useCallback(e=>{let t="function"==typeof e?e(C):e;x(t);try{localStorage.setItem(l,t)}catch(e){}},[C]),R=s.useCallback(e=>{O(m(e)),"system"===C&&n&&!t&&P("system")},[C,t]);s.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(R),R(e),()=>e.removeListener(R)},[R]),s.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?x(e.newValue):A(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),s.useEffect(()=>{P(null!=t?t:C)},[t,C]);let k=s.useMemo(()=>({theme:C,setTheme:A,forcedTheme:t,resolvedTheme:"system"===C?T:C,themes:n?[...c,"system"]:c,systemTheme:n?T:void 0}),[C,A,t,T,n,c]);return s.createElement(o.Provider,{value:k},s.createElement(f,{forcedTheme:t,storageKey:l,attribute:v,enableSystem:n,enableColorScheme:u,defaultTheme:d,value:b,themes:c,nonce:w,scriptProps:E}),g)},f=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:i,enableSystem:a,enableColorScheme:o,defaultTheme:u,value:l,themes:c,nonce:h,scriptProps:d}=e,f=JSON.stringify([i,r,u,t,c,l,a,o]).slice(1,-1);return s.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(f,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},1976:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},2098:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2922:(e,t,r)=>{"use strict";r.d(t,{E:()=>N});var s="undefined"==typeof window||"Deno"in globalThis;function n(){}function i(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:a,stale:o}=e;if(a){if(s){if(t.queryHash!==u(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!n||n===t.state.fetchStatus)&&(!i||!!i(t))}function o(e,t){let{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(l(t.options.mutationKey)!==l(i))return!1}else if(!c(t.options.mutationKey,i))return!1}return(!s||t.state.status===s)&&(!n||!!n(t))}function u(e,t){return(t?.queryKeyHashFn||l)(e)}function l(e){return JSON.stringify(e,(e,t)=>d(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>c(e[r],t[r]))}function h(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function d(e){if(!f(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!f(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,r=0){let s=[...e,t];return r&&s.length>r?s.slice(1):s}function y(e,t,r=0){let s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var m=Symbol();function v(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==m?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var b=e=>setTimeout(e,0),g=function(){let e=[],t=0,r=e=>{e()},s=e=>{e()},n=b,i=s=>{t?e.push(s):n(()=>{r(s)})};return{batch:i=>{let a;t++;try{a=i()}finally{--t||(()=>{let t=e;e=[],t.length&&n(()=>{s(()=>{t.forEach(e=>{r(e)})})})})()}return a},batchCalls:e=>(...t)=>{i(()=>{e(...t)})},schedule:i,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{s=e},setScheduler:e=>{n=e}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},E=new class extends w{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!s&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},C=new class extends w{#s=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!s&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#s!==e&&(this.#s=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#s}};function x(e){return Math.min(1e3*2**e,3e4)}function T(e){return(e??"online")!=="online"||C.isOnline()}var O=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function S(e){return e instanceof O}function P(e){let t,r=!1,n=0,i=!1,a=function(){let e,t,r=new Promise((r,s)=>{e=r,t=s});function s(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},r.reject=e=>{s({status:"rejected",reason:e}),t(e)},r}(),o=()=>E.isFocused()&&("always"===e.networkMode||C.isOnline())&&e.canRun(),u=()=>T(e.networkMode)&&e.canRun(),l=r=>{i||(i=!0,e.onSuccess?.(r),t?.(),a.resolve(r))},c=r=>{i||(i=!0,e.onError?.(r),t?.(),a.reject(r))},h=()=>new Promise(r=>{t=e=>{(i||o())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,i||e.onContinue?.()}),d=()=>{let t;if(i)return;let a=0===n?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch(t=>{if(i)return;let a=e.retry??3*!s,u=e.retryDelay??x,l="function"==typeof u?u(n,t):u,f=!0===a||"number"==typeof a&&n<a||"function"==typeof a&&a(n,t);if(r||!f)return void c(t);n++,e.onFail?.(n,t),new Promise(e=>{setTimeout(e,l)}).then(()=>o()?void 0:h()).then(()=>{r?c(t):d()})})};return{promise:a,cancel:t=>{i||(c(new O(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:u,start:()=>(u()?d():h().then(d),a)}}var A=class{#n;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#n=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s?1/0:3e5))}clearGcTimeout(){this.#n&&(clearTimeout(this.#n),this.#n=void 0)}},R=class extends A{#i;#a;#o;#u;#l;#c;#h;constructor(e){super(),this.#h=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#o=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#i=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#i,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){var r,s;let n=(r=this.state.data,"function"==typeof(s=this.options).structuralSharing?s.structuralSharing(r,e):!1!==s.structuralSharing?function e(t,r){if(t===r)return t;let s=h(t)&&h(r);if(s||d(t)&&d(r)){let n=s?t:Object.keys(t),i=n.length,a=s?r:Object.keys(r),o=a.length,u=s?[]:{},l=new Set(n),c=0;for(let n=0;n<o;n++){let i=s?n:a[n];(!s&&l.has(i)||s)&&void 0===t[i]&&void 0===r[i]?(u[i]=void 0,c++):(u[i]=e(t[i],r[i]),u[i]===t[i]&&void 0!==t[i]&&c++)}return i===o&&c===i?t:u}return r}(r,e):e);return this.#d({data:n,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),n}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#l?.promise;return this.#l?.cancel(e),t?t.then(n).catch(n):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#i)}isActive(){return this.observers.some(e=>{var t;return!1!==(t=e.options.enabled,"function"==typeof t?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===i(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#l&&(this.#h?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,s=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,r.signal)})},n=()=>{let e=v(this.options,t),r=(()=>{let e={client:this.#u,queryKey:this.queryKey,meta:this.meta};return s(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,r,this):e(r)},i=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:n};return s(e),e})();this.options.behavior?.onFetch(i,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#d({type:"fetch",meta:i.fetchOptions?.meta});let a=e=>{S(e)&&e.silent||this.#d({type:"error",error:e}),S(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#l=P({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void a(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){a(e);return}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#l.start()}#d(e){let t=t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var r;return{...t,...(r=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:T(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return this.#a=void 0,{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=e.error;if(S(s)&&s.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}};this.state=t(this.state),g.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:e})})}},k=class extends w{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,r){let s=t.queryKey,n=t.queryHash??u(s,t),i=this.get(n);return i||(i=new R({client:e,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(i)),i}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){g.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){g.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){g.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},M=class extends A{#p;#y;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#y=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#d({type:"continue"})};this.#l=P({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let r="pending"===this.state.status,s=!this.#l.canStart();try{if(r)t();else{this.#d({type:"pending",variables:e,isPaused:s}),await this.#y.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:s})}let n=await this.#l.start();return await this.#y.config.onSuccess?.(n,e,this.state.context,this),await this.options.onSuccess?.(n,e,this.state.context),await this.#y.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,e,this.state.context),this.#d({type:"success",data:n}),n}catch(t){try{throw await this.#y.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#y.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#y.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),g.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#y.notify({mutation:this,type:"updated",action:e})})}},D=class extends w{constructor(e={}){super(),this.config=e,this.#m=new Set,this.#v=new Map,this.#b=0}#m;#v;#b;build(e,t,r){let s=new M({mutationCache:this,mutationId:++this.#b,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#m.add(e);let t=F(e);if("string"==typeof t){let r=this.#v.get(t);r?r.push(e):this.#v.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#m.delete(e)){let t=F(e);if("string"==typeof t){let r=this.#v.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#v.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=F(e);if("string"!=typeof t)return!0;{let r=this.#v.get(t),s=r?.find(e=>"pending"===e.state.status);return!s||s===e}}runNext(e){let t=F(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#v.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){g.batch(()=>{this.#m.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>o(t,e))}findAll(e={}){return this.getAll().filter(t=>o(e,t))}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return g.batch(()=>Promise.all(e.map(e=>e.continue().catch(n))))}};function F(e){return e.options.scope?.id}function q(e){return{onFetch:(t,r)=>{let s=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],a=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,l=async()=>{let r=!1,l=v(t.options,t.fetchOptions),c=async(e,s,n)=>{if(r)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);let i=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:s,direction:n?"backward":"forward",meta:t.options.meta};return Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)}),e})(),a=await l(i),{maxPages:o}=t.options,u=n?y:p;return{pages:u(e.pages,a,o),pageParams:u(e.pageParams,s,o)}};if(n&&i.length){let e="backward"===n,t={pages:i,pageParams:a},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:L)(s,t);o=await c(t,r,e)}else{let t=e??i.length;do{let e=0===u?a[0]??s.initialPageParam:L(s,o);if(u>0&&null==e)break;o=await c(o,e),u++}while(u<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=l}}}function L(e,{pages:t,pageParams:r}){let s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}var N=class{#g;#y;#c;#w;#E;#C;#x;#T;constructor(e={}){this.#g=e.queryCache||new k,this.#y=e.mutationCache||new D,this.#c=e.defaultOptions||{},this.#w=new Map,this.#E=new Map,this.#C=0}mount(){this.#C++,1===this.#C&&(this.#x=E.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#T=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#C--,0===this.#C&&(this.#x?.(),this.#x=void 0,this.#T?.(),this.#T=void 0)}isFetching(e){return this.#g.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#y.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#g.build(this,t),s=r.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(i(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#g.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let s=this.defaultQueryOptions({queryKey:e}),n=this.#g.get(s.queryHash),i=n?.state.data,a="function"==typeof t?t(i):t;if(void 0!==a)return this.#g.build(this,s).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return g.batch(()=>this.#g.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state}removeQueries(e){let t=this.#g;g.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#g;return g.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(g.batch(()=>this.#g.findAll(e).map(e=>e.cancel(r)))).then(n).catch(n)}invalidateQueries(e,t={}){return g.batch(()=>(this.#g.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(g.batch(()=>this.#g.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#g.build(this,t);return r.isStaleByTime(i(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n).catch(n)}fetchInfiniteQuery(e){return e.behavior=q(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n).catch(n)}ensureInfiniteQueryData(e){return e.behavior=q(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return C.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#w.set(l(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#w.values()],r={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#E.set(l(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#E.values()],r={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=u(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===m&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}}},3509:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6707:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6715:(e,t,r)=>{"use strict";r.d(t,{Ht:()=>a});var s=r(2115),n=r(5155),i=s.createContext(void 0),a=e=>{let{client:t,children:r}=e;return s.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,n.jsx)(i.Provider,{value:t,children:r})}},7108:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7809:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}}]);