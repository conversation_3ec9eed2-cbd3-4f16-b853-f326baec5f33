"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Star,
  Factory,
  Zap,
  Settings,
  TrendingUp,
  Award,
  Shield,
  Gauge,
  Users,
  Calendar,
  MapPin,
  Phone,
  Mail,
  ArrowRight,
  Download,
  Eye,
} from "lucide-react"

// Mock production lines data
const mockProductionLines = [
  {
    id: "1",
    name: "Automated Assembly Line Pro",
    slug: "automated-assembly-line-pro",
    description: "High-speed automated assembly line with advanced robotics and quality control systems for maximum efficiency.",
    shortDescription: "High-speed automated assembly with robotics",
    category: "Assembly Lines",
    industry: "Automotive",
    capacity: "500 units/hour",
    power: "150 kW",
    footprint: "50m x 20m",
    price: 2500000,
    leadTime: "12-16 weeks",
    rating: 4.9,
    installationCount: 45,
    image: "/production-line-1.jpg",
    featured: true,
    popular: true,
    certifications: ["ISO 9001", "CE", "UL"],
    specifications: {
      "Production Capacity": "500 units/hour",
      "Power Consumption": "150 kW",
      "Floor Space": "50m x 20m x 8m",
      "Automation Level": "Fully Automated",
      "Quality Control": "Integrated Vision Systems",
      "Maintenance": "Predictive Maintenance",
      "Warranty": "2 Years Full Coverage",
      "Training": "Included"
    },
    features: [
      "Advanced Robotics Integration",
      "Real-time Quality Monitoring",
      "Predictive Maintenance System",
      "Energy Efficient Design",
      "Modular Configuration",
      "Remote Monitoring Capability",
      "Safety Compliance Systems",
      "Data Analytics Dashboard"
    ],
    applications: [
      "Automotive Manufacturing",
      "Electronics Assembly",
      "Consumer Goods",
      "Medical Devices"
    ]
  },
  {
    id: "2",
    name: "Flexible Manufacturing Cell",
    slug: "flexible-manufacturing-cell",
    description: "Versatile manufacturing cell that can be quickly reconfigured for different product types and batch sizes.",
    shortDescription: "Versatile cell for different product types",
    category: "Manufacturing Cells",
    industry: "Electronics",
    capacity: "200 units/hour",
    power: "75 kW",
    footprint: "25m x 15m",
    price: 1200000,
    leadTime: "8-12 weeks",
    rating: 4.7,
    installationCount: 67,
    image: "/production-line-2.jpg",
    featured: false,
    popular: true,
    certifications: ["ISO 9001", "CE"],
    specifications: {
      "Production Capacity": "200 units/hour",
      "Power Consumption": "75 kW",
      "Floor Space": "25m x 15m x 6m",
      "Automation Level": "Semi-Automated",
      "Changeover Time": "< 30 minutes",
      "Batch Size": "50-1000 units",
      "Warranty": "18 Months",
      "Training": "2 Weeks On-site"
    },
    features: [
      "Quick Changeover System",
      "Multi-Product Capability",
      "Lean Manufacturing Design",
      "Operator-Friendly Interface",
      "Quality Tracking System",
      "Flexible Tooling",
      "Compact Footprint",
      "Cost-Effective Solution"
    ],
    applications: [
      "Electronics Manufacturing",
      "Small Appliances",
      "Precision Components",
      "Custom Products"
    ]
  },
  {
    id: "3",
    name: "High-Speed Packaging Line",
    slug: "high-speed-packaging-line",
    description: "Ultra-fast packaging line with integrated labeling, sealing, and quality inspection for consumer goods.",
    shortDescription: "Ultra-fast packaging with quality inspection",
    category: "Packaging Lines",
    industry: "Food & Beverage",
    capacity: "1200 packages/hour",
    power: "100 kW",
    footprint: "40m x 12m",
    price: 1800000,
    leadTime: "10-14 weeks",
    rating: 4.8,
    installationCount: 32,
    image: "/production-line-3.jpg",
    featured: true,
    popular: false,
    certifications: ["FDA", "CE", "ISO 22000"],
    specifications: {
      "Packaging Speed": "1200 packages/hour",
      "Power Consumption": "100 kW",
      "Floor Space": "40m x 12m x 7m",
      "Package Types": "Multiple Formats",
      "Labeling": "Automatic Application",
      "Quality Control": "Vision Inspection",
      "Warranty": "2 Years",
      "Compliance": "Food Grade Materials"
    },
    features: [
      "Multi-Format Packaging",
      "Automatic Labeling System",
      "Vision Quality Inspection",
      "Hygienic Design",
      "Waste Reduction Technology",
      "Traceability System",
      "Easy Cleaning Protocols",
      "Regulatory Compliance"
    ],
    applications: [
      "Food Processing",
      "Beverage Industry",
      "Pharmaceuticals",
      "Consumer Products"
    ]
  },
  {
    id: "4",
    name: "Precision Machining Center",
    slug: "precision-machining-center",
    description: "State-of-the-art CNC machining center with multi-axis capability and sub-micron precision for aerospace components.",
    shortDescription: "Multi-axis CNC with sub-micron precision",
    category: "Machining Centers",
    industry: "Aerospace",
    capacity: "24/7 Operation",
    power: "200 kW",
    footprint: "30m x 25m",
    price: 3200000,
    leadTime: "16-20 weeks",
    rating: 4.9,
    installationCount: 18,
    image: "/production-line-4.jpg",
    featured: true,
    popular: false,
    certifications: ["AS9100", "ISO 9001", "NADCAP"],
    specifications: {
      "Machining Precision": "±0.001mm",
      "Power Consumption": "200 kW",
      "Floor Space": "30m x 25m x 10m",
      "Axis Configuration": "5-Axis Simultaneous",
      "Tool Capacity": "200 Tools",
      "Workpiece Size": "2000mm x 1500mm",
      "Warranty": "3 Years",
      "Certification": "Aerospace Grade"
    },
    features: [
      "5-Axis Simultaneous Machining",
      "Sub-micron Precision",
      "Automatic Tool Changing",
      "In-Process Measurement",
      "Thermal Compensation",
      "Vibration Dampening",
      "Coolant Management",
      "Advanced CAM Integration"
    ],
    applications: [
      "Aerospace Components",
      "Medical Implants",
      "Precision Tooling",
      "Research & Development"
    ]
  }
]

const categories = [
  "All Categories",
  "Assembly Lines",
  "Manufacturing Cells",
  "Packaging Lines",
  "Machining Centers",
  "Testing Equipment",
]

const industries = [
  "All Industries",
  "Automotive",
  "Electronics",
  "Food & Beverage",
  "Aerospace",
  "Medical",
  "Pharmaceutical",
]

const sortOptions = [
  { value: "featured", label: "Featured" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "capacity", label: "Highest Capacity" },
  { value: "popular", label: "Most Popular" },
]

export default function ProductionLinesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedIndustry, setSelectedIndustry] = useState("All Industries")
  const [sortBy, setSortBy] = useState("featured")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  
  const { t } = useLanguage()
  const { toast } = useToast()

  const handleInquiry = (productionLine: typeof mockProductionLines[0]) => {
    toast({
      title: "Inquiry Sent",
      description: `We'll contact you about ${productionLine.name} within 24 hours`,
    })
  }

  const handleDownloadBrochure = (productionLine: typeof mockProductionLines[0]) => {
    toast({
      title: "Download Started",
      description: `${productionLine.name} brochure is being downloaded`,
    })
  }

  const filteredProductionLines = mockProductionLines.filter(line => {
    const matchesSearch = line.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         line.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All Categories" || line.category === selectedCategory
    const matchesIndustry = selectedIndustry === "All Industries" || line.industry === selectedIndustry
    
    return matchesSearch && matchesCategory && matchesIndustry
  })

  const sortedProductionLines = filteredProductionLines.sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price
      case "price-high":
        return b.price - a.price
      case "capacity":
        return parseInt(b.capacity) - parseInt(a.capacity)
      case "popular":
        return (b.popular ? 1 : 0) - (a.popular ? 1 : 0)
      case "featured":
      default:
        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0)
    }
  })

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Assembly Lines": return Factory
      case "Manufacturing Cells": return Settings
      case "Packaging Lines": return Zap
      case "Machining Centers": return Gauge
      default: return Factory
    }
  }

  const ProductionLineCard = ({ line }: { line: typeof mockProductionLines[0] }) => {
    const CategoryIcon = getCategoryIcon(line.category)
    
    return (
      <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
        <div className="relative">
          {/* Production Line Image */}
          <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
            <CategoryIcon className="h-20 w-20 text-gray-400" />
          </div>
          
          {/* Badges */}
          <div className="absolute top-3 left-3 space-y-1">
            {line.featured && (
              <Badge className="bg-primary-500 text-white">Featured</Badge>
            )}
            {line.popular && (
              <Badge className="bg-orange-500 text-white">Popular</Badge>
            )}
          </div>

          {/* Quick Actions */}
          <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity space-y-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => handleDownloadBrochure(line)}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Header */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline" className="text-xs">
                  {line.category}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {line.industry}
                </Badge>
              </div>
              
              <h3 className="text-xl font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                <Link href={`/production-lines/${line.slug}`}>
                  {line.name}
                </Link>
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2">
                {line.shortDescription}
              </p>
            </div>

            {/* Key Specs */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-500 dark:text-gray-400">Capacity</div>
                <div className="font-medium">{line.capacity}</div>
              </div>
              <div>
                <div className="text-gray-500 dark:text-gray-400">Power</div>
                <div className="font-medium">{line.power}</div>
              </div>
              <div>
                <div className="text-gray-500 dark:text-gray-400">Footprint</div>
                <div className="font-medium">{line.footprint}</div>
              </div>
              <div>
                <div className="text-gray-500 dark:text-gray-400">Lead Time</div>
                <div className="font-medium">{line.leadTime}</div>
              </div>
            </div>

            {/* Certifications */}
            <div className="flex flex-wrap gap-1">
              {line.certifications.map((cert, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {cert}
                </Badge>
              ))}
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span>{line.rating}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Factory className="h-4 w-4" />
                <span>{line.installationCount} installations</span>
              </div>
            </div>

            {/* Price and CTA */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div>
                <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {formatPrice(line.price)}
                </span>
                <span className="text-sm text-gray-500 ml-1">starting</span>
              </div>
              
              <div className="flex space-x-2">
                <Link href={`/production-lines/${line.slug}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    Details
                  </Button>
                </Link>
                <Button 
                  size="sm"
                  onClick={() => handleInquiry(line)}
                >
                  Inquire
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <Factory className="h-4 w-4 mr-2" />
            Industrial Solutions
          </Badge>
          <h1 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Production Lines
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            State-of-the-art manufacturing equipment and production lines designed for efficiency, reliability, and scalability in modern industrial environments.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search production lines..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* Filter Bar */}
          <div className="flex flex-wrap items-center justify-center gap-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Industry Filter */}
            <select
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {industries.map((industry) => (
                <option key={industry} value={industry}>
                  {industry}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              {sortedProductionLines.length} production lines found
            </div>
          </div>
        </div>

        {/* Production Lines Grid */}
        <div className={`grid gap-8 ${
          viewMode === "grid" 
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-2" 
            : "grid-cols-1"
        }`}>
          {sortedProductionLines.map((line) => (
            <ProductionLineCard key={line.id} line={line} />
          ))}
        </div>

        {/* Empty State */}
        {sortedProductionLines.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏭</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No production lines found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search or filter criteria
            </p>
            <Button onClick={() => {
              setSearchQuery("")
              setSelectedCategory("All Categories")
              setSelectedIndustry("All Industries")
            }}>
              Clear Filters
            </Button>
          </div>
        )}

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          {[
            { value: "200+", label: "Installations Worldwide", icon: Factory },
            { value: "50+", label: "Countries Served", icon: MapPin },
            { value: "15+", label: "Years Experience", icon: Award },
            { value: "24/7", label: "Support Available", icon: Shield },
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl mb-3 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors">
                <stat.icon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8">
          <h3 className="text-2xl font-bold mb-4">
            Need a Custom Production Line?
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
            Our engineering team specializes in designing and building custom production lines tailored to your specific manufacturing requirements and industry standards.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="group">
              <Phone className="h-5 w-5 mr-2" />
              Schedule Consultation
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button variant="outline" size="lg">
              <Mail className="h-5 w-5 mr-2" />
              Request Quote
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
