import { USER_ROLES, ORDER_STATUS, PAYMENT_STATUS, NOTIFICATION_TYPES } from '@/config/constants'

// User Types
export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  phone?: string
  role: keyof typeof USER_ROLES
  emailVerified?: Date
  phoneVerified?: Date
  createdAt: Date
  updatedAt: Date
  addresses?: Address[]
  orders?: Order[]
  wishlist?: WishlistItem[]
  reviews?: Review[]
  notifications?: Notification[]
}

export interface Address {
  id: string
  userId: string
  type: 'shipping' | 'billing'
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state: string
  postalCode: string
  country: string
  phone?: string
  isDefault: boolean
  createdAt: Date
  updatedAt: Date
}

// Product Types
export interface Product {
  id: string
  name: string
  slug: string
  description: string
  shortDescription?: string
  price: number
  comparePrice?: number
  cost?: number
  sku: string
  barcode?: string
  trackQuantity: boolean
  quantity?: number
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  images: ProductImage[]
  category: Category
  categoryId: string
  tags: string[]
  variants?: ProductVariant[]
  attributes?: ProductAttribute[]
  seoTitle?: string
  seoDescription?: string
  status: 'draft' | 'active' | 'archived'
  featured: boolean
  createdAt: Date
  updatedAt: Date
  reviews?: Review[]
  averageRating?: number
  totalReviews?: number
}

export interface ProductImage {
  id: string
  productId: string
  url: string
  alt: string
  position: number
  createdAt: Date
}

export interface ProductVariant {
  id: string
  productId: string
  name: string
  price: number
  comparePrice?: number
  sku: string
  barcode?: string
  quantity?: number
  image?: string
  options: VariantOption[]
  createdAt: Date
  updatedAt: Date
}

export interface VariantOption {
  name: string
  value: string
}

export interface ProductAttribute {
  name: string
  value: string
}

export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  parent?: Category
  children?: Category[]
  products?: Product[]
  seoTitle?: string
  seoDescription?: string
  status: 'active' | 'inactive'
  position: number
  createdAt: Date
  updatedAt: Date
}

// Cart Types
export interface CartItem {
  id: string
  productId: string
  product: Product
  variantId?: string
  variant?: ProductVariant
  quantity: number
  price: number
  createdAt: Date
  updatedAt: Date
}

export interface Cart {
  id: string
  userId?: string
  sessionId?: string
  items: CartItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  currency: string
  createdAt: Date
  updatedAt: Date
}

// Order Types
export interface Order {
  id: string
  orderNumber: string
  userId?: string
  email: string
  status: keyof typeof ORDER_STATUS
  paymentStatus: keyof typeof PAYMENT_STATUS
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  currency: string
  shippingAddress: Address
  billingAddress: Address
  paymentMethod?: string
  paymentIntentId?: string
  notes?: string
  trackingNumber?: string
  shippedAt?: Date
  deliveredAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface OrderItem {
  id: string
  orderId: string
  productId: string
  product: Product
  variantId?: string
  variant?: ProductVariant
  quantity: number
  price: number
  total: number
  createdAt: Date
}

// Wishlist Types
export interface WishlistItem {
  id: string
  userId: string
  productId: string
  product: Product
  variantId?: string
  variant?: ProductVariant
  createdAt: Date
}

// Review Types
export interface Review {
  id: string
  userId: string
  user: User
  productId: string
  product: Product
  rating: number
  title?: string
  content: string
  images?: string[]
  verified: boolean
  helpful: number
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  updatedAt: Date
}

// Service Types
export interface Service {
  id: string
  name: string
  slug: string
  description: string
  shortDescription?: string
  price?: number
  duration?: number
  category: ServiceCategory
  categoryId: string
  features: string[]
  images: string[]
  status: 'active' | 'inactive'
  featured: boolean
  seoTitle?: string
  seoDescription?: string
  createdAt: Date
  updatedAt: Date
}

export interface ServiceCategory {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  services?: Service[]
  status: 'active' | 'inactive'
  position: number
  createdAt: Date
  updatedAt: Date
}

// Production Line Types
export interface ProductionLine {
  id: string
  name: string
  slug: string
  description: string
  shortDescription?: string
  specifications: ProductionLineSpec[]
  images: string[]
  videos?: string[]
  brochure?: string
  category: ProductionLineCategory
  categoryId: string
  status: 'active' | 'inactive'
  featured: boolean
  seoTitle?: string
  seoDescription?: string
  createdAt: Date
  updatedAt: Date
}

export interface ProductionLineSpec {
  name: string
  value: string
  unit?: string
}

export interface ProductionLineCategory {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  productionLines?: ProductionLine[]
  status: 'active' | 'inactive'
  position: number
  createdAt: Date
  updatedAt: Date
}

// Blog Types
export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featuredImage?: string
  images?: string[]
  authorId: string
  author: User
  category: BlogCategory
  categoryId: string
  tags: string[]
  status: 'draft' | 'published' | 'archived'
  featured: boolean
  publishedAt?: Date
  seoTitle?: string
  seoDescription?: string
  readingTime?: number
  views: number
  createdAt: Date
  updatedAt: Date
}

export interface BlogCategory {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  posts?: BlogPost[]
  status: 'active' | 'inactive'
  position: number
  createdAt: Date
  updatedAt: Date
}

// Notification Types
export interface Notification {
  id: string
  userId: string
  type: keyof typeof NOTIFICATION_TYPES
  title: string
  message: string
  data?: Record<string, unknown>
  read: boolean
  createdAt: Date
  updatedAt: Date
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T = unknown> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form Types
export interface ContactForm {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
}

export interface NewsletterForm {
  email: string
}

export interface ServiceRequestForm {
  name: string
  email: string
  phone: string
  company?: string
  serviceId: string
  message: string
  preferredDate?: Date
}

export interface ProductionLineRequestForm {
  name: string
  email: string
  phone: string
  company: string
  productionLineId: string
  message: string
  budget?: string
}

// Search Types
export interface SearchFilters {
  query?: string
  category?: string
  minPrice?: number
  maxPrice?: number
  rating?: number
  inStock?: boolean
  featured?: boolean
  tags?: string[]
  sortBy?: 'name' | 'price' | 'rating' | 'created' | 'updated'
  sortOrder?: 'asc' | 'desc'
}

export interface SearchResult<T = unknown> {
  items: T[]
  total: number
  facets?: Record<string, unknown>
  suggestions?: string[]
}
