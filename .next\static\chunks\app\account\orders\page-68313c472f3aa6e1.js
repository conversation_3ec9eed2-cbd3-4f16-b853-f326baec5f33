(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[478],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(5155),s=t(2115),n=t(9708),i=t(2085),c=t(9434);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,asChild:l=!1,...o}=e,m=l?n.DX:"button";return(0,a.jsx)(m,{className:(0,c.cn)(d({variant:s,size:i,className:t})),ref:r,...o})});l.displayName="Button"},1493:(e,r,t)=>{Promise.resolve().then(t.bind(t,9328))},4817:(e,r,t)=>{"use strict";t.d(r,{LanguageProvider:()=>d,o:()=>l});var a=t(5155),s=t(2115);let n={EN:{code:"en",name:"English",dir:"ltr"},AR:{code:"ar",name:"العربية",dir:"rtl"}},i=(0,s.createContext)(void 0),c={en:{"nav.home":"Home","nav.shop":"Shop","nav.services":"Services","nav.production-lines":"Production Lines","nav.blog":"Blog","nav.about":"About","nav.contact":"Contact","auth.login":"Login","auth.register":"Register","auth.logout":"Logout","common.loading":"Loading...","common.error":"Error","common.success":"Success","common.cancel":"Cancel","common.save":"Save","common.edit":"Edit","common.delete":"Delete","common.search":"Search","common.filter":"Filter","common.sort":"Sort","common.view-all":"View All","common.read-more":"Read More","common.add-to-cart":"Add to Cart","common.buy-now":"Buy Now","common.out-of-stock":"Out of Stock","common.in-stock":"In Stock","common.price":"Price","common.quantity":"Quantity","common.total":"Total","common.subtotal":"Subtotal","common.shipping":"Shipping","common.tax":"Tax","common.discount":"Discount"},ar:{"nav.home":"الرئيسية","nav.shop":"المتجر","nav.services":"الخدمات","nav.production-lines":"خطوط الإنتاج","nav.blog":"المدونة","nav.about":"من نحن","nav.contact":"اتصل بنا","auth.login":"تسجيل الدخول","auth.register":"إنشاء حساب","auth.logout":"تسجيل الخروج","common.loading":"جاري التحميل...","common.error":"خطأ","common.success":"نجح","common.cancel":"إلغاء","common.save":"حفظ","common.edit":"تعديل","common.delete":"حذف","common.search":"بحث","common.filter":"تصفية","common.sort":"ترتيب","common.view-all":"عرض الكل","common.read-more":"اقرأ المزيد","common.add-to-cart":"أضف للسلة","common.buy-now":"اشتري الآن","common.out-of-stock":"غير متوفر","common.in-stock":"متوفر","common.price":"السعر","common.quantity":"الكمية","common.total":"المجموع","common.subtotal":"المجموع الفرعي","common.shipping":"الشحن","common.tax":"الضريبة","common.discount":"الخصم"}};function d(e){let{children:r}=e,[t,d]=(0,s.useState)("en"),[l,o]=(0,s.useState)("ltr"),m=e=>{d(e),o(n[e].dir),document.documentElement.lang=e,document.documentElement.dir=n[e].dir,localStorage.setItem("language",e)};return(0,s.useEffect)(()=>{let e=localStorage.getItem("language"),r=navigator.language.startsWith("ar")?"ar":"en";m(e||r)},[]),(0,a.jsx)(i.Provider,{value:{language:t,direction:l,setLanguage:m,t:(e,r)=>{let a=c[t][e]||e;return r&&Object.entries(r).forEach(e=>{let[r,t]=e;a=a.replace("{{".concat(r,"}}"),t)}),a}},children:(0,a.jsx)("div",{dir:l,className:"rtl"===l?"font-arabic":"font-sans",children:r})})}function l(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}},4997:(e,r,t)=>{"use strict";t.d(r,{O:()=>v});var a=t(5155),s=t(6874),n=t.n(s),i=t(5695),c=t(9434),d=t(4817),l=t(1007),o=t(4516),m=t(7108),x=t(1976),u=t(3861),h=t(1586),g=t(5525),p=t(381),y=t(9947),f=t(4835);function v(e){let{children:r}=e,t=(0,i.usePathname)(),{t:s}=(0,d.o)(),v=[{name:"Profile",href:"/account/profile",icon:l.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:o.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:m.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:x.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:u.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:h.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:g.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:p.A,description:"Account preferences and settings"}],j=[{name:"Help Center",href:"/help",icon:y.A},{name:"Contact Support",href:"/contact",icon:y.A}];return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,a.jsxs)("nav",{className:"p-2",children:[(0,a.jsx)("div",{className:"space-y-1",children:v.map(e=>{let r=t===e.href;return(0,a.jsxs)(n(),{href:e.href,className:(0,c.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",r?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(e.icon,{className:(0,c.cn)("mr-3 h-5 w-5",r?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{children:e.name}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:e.description})]})]},e.href)})}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"space-y-1",children:[j.map(e=>(0,a.jsxs)(n(),{href:e.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,a.jsx)(e.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),e.name]},e.href)),(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,a.jsx)(f.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:r})})]})})})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>c,wL:()=>m});var a=t(5155),s=t(2115),n=t(9434);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});i.displayName="Card";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...s})});c.displayName="CardHeader";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});d.displayName="CardTitle";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});l.displayName="CardDescription";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...s})});o.displayName="CardContent";let m=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...s})});m.displayName="CardFooter"},7313:(e,r,t)=>{"use strict";t.d(r,{Xi:()=>l,av:()=>o,j7:()=>d,tU:()=>c});var a=t(5155),s=t(2115),n=t(9255),i=t(9434);let c=n.bL,d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.B8,{ref:r,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});d.displayName=n.B8.displayName;let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.l9,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});l.displayName=n.l9.displayName;let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.UC,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});o.displayName=n.UC.displayName},9328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w});var a=t(5155),s=t(2115),n=t(6874),i=t.n(n),c=t(4997),d=t(285),l=t(6695),o=t(7313),m=t(4817),x=t(9434),u=t(4186),h=t(646),g=t(3904),p=t(9799),y=t(4861),f=t(7108),v=t(2657),j=t(1788),b=t(6151);let N=[{id:"ORD-2024-001",orderNumber:"ORD-2024-001",status:"delivered",paymentStatus:"succeeded",total:299.99,currency:"USD",items:[{id:"1",name:"Wireless Bluetooth Headphones",image:"/placeholder-product.jpg",price:149.99,quantity:1},{id:"2",name:"USB-C Charging Cable",image:"/placeholder-product.jpg",price:24.99,quantity:2}],createdAt:new Date("2024-01-15"),shippedAt:new Date("2024-01-16"),deliveredAt:new Date("2024-01-18"),trackingNumber:"1Z999AA1234567890"},{id:"ORD-2024-002",orderNumber:"ORD-2024-002",status:"shipped",paymentStatus:"succeeded",total:89.99,currency:"USD",items:[{id:"3",name:"Smartphone Case",image:"/placeholder-product.jpg",price:29.99,quantity:1},{id:"4",name:"Screen Protector",image:"/placeholder-product.jpg",price:19.99,quantity:3}],createdAt:new Date("2024-01-20"),shippedAt:new Date("2024-01-21"),trackingNumber:"1Z999AA1234567891"},{id:"ORD-2024-003",orderNumber:"ORD-2024-003",status:"processing",paymentStatus:"succeeded",total:199.99,currency:"USD",items:[{id:"5",name:"Wireless Mouse",image:"/placeholder-product.jpg",price:79.99,quantity:1},{id:"6",name:"Keyboard",image:"/placeholder-product.jpg",price:119.99,quantity:1}],createdAt:new Date("2024-01-22")}];function w(){let[e,r]=(0,s.useState)("all"),{t}=(0,m.o)(),n=e=>e&&"all"!==e?N.filter(r=>r.status===e):N,w=e=>{let{order:r}=e;return(0,a.jsxs)(l.Zp,{className:"mb-4",children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.ZB,{className:"text-lg",children:["Order #",r.orderNumber]}),(0,a.jsxs)(l.BT,{children:["Placed on ",(0,x.Yq)(r.createdAt)]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"pending":return"text-yellow-700 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900/30";case"confirmed":case"processing":return"text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/30";case"shipped":return"text-purple-700 bg-purple-100 dark:text-purple-300 dark:bg-purple-900/30";case"delivered":return"text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/30";case"cancelled":return"text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30";default:return"text-gray-700 bg-gray-100 dark:text-gray-300 dark:bg-gray-900/30"}})(r.status)),children:[(e=>{switch(e){case"pending":return(0,a.jsx)(u.A,{className:"h-4 w-4 text-yellow-500"});case"confirmed":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-blue-500"});case"processing":return(0,a.jsx)(g.A,{className:"h-4 w-4 text-blue-500"});case"shipped":return(0,a.jsx)(p.A,{className:"h-4 w-4 text-purple-500"});case"delivered":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-500"});case"cancelled":return(0,a.jsx)(y.A,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-500"})}})(r.status),(0,a.jsx)("span",{className:"ml-1 capitalize",children:r.status})]}),(0,a.jsx)("div",{className:"text-lg font-semibold mt-1",children:(0,x.$g)(r.total,{currency:r.currency})})]})]})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"space-y-3 mb-4",children:r.items.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Qty: ",e.quantity," \xd7 ",(0,x.$g)(e.price,{currency:r.currency})]})]})]},e.id))}),(r.shippedAt||r.deliveredAt||r.trackingNumber)&&(0,a.jsx)("div",{className:"border-t pt-4 mb-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[r.trackingNumber&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Tracking Number:"}),(0,a.jsx)("span",{className:"font-mono",children:r.trackingNumber})]}),r.shippedAt&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Shipped:"}),(0,a.jsx)("span",{children:(0,x.Yq)(r.shippedAt)})]}),r.deliveredAt&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Delivered:"}),(0,a.jsx)("span",{children:(0,x.Yq)(r.deliveredAt)})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(i(),{href:"/account/orders/".concat(r.id),children:(0,a.jsxs)(d.$,{size:"sm",variant:"outline",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"View Details"]})}),"delivered"===r.status&&(0,a.jsxs)(d.$,{size:"sm",variant:"outline",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Invoice"]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[r.trackingNumber&&"shipped"===r.status&&(0,a.jsxs)(d.$,{size:"sm",variant:"outline",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Track Package"]}),"delivered"===r.status&&(0,a.jsxs)(d.$,{size:"sm",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Reorder"]})]})]})]})]})};return(0,a.jsx)(c.O,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Order History"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Track and manage your orders"})]}),(0,a.jsxs)(o.tU,{value:e,onValueChange:r,className:"space-y-6",children:[(0,a.jsxs)(o.j7,{className:"grid w-full grid-cols-6",children:[(0,a.jsx)(o.Xi,{value:"all",children:"All Orders"}),(0,a.jsx)(o.Xi,{value:"pending",children:"Pending"}),(0,a.jsx)(o.Xi,{value:"processing",children:"Processing"}),(0,a.jsx)(o.Xi,{value:"shipped",children:"Shipped"}),(0,a.jsx)(o.Xi,{value:"delivered",children:"Delivered"}),(0,a.jsx)(o.Xi,{value:"cancelled",children:"Cancelled"})]}),(0,a.jsx)(o.av,{value:"all",className:"space-y-4",children:n().length>0?n().map(e=>(0,a.jsx)(w,{order:e},e.id)):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,a.jsx)(b.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No orders yet"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Start shopping to see your orders here"}),(0,a.jsx)(i(),{href:"/shop",children:(0,a.jsxs)(d.$,{children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Start Shopping"]})})]})})}),(0,a.jsx)(o.av,{value:"pending",className:"space-y-4",children:n("pending").length>0?n("pending").map(e=>(0,a.jsx)(w,{order:e},e.id)):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No pending orders"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"All your orders have been processed"})]})})}),(0,a.jsx)(o.av,{value:"processing",className:"space-y-4",children:n("processing").length>0?n("processing").map(e=>(0,a.jsx)(w,{order:e},e.id)):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No processing orders"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No orders are currently being processed"})]})})}),(0,a.jsx)(o.av,{value:"shipped",className:"space-y-4",children:n("shipped").length>0?n("shipped").map(e=>(0,a.jsx)(w,{order:e},e.id)):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No shipped orders"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No orders are currently shipped"})]})})}),(0,a.jsx)(o.av,{value:"delivered",className:"space-y-4",children:n("delivered").length>0?n("delivered").map(e=>(0,a.jsx)(w,{order:e},e.id)):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No delivered orders"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No orders have been delivered yet"})]})})}),(0,a.jsx)(o.av,{value:"cancelled",className:"space-y-4",children:n("cancelled").length>0?n("cancelled").map(e=>(0,a.jsx)(w,{order:e},e.id)):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,a.jsx)(y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No cancelled orders"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"You haven't cancelled any orders"})]})})})]})]})})}},9434:(e,r,t)=>{"use strict";t.d(r,{$g:()=>i,Yq:()=>c,cn:()=>n});var a=t(2596),s=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function i(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{currency:t="USD",notation:a="standard",locale:s="en-US"}=r;return new Intl.NumberFormat(s,{style:"currency",currency:t,notation:a,maximumFractionDigits:2}).format(e)}function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";return new Intl.DateTimeFormat(t,{month:"long",day:"numeric",year:"numeric",...r}).format(new Date(e))}}},e=>{e.O(0,[274,670,333,441,964,358],()=>e(e.s=1493)),_N_E=e.O()}]);