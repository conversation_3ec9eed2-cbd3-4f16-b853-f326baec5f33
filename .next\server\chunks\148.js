"use strict";exports.id=148,exports.ids=[148],exports.modules={44493:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},82096:(a,b,c)=>{c.d(b,{tU:()=>$,av:()=>ab,j7:()=>_,Xi:()=>aa});var d=c(60687),e=c(43210),f=c.t(e,2),g=c(70569),h=c(11273),i=c(9510),j=c(98599),k=c(66156),l=f[" useId ".trim().toString()]||(()=>void 0),m=0;function n(a){let[b,c]=e.useState(l());return(0,k.N)(()=>{a||c(a=>a??String(m++))},[a]),a||(b?`radix-${b}`:"")}var o=c(14163),p=c(13495),q=c(65551),r=e.createContext(void 0);function s(a){let b=e.useContext(r);return a||b||"ltr"}var t="rovingFocusGroup.onEntryFocus",u={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[w,x,y]=(0,i.N)(v),[z,A]=(0,h.A)(v,[y]),[B,C]=z(v),D=e.forwardRef((a,b)=>(0,d.jsx)(w.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,d.jsx)(w.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,d.jsx)(E,{...a,ref:b})})}));D.displayName=v;var E=e.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:i,currentTabStopId:k,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:m,onEntryFocus:n,preventScrollOnEntryFocus:r=!1,...w}=a,y=e.useRef(null),z=(0,j.s)(b,y),A=s(i),[C,D]=(0,q.i)({prop:k,defaultProp:l??null,onChange:m,caller:v}),[E,F]=e.useState(!1),G=(0,p.c)(n),H=x(c),J=e.useRef(!1),[K,L]=e.useState(0);return e.useEffect(()=>{let a=y.current;if(a)return a.addEventListener(t,G),()=>a.removeEventListener(t,G)},[G]),(0,d.jsx)(B,{scope:c,orientation:f,dir:A,loop:h,currentTabStopId:C,onItemFocus:e.useCallback(a=>D(a),[D]),onItemShiftTab:e.useCallback(()=>F(!0),[]),onFocusableItemAdd:e.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:e.useCallback(()=>L(a=>a-1),[]),children:(0,d.jsx)(o.sG.div,{tabIndex:E||0===K?-1:0,"data-orientation":f,...w,ref:z,style:{outline:"none",...a.style},onMouseDown:(0,g.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,g.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!E){let b=new CustomEvent(t,u);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=H().filter(a=>a.focusable);I([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),r)}}J.current=!1}),onBlur:(0,g.m)(a.onBlur,()=>F(!1))})})}),F="RovingFocusGroupItem",G=e.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:h=!1,tabStopId:i,children:j,...k}=a,l=n(),m=i||l,p=C(F,c),q=p.currentTabStopId===m,r=x(c),{onFocusableItemAdd:s,onFocusableItemRemove:t,currentTabStopId:u}=p;return e.useEffect(()=>{if(f)return s(),()=>t()},[f,s,t]),(0,d.jsx)(w.ItemSlot,{scope:c,id:m,focusable:f,active:h,children:(0,d.jsx)(o.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...k,ref:b,onMouseDown:(0,g.m)(a.onMouseDown,a=>{f?p.onItemFocus(m):a.preventDefault()}),onFocus:(0,g.m)(a.onFocus,()=>p.onItemFocus(m)),onKeyDown:(0,g.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return H[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=r().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>I(c))}}),children:"function"==typeof j?j({isCurrentTabStop:q,hasTabStop:null!=u}):j})})});G.displayName=F;var H={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var J=c(46059),K="Tabs",[L,M]=(0,h.A)(K,[A]),N=A(),[O,P]=L(K),Q=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:j="automatic",...k}=a,l=s(i),[m,p]=(0,q.i)({prop:e,onChange:f,defaultProp:g??"",caller:K});return(0,d.jsx)(O,{scope:c,baseId:n(),value:m,onValueChange:p,orientation:h,dir:l,activationMode:j,children:(0,d.jsx)(o.sG.div,{dir:l,"data-orientation":h,...k,ref:b})})});Q.displayName=K;var R="TabsList",S=e.forwardRef((a,b)=>{let{__scopeTabs:c,loop:e=!0,...f}=a,g=P(R,c),h=N(c);return(0,d.jsx)(D,{asChild:!0,...h,orientation:g.orientation,dir:g.dir,loop:e,children:(0,d.jsx)(o.sG.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:b})})});S.displayName=R;var T="TabsTrigger",U=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,disabled:f=!1,...h}=a,i=P(T,c),j=N(c),k=X(i.baseId,e),l=Y(i.baseId,e),m=e===i.value;return(0,d.jsx)(G,{asChild:!0,...j,focusable:!f,active:m,children:(0,d.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":l,"data-state":m?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:k,...h,ref:b,onMouseDown:(0,g.m)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():i.onValueChange(e)}),onKeyDown:(0,g.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&i.onValueChange(e)}),onFocus:(0,g.m)(a.onFocus,()=>{let a="manual"!==i.activationMode;m||f||!a||i.onValueChange(e)})})})});U.displayName=T;var V="TabsContent",W=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:f,forceMount:g,children:h,...i}=a,j=P(V,c),k=X(j.baseId,f),l=Y(j.baseId,f),m=f===j.value,n=e.useRef(m);return e.useEffect(()=>{let a=requestAnimationFrame(()=>n.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,d.jsx)(J.C,{present:g||m,children:({present:c})=>(0,d.jsx)(o.sG.div,{"data-state":m?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":k,hidden:!c,id:l,tabIndex:0,...i,ref:b,style:{...a.style,animationDuration:n.current?"0s":void 0},children:c&&h})})});function X(a,b){return`${a}-trigger-${b}`}function Y(a,b){return`${a}-content-${b}`}W.displayName=V;var Z=c(4780);let $=Q,_=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(S,{ref:c,className:(0,Z.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...b}));_.displayName=S.displayName;let aa=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(U,{ref:c,className:(0,Z.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...b}));aa.displayName=U.displayName;let ab=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(W,{ref:c,className:(0,Z.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));ab.displayName=W.displayName}};