"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { AdminLayout } from "@/components/layout/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import { emailService, EmailTemplate, EmailCampaign } from "@/lib/email-service"
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Mail,
  Send,
  Users,
  BarChart3,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Download,
  Upload,
  Play,
  Pause,
  Copy,
  TrendingUp,
  TrendingDown,
} from "lucide-react"

const templateCategories = ["All Categories", "transactional", "marketing", "notification"]
const templateStatuses = ["All Status", "active", "draft", "archived"]
const campaignStatuses = ["All Status", "draft", "scheduled", "sending", "sent", "paused"]

const getStatusColor = (status: string, type: "template" | "campaign") => {
  const colors = {
    template: {
      active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      draft: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      archived: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    },
    campaign: {
      draft: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
      scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      sending: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      sent: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      paused: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    }
  }
  return colors[type][status as keyof typeof colors[typeof type]] || "bg-gray-100 text-gray-800"
}

const getStatusIcon = (status: string, type: "template" | "campaign") => {
  const icons = {
    template: {
      active: CheckCircle,
      draft: AlertCircle,
      archived: XCircle,
    },
    campaign: {
      draft: AlertCircle,
      scheduled: Clock,
      sending: Play,
      sent: CheckCircle,
      paused: Pause,
    }
  }
  return icons[type][status as keyof typeof icons[typeof type]] || AlertCircle
}

export default function EmailManagementPage() {
  const [activeTab, setActiveTab] = useState<"templates" | "campaigns" | "analytics">("templates")
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [campaigns, setCampaigns] = useState<EmailCampaign[]>([])
  const [analytics, setAnalytics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedStatus, setSelectedStatus] = useState("All Status")
  
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [templatesData, campaignsData, analyticsData] = await Promise.all([
        emailService.getEmailTemplates(),
        emailService.getEmailCampaigns(),
        emailService.getEmailAnalytics()
      ])
      
      setTemplates(templatesData)
      setCampaigns(campaignsData)
      setAnalytics(analyticsData)
    } catch (error) {
      console.error('Failed to load email data:', error)
      toast({
        title: "Error",
        description: "Failed to load email data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSendCampaign = async (campaignId: string) => {
    try {
      const result = await emailService.sendMarketingEmail(campaignId)
      if (result.success) {
        toast({
          title: "Campaign Sent",
          description: "Email campaign has been sent successfully"
        })
        loadData()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send campaign",
        variant: "destructive"
      })
    }
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.subject.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All Categories" || template.category === selectedCategory
    const matchesStatus = selectedStatus === "All Status" || template.status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         campaign.subject.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = selectedStatus === "All Status" || campaign.status === selectedStatus
    
    return matchesSearch && matchesStatus
  })

  const TemplateCard = ({ template }: { template: EmailTemplate }) => {
    const StatusIcon = getStatusIcon(template.status, "template")

    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-2">{template.name}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {template.subject}
              </p>
              <div className="flex items-center space-x-2 mb-3">
                <Badge className={getStatusColor(template.status, "template")}>
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {template.status}
                </Badge>
                <Badge variant="outline" className="capitalize">
                  {template.category}
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
            <span>Variables: {template.variables.length}</span>
            <span>Updated {formatDate(template.updatedAt)}</span>
          </div>

          <div className="flex items-center space-x-2">
            <Link href={`/admin/emails/templates/${template.id}`}>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-1" />
                Preview
              </Button>
            </Link>
            <Link href={`/admin/emails/templates/${template.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
            </Link>
            <Button variant="outline" size="sm">
              <Copy className="h-4 w-4 mr-1" />
              Duplicate
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const CampaignCard = ({ campaign }: { campaign: EmailCampaign }) => {
    const StatusIcon = getStatusIcon(campaign.status, "campaign")
    const openRate = campaign.stats.sent > 0 ? (campaign.stats.opened / campaign.stats.sent * 100).toFixed(1) : 0
    const clickRate = campaign.stats.sent > 0 ? (campaign.stats.clicked / campaign.stats.sent * 100).toFixed(1) : 0

    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-2">{campaign.name}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {campaign.subject}
              </p>
              <div className="flex items-center space-x-2 mb-3">
                <Badge className={getStatusColor(campaign.status, "campaign")}>
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {campaign.status}
                </Badge>
                {campaign.scheduledAt && (
                  <Badge variant="outline">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(campaign.scheduledAt)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {campaign.stats.sent > 0 && (
            <div className="grid grid-cols-4 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-center">
                <p className="text-lg font-semibold">{campaign.stats.sent.toLocaleString()}</p>
                <p className="text-xs text-gray-500">Sent</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-semibold">{openRate}%</p>
                <p className="text-xs text-gray-500">Opened</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-semibold">{clickRate}%</p>
                <p className="text-xs text-gray-500">Clicked</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-semibold">{campaign.stats.unsubscribed}</p>
                <p className="text-xs text-gray-500">Unsubscribed</p>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
            <span>Recipients: {campaign.recipients.length || 'Segment'}</span>
            <span>Created {formatDate(campaign.createdAt)}</span>
          </div>

          <div className="flex items-center space-x-2">
            {campaign.status === 'draft' && (
              <Button 
                size="sm" 
                onClick={() => handleSendCampaign(campaign.id)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Send className="h-4 w-4 mr-1" />
                Send Now
              </Button>
            )}
            <Link href={`/admin/emails/campaigns/${campaign.id}`}>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-1" />
                View
              </Button>
            </Link>
            <Link href={`/admin/emails/campaigns/${campaign.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  const AnalyticsOverview = () => {
    if (!analytics) return null

    return (
      <div className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Sent
                  </p>
                  <p className="text-2xl font-bold">
                    {analytics.totalSent.toLocaleString()}
                  </p>
                </div>
                <Mail className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Open Rate
                  </p>
                  <p className="text-2xl font-bold">
                    {analytics.openRate}%
                  </p>
                </div>
                <Eye className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Click Rate
                  </p>
                  <p className="text-2xl font-bold">
                    {analytics.clickRate}%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Delivery Rate
                  </p>
                  <p className="text-2xl font-bold">
                    {analytics.deliveryRate}%
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Stats */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Email Performance</CardTitle>
              <CardDescription>Detailed engagement metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Delivered</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ width: `${analytics.deliveryRate}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{analytics.deliveryRate}%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span>Opened</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${analytics.openRate}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{analytics.openRate}%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span>Clicked</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-purple-500 h-2 rounded-full" 
                        style={{ width: `${analytics.clickRate}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{analytics.clickRate}%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span>Bounced</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-red-500 h-2 rounded-full" 
                        style={{ width: `${analytics.bounceRate}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{analytics.bounceRate}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Campaigns</CardTitle>
              <CardDescription>Latest campaign performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {campaigns.slice(0, 3).map((campaign) => (
                  <div key={campaign.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <p className="font-medium">{campaign.name}</p>
                      <p className="text-sm text-gray-500">
                        {campaign.stats.sent > 0 ? `${campaign.stats.sent} sent` : 'Not sent yet'}
                      </p>
                    </div>
                    <Badge className={getStatusColor(campaign.status, "campaign")}>
                      {campaign.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="space-y-8">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-64 animate-pulse"></div>
            ))}
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Email Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage email templates, campaigns, and analytics
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Link href="/admin/emails/templates/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Template
              </Button>
            </Link>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8">
            {[
              { id: 'templates', label: 'Templates', icon: Mail },
              { id: 'campaigns', label: 'Campaigns', icon: Send },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        {activeTab === 'analytics' ? (
          <AnalyticsOverview />
        ) : (
          <>
            {/* Filters */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder={`Search ${activeTab}...`}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center space-x-4">
                    {activeTab === 'templates' && (
                      <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                      >
                        {templateCategories.map((category) => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    )}

                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                    >
                      {(activeTab === 'templates' ? templateStatuses : campaignStatuses).map((status) => (
                        <option key={status} value={status}>
                          {status}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Items Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activeTab === 'templates' 
                ? filteredTemplates.map((template) => (
                    <TemplateCard key={template.id} template={template} />
                  ))
                : filteredCampaigns.map((campaign) => (
                    <CampaignCard key={campaign.id} campaign={campaign} />
                  ))
              }
            </div>

            {/* Empty State */}
            {((activeTab === 'templates' && filteredTemplates.length === 0) ||
              (activeTab === 'campaigns' && filteredCampaigns.length === 0)) && (
              <Card>
                <CardContent className="text-center py-12">
                  <Mail className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    No {activeTab} found
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Get started by creating your first {activeTab.slice(0, -1)}
                  </p>
                  <Link href={`/admin/emails/${activeTab}/new`}>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create {activeTab.slice(0, -1)}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  )
}
