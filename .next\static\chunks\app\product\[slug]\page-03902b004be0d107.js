(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[490],{4527:(e,s,a)=>{Promise.resolve().then(a.bind(a,5500))},5500:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>A});var r=a(5155),t=a(2115),i=a(6874),c=a.n(i),n=a(5695),l=a(285),d=a(6695),o=a(7313),x=a(4817),m=a(7481),h=a(9434),p=a(5169),u=a(8564),g=a(7712),f=a(4616),j=a(7809),v=a(1976),N=a(6516),y=a(9799),b=a(5525),k=a(133),w=a(1366);let C={id:"1",name:"Premium Wireless Headphones",slug:"premium-wireless-headphones",price:199.99,comparePrice:249.99,rating:4.8,reviewCount:124,images:["/placeholder-product.jpg","/placeholder-product.jpg","/placeholder-product.jpg","/placeholder-product.jpg"],category:"Electronics",brand:"AudioTech",sku:"AT-WH-001",inStock:!0,stockCount:15,description:"Experience premium sound quality with our latest wireless headphones featuring advanced noise cancellation technology.",features:["Active Noise Cancellation","30-hour battery life","Quick charge: 5 min = 3 hours","Premium leather ear cushions","Bluetooth 5.0 connectivity","Built-in microphone"],specifications:{"Driver Size":"40mm","Frequency Response":"20Hz - 20kHz",Impedance:"32 ohms",Weight:"250g",Connectivity:"Bluetooth 5.0, 3.5mm jack","Battery Life":"30 hours","Charging Time":"2 hours",Warranty:"2 years"},variants:[{id:"1",name:"Black",price:199.99,inStock:!0},{id:"2",name:"White",price:199.99,inStock:!0},{id:"3",name:"Silver",price:209.99,inStock:!1}]},S=[{id:"1",user:"John D.",rating:5,date:"2024-01-15",title:"Excellent sound quality!",content:"These headphones exceeded my expectations. The noise cancellation is fantastic and the battery life is amazing.",verified:!0},{id:"2",user:"Sarah M.",rating:4,date:"2024-01-10",title:"Great value for money",content:"Really happy with this purchase. Comfortable to wear for long periods and great sound quality.",verified:!0}];function A(e){let{params:s}=e,[a,i]=(0,t.useState)(0),[A,R]=(0,t.useState)(C.variants[0]),[W,D]=(0,t.useState)(1),[P,B]=(0,t.useState)("description"),{t:T}=(0,x.o)(),{toast:E}=(0,m.dj)();C||(0,n.notFound)();let F=C.comparePrice?Math.round((C.comparePrice-C.price)/C.comparePrice*100):0;return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-8",children:[(0,r.jsx)(c(),{href:"/",className:"hover:text-foreground",children:"Home"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(c(),{href:"/shop",className:"hover:text-foreground",children:"Shop"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(c(),{href:"/shop?category=".concat(C.category),className:"hover:text-foreground",children:C.category}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-foreground",children:C.name})]}),(0,r.jsxs)(c(),{href:"/shop",className:"inline-flex items-center text-sm text-gray-500 hover:text-foreground mb-6",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back to Shop"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"aspect-square bg-white dark:bg-gray-800 rounded-lg overflow-hidden border",children:(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center text-6xl",children:"\uD83D\uDCE6"})}),(0,r.jsx)("div",{className:"flex space-x-2",children:C.images.map((e,s)=>(0,r.jsx)("button",{onClick:()=>i(s),className:"w-20 h-20 bg-white dark:bg-gray-800 rounded-lg border-2 flex items-center justify-center text-2xl ".concat(a===s?"border-primary-500":"border-gray-200 dark:border-gray-700"),children:"\uD83D\uDCE6"},s))})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2",children:[(0,r.jsx)("span",{children:C.brand}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["SKU: ",C.sku]})]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:C.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsx)(u.A,{className:"h-5 w-5 ".concat(s<Math.floor(C.rating)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[C.rating," (",C.reviewCount," reviews)"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:(0,h.$g)(A.price)}),C.comparePrice&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-xl text-gray-500 line-through",children:(0,h.$g)(C.comparePrice)}),(0,r.jsxs)("span",{className:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm font-medium",children:["Save ",F,"%"]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:C.inStock?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-green-600 dark:text-green-400 text-sm",children:["In Stock (",C.stockCount," available)"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"h-2 w-2 bg-red-500 rounded-full"}),(0,r.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm",children:"Out of Stock"})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"Color"}),(0,r.jsx)("div",{className:"flex space-x-2",children:C.variants.map(e=>(0,r.jsxs)("button",{onClick:()=>R(e),disabled:!e.inStock,className:"px-4 py-2 border rounded-md text-sm font-medium transition-colors ".concat(A.id===e.id?"border-primary-500 bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300":e.inStock?"border-gray-300 dark:border-gray-600 hover:border-gray-400":"border-gray-200 dark:border-gray-700 text-gray-400 cursor-not-allowed"),children:[e.name,!e.inStock&&" (Out of Stock)"]},e.id))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"Quantity"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center border rounded-md",children:[(0,r.jsx)("button",{onClick:()=>D(Math.max(1,W-1)),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)("span",{className:"px-4 py-2 border-x",children:W}),(0,r.jsx)("button",{onClick:()=>D(Math.min(C.stockCount,W+1)),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["Total: ",(0,h.$g)(A.price*W)]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)(l.$,{onClick:()=>{E({title:"Added to Cart",description:"".concat(W,"x ").concat(C.name," (").concat(A.name,") added to cart")})},disabled:!C.inStock,className:"flex-1",size:"lg",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 mr-2"}),"Add to Cart"]}),(0,r.jsx)(l.$,{variant:"outline",onClick:()=>{E({title:"Added to Wishlist",description:"".concat(C.name," has been added to your wishlist")})},size:"lg",children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),(0,r.jsx)(l.$,{variant:"outline",onClick:()=>{navigator.share?navigator.share({title:C.name,text:C.description,url:window.location.href}):(navigator.clipboard.writeText(window.location.href),E({title:"Link Copied",description:"Product link copied to clipboard"}))},size:"lg",children:(0,r.jsx)(N.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 text-green-500"}),(0,r.jsx)("span",{className:"text-sm",children:"Free Shipping"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm",children:"2 Year Warranty"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(k.A,{className:"h-5 w-5 text-purple-500"}),(0,r.jsx)("span",{className:"text-sm",children:"30-Day Returns"})]})]})]})]}),(0,r.jsx)("div",{className:"mt-16",children:(0,r.jsxs)(o.tU,{value:P,onValueChange:B,children:[(0,r.jsxs)(o.j7,{className:"grid w-full grid-cols-3",children:[(0,r.jsx)(o.Xi,{value:"description",children:"Description"}),(0,r.jsx)(o.Xi,{value:"specifications",children:"Specifications"}),(0,r.jsxs)(o.Xi,{value:"reviews",children:["Reviews (",C.reviewCount,")"]})]}),(0,r.jsx)(o.av,{value:"description",className:"mt-8",children:(0,r.jsx)(d.Zp,{children:(0,r.jsxs)(d.Wu,{className:"p-6",children:[(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:C.description}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Key Features"}),(0,r.jsx)("ul",{className:"space-y-2",children:C.features.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-1.5 w-1.5 bg-primary-500 rounded-full"}),(0,r.jsx)("span",{children:e})]},s))})]})})}),(0,r.jsx)(o.av,{value:"specifications",className:"mt-8",children:(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(C.specifications).map(e=>{let[s,a]=e;return(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("span",{className:"font-medium",children:s}),(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:a})]},s)})})})})}),(0,r.jsx)(o.av,{value:"reviews",className:"mt-8",children:(0,r.jsxs)("div",{className:"space-y-6",children:[S.map(e=>(0,r.jsx)(d.Zp,{children:(0,r.jsxs)(d.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("span",{className:"font-medium",children:e.user}),e.verified&&(0,r.jsx)("span",{className:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-0.5 rounded text-xs",children:"Verified Purchase"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((s,a)=>(0,r.jsx)(u.A,{className:"h-4 w-4 ".concat(a<e.rating?"text-yellow-400 fill-current":"text-gray-300")},a))}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:e.date})]})]})}),(0,r.jsx)("h4",{className:"font-medium mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.content})]})},e.id)),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(l.$,{variant:"outline",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Write a Review"]})})]})})]})})]})})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>c,aR:()=>n,wL:()=>x});var r=a(5155),t=a(2115),i=a(9434);let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});c.displayName="Card";let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...t})});n.displayName="CardHeader";let l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});l.displayName="CardTitle";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...t})});d.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",a),...t})});o.displayName="CardContent";let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",a),...t})});x.displayName="CardFooter"},7313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>l,tU:()=>n});var r=a(5155),t=a(2115),i=a(9255),c=a(9434);let n=i.bL,l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.B8,{ref:s,className:(0,c.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...t})});l.displayName=i.B8.displayName;let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.l9,{ref:s,className:(0,c.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...t})});d.displayName=i.l9.displayName;let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.UC,{ref:s,className:(0,c.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...t})});o.displayName=i.UC.displayName}},e=>{e.O(0,[455,874,837,320,197,441,964,358],()=>e(e.s=4527)),_N_E=e.O()}]);