(()=>{var a={};a.id=612,a.ids=[612],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";function d(){return Math.random().toString(36).substring(2)+Date.now().toString(36)}function e(a){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)}c.d(b,{$C:()=>d,B9:()=>e})},12909:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{Nh:()=>o,ht:()=>n,kg:()=>m});var e=c(16467),f=c(36344),g=c(66699),h=c(13581),i=c(93139),j=c(94747),k=a([i]);i=(k.then?(await k)():k)[0];let o={adapter:(0,e.y)(j.zR),providers:[(0,f.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}}),(0,g.A)({clientId:process.env.FACEBOOK_CLIENT_ID,clientSecret:process.env.FACEBOOK_CLIENT_SECRET}),(0,h.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(a){if(!a?.email||!a?.password)throw Error("Email and password are required");let b=await j.zR.user.findUnique({where:{email:a.email}});if(!b||!b.password||!await i.default.compare(a.password,b.password))throw Error("Invalid email or password");return{id:b.id,email:b.email,name:b.name,avatar:b.avatar,role:b.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:a,user:b,account:c})=>(b&&(a.role=b.role,a.avatar=b.avatar),c&&(a.accessToken=c.access_token),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub,a.user.role=b.role,a.user.avatar=b.avatar,a.accessToken=b.accessToken),a),async signIn({user:a,account:b,profile:c}){if(b?.provider==="google"||b?.provider==="facebook")try{await j.zR.user.findUnique({where:{email:a.email}})||await j.zR.user.create({data:{email:a.email,name:a.name,avatar:a.image,emailVerified:new Date,role:"USER"}})}catch(a){return console.error("Error during social sign in:",a),!1}return!0},redirect:async({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b},events:{async signIn({user:a,account:b,profile:c,isNewUser:d}){console.log(`User ${a.email} signed in with ${b?.provider}`),a.id&&await j.zR.notification.create({data:{userId:a.id,type:"SYSTEM",title:"Welcome back!",message:"You have successfully signed in to your account.",data:{provider:b?.provider,timestamp:new Date().toISOString()}}}).catch(console.error)},async signOut({session:a,token:b}){console.log("User signed out")},async createUser({user:a}){console.log(`New user created: ${a.email}`),a.id&&await j.zR.notification.create({data:{userId:a.id,type:"SYSTEM",title:"Welcome to AIDEVCOMMERCE!",message:"Thank you for joining our platform. Explore our products and services.",data:{isWelcome:!0,timestamp:new Date().toISOString()}}}).catch(console.error)}},debug:!1};async function l(a){return await i.default.hash(a,12)}async function m(a){let b=await l(a.password);return await j.zR.user.create({data:{email:a.email,name:a.name,password:b,phone:a.phone,role:"USER"}})}async function n(a){return await j.zR.user.findUnique({where:{email:a},include:{addresses:!0,orders:{include:{items:{include:{product:!0,variant:!0}}}},wishlistItems:{include:{product:!0,variant:!0}},reviews:{include:{product:!0}},notifications:{orderBy:{createdAt:"desc"}}}})}d()}catch(a){d(a)}})},15997:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(46264),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},distDir:".next",projectDir:"",resolvedPagePath:"E:\\aidevcommerce\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/api/auth/register/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46264:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{POST:()=>k});var e=c(32190),f=c(50639),g=c(14250),h=c(12909),i=c(10974),j=a([h]);h=(j.then?(await j)():j)[0];let l=f.Ik({name:f.Yj().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters"),email:f.Yj().email("Invalid email address"),password:f.Yj().min(8,"Password must be at least 8 characters").max(128,"Password must be less than 128 characters"),phone:f.Yj().optional()});async function k(a){try{let b=await a.json(),c=l.parse(b);if(!(0,i.B9)(c.email))return e.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(await (0,h.ht)(c.email))return e.NextResponse.json({success:!1,error:"User with this email already exists"},{status:409});let{password:d,...f}=await (0,h.kg)(c);return e.NextResponse.json({success:!0,data:f,message:"User created successfully"},{status:201})}catch(a){if(console.error("Registration error:",a),a instanceof g.G)return e.NextResponse.json({success:!1,error:"Validation failed",errors:a.flatten().fieldErrors},{status:400});return e.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}d()}catch(a){d(a)}})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93139:a=>{"use strict";a.exports=import("bcryptjs")},94747:(a,b,c)=>{"use strict";c.d(b,{zR:()=>e});let d=require("@prisma/client"),e=globalThis.prisma??new d.PrismaClient({log:["error"],errorFormat:"pretty"})},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,55,116],()=>b(b.s=15997));module.exports=c})();