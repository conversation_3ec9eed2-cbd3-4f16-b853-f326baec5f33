"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { formatDate } from "@/lib/utils"
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Calendar,
  User,
  Eye,
  MessageCircle,
  BookOpen,
  TrendingUp,
  Clock,
  Tag,
  ArrowRight,
  Rss,
} from "lucide-react"

// Mock blog posts data
const mockBlogPosts = [
  {
    id: "1",
    title: "The Future of AI in E-commerce: Transforming Online Shopping",
    slug: "future-ai-ecommerce-transforming-online-shopping",
    excerpt: "Explore how artificial intelligence is revolutionizing the e-commerce landscape, from personalized recommendations to automated customer service.",
    content: "Full article content here...",
    category: "Technology",
    author: {
      name: "<PERSON>",
      avatar: "/author-1.jpg",
      bio: "AI Research Specialist"
    },
    publishedAt: new Date("2024-01-15"),
    readTime: "8 min read",
    views: 1250,
    comments: 23,
    featured: true,
    trending: true,
    image: "/blog-1.jpg",
    tags: ["AI", "E-commerce", "Machine Learning", "Technology"]
  },
  {
    id: "2",
    title: "Sustainable Manufacturing: Green Production Lines for the Future",
    slug: "sustainable-manufacturing-green-production-lines",
    excerpt: "Discover how modern production lines are becoming more environmentally friendly while maintaining efficiency and profitability.",
    content: "Full article content here...",
    category: "Manufacturing",
    author: {
      name: "Michael Chen",
      avatar: "/author-2.jpg",
      bio: "Manufacturing Engineer"
    },
    publishedAt: new Date("2024-01-12"),
    readTime: "6 min read",
    views: 890,
    comments: 15,
    featured: false,
    trending: true,
    image: "/blog-2.jpg",
    tags: ["Sustainability", "Manufacturing", "Green Technology", "Environment"]
  },
  {
    id: "3",
    title: "Digital Transformation in Small Businesses: A Complete Guide",
    slug: "digital-transformation-small-businesses-guide",
    excerpt: "Learn how small businesses can successfully navigate digital transformation with practical strategies and real-world examples.",
    content: "Full article content here...",
    category: "Business",
    author: {
      name: "Emily Rodriguez",
      avatar: "/author-3.jpg",
      bio: "Business Consultant"
    },
    publishedAt: new Date("2024-01-10"),
    readTime: "10 min read",
    views: 2100,
    comments: 34,
    featured: true,
    trending: false,
    image: "/blog-3.jpg",
    tags: ["Digital Transformation", "Small Business", "Strategy", "Technology"]
  },
  {
    id: "4",
    title: "UX Design Trends 2024: What's Shaping User Experience",
    slug: "ux-design-trends-2024-user-experience",
    excerpt: "Stay ahead of the curve with the latest UX design trends that are defining user experience in 2024 and beyond.",
    content: "Full article content here...",
    category: "Design",
    author: {
      name: "Alex Thompson",
      avatar: "/author-4.jpg",
      bio: "UX Designer"
    },
    publishedAt: new Date("2024-01-08"),
    readTime: "7 min read",
    views: 1560,
    comments: 28,
    featured: false,
    trending: true,
    image: "/blog-4.jpg",
    tags: ["UX Design", "Design Trends", "User Experience", "Interface"]
  },
  {
    id: "5",
    title: "Cloud Infrastructure Best Practices for Scalable Applications",
    slug: "cloud-infrastructure-best-practices-scalable-applications",
    excerpt: "Master the fundamentals of cloud infrastructure design with proven best practices for building scalable, reliable applications.",
    content: "Full article content here...",
    category: "Technology",
    author: {
      name: "David Kim",
      avatar: "/author-5.jpg",
      bio: "Cloud Architect"
    },
    publishedAt: new Date("2024-01-05"),
    readTime: "12 min read",
    views: 980,
    comments: 19,
    featured: false,
    trending: false,
    image: "/blog-5.jpg",
    tags: ["Cloud Computing", "Infrastructure", "Scalability", "DevOps"]
  },
  {
    id: "6",
    title: "The Rise of Voice Commerce: Shopping with Voice Assistants",
    slug: "rise-voice-commerce-shopping-voice-assistants",
    excerpt: "Explore the growing trend of voice commerce and how businesses can optimize for voice-based shopping experiences.",
    content: "Full article content here...",
    category: "E-commerce",
    author: {
      name: "Lisa Wang",
      avatar: "/author-6.jpg",
      bio: "E-commerce Strategist"
    },
    publishedAt: new Date("2024-01-03"),
    readTime: "5 min read",
    views: 720,
    comments: 12,
    featured: false,
    trending: false,
    image: "/blog-6.jpg",
    tags: ["Voice Commerce", "E-commerce", "Voice Assistants", "Shopping"]
  }
]

const categories = [
  "All Categories",
  "Technology",
  "Manufacturing",
  "Business",
  "Design",
  "E-commerce",
  "Marketing",
]

const sortOptions = [
  { value: "latest", label: "Latest Posts" },
  { value: "popular", label: "Most Popular" },
  { value: "trending", label: "Trending" },
  { value: "featured", label: "Featured" },
]

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [sortBy, setSortBy] = useState("latest")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  
  const { t } = useLanguage()

  const filteredPosts = mockBlogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === "All Categories" || post.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const sortedPosts = filteredPosts.sort((a, b) => {
    switch (sortBy) {
      case "popular":
        return b.views - a.views
      case "trending":
        return (b.trending ? 1 : 0) - (a.trending ? 1 : 0)
      case "featured":
        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0)
      case "latest":
      default:
        return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    }
  })

  const featuredPosts = mockBlogPosts.filter(post => post.featured).slice(0, 3)
  const trendingPosts = mockBlogPosts.filter(post => post.trending).slice(0, 5)

  const BlogPostCard = ({ post, featured = false }: { post: typeof mockBlogPosts[0], featured?: boolean }) => (
    <Card className={`group hover:shadow-xl transition-all duration-300 overflow-hidden ${featured ? 'md:col-span-2' : ''}`}>
      <div className="relative">
        {/* Post Image */}
        <div className={`${featured ? 'aspect-video' : 'aspect-[4/3]'} bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900 dark:to-secondary-900 flex items-center justify-center`}>
          <BookOpen className={`${featured ? 'h-16 w-16' : 'h-12 w-12'} text-primary-600 dark:text-primary-400`} />
        </div>
        
        {/* Badges */}
        <div className="absolute top-3 left-3 space-y-1">
          {post.featured && (
            <Badge className="bg-primary-500 text-white">Featured</Badge>
          )}
          {post.trending && (
            <Badge className="bg-orange-500 text-white">Trending</Badge>
          )}
        </div>

        {/* Category */}
        <div className="absolute top-3 right-3">
          <Badge variant="secondary">{post.category}</Badge>
        </div>
      </div>

      <CardContent className={`${featured ? 'p-8' : 'p-6'}`}>
        <div className="space-y-4">
          {/* Meta Info */}
          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <User className="h-4 w-4" />
              <span>{post.author.name}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{post.readTime}</span>
            </div>
          </div>

          {/* Title */}
          <h3 className={`${featured ? 'text-2xl' : 'text-xl'} font-semibold group-hover:text-primary-600 transition-colors line-clamp-2`}>
            <Link href={`/blog/${post.slug}`}>
              {post.title}
            </Link>
          </h3>

          {/* Excerpt */}
          <p className={`text-gray-600 dark:text-gray-400 ${featured ? 'text-lg line-clamp-3' : 'text-sm line-clamp-2'}`}>
            {post.excerpt}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {post.tags.slice(0, featured ? 4 : 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                <Tag className="h-3 w-3 mr-1" />
                {tag}
              </Badge>
            ))}
          </div>

          {/* Stats and CTA */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <Eye className="h-4 w-4" />
                <span>{post.views}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MessageCircle className="h-4 w-4" />
                <span>{post.comments}</span>
              </div>
            </div>
            
            <Link href={`/blog/${post.slug}`}>
              <Button variant="ghost" size="sm" className="group">
                Read More
                <ArrowRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <BookOpen className="h-4 w-4 mr-2" />
            Knowledge Hub
          </Badge>
          <h1 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Our Blog
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Stay updated with the latest insights, trends, and best practices in technology, business, and innovation.
          </p>
        </div>

        {/* Featured Posts */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">Featured Articles</h2>
            <Button variant="outline" size="sm">
              <Rss className="h-4 w-4 mr-2" />
              Subscribe
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredPosts.map((post, index) => (
              <BlogPostCard key={post.id} post={post} featured={index === 0} />
            ))}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* Filter Bar */}
          <div className="flex flex-wrap items-center justify-center gap-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              {sortedPosts.length} articles found
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Blog Posts Grid */}
            <div className={`grid gap-8 ${
              viewMode === "grid" 
                ? "grid-cols-1 md:grid-cols-2" 
                : "grid-cols-1"
            }`}>
              {sortedPosts.map((post) => (
                <BlogPostCard key={post.id} post={post} />
              ))}
            </div>

            {/* Empty State */}
            {sortedPosts.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No articles found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Try adjusting your search or filter criteria
                </p>
                <Button onClick={() => {
                  setSearchQuery("")
                  setSelectedCategory("All Categories")
                }}>
                  Clear Filters
                </Button>
              </div>
            )}

            {/* Load More */}
            {sortedPosts.length > 0 && (
              <div className="text-center mt-12">
                <Button variant="outline" size="lg">
                  Load More Articles
                </Button>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Trending Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Trending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {trendingPosts.map((post, index) => (
                    <div key={post.id} className="flex space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                        <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">
                          {index + 1}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <Link 
                          href={`/blog/${post.slug}`}
                          className="text-sm font-medium hover:text-primary-600 transition-colors line-clamp-2"
                        >
                          {post.title}
                        </Link>
                        <div className="flex items-center space-x-2 mt-1 text-xs text-gray-500">
                          <Eye className="h-3 w-3" />
                          <span>{post.views}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {categories.slice(1).map((category) => {
                    const count = mockBlogPosts.filter(post => post.category === category).length
                    return (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className={`w-full flex items-center justify-between p-2 rounded-lg text-sm transition-colors ${
                          selectedCategory === category
                            ? "bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300"
                            : "hover:bg-gray-100 dark:hover:bg-gray-800"
                        }`}
                      >
                        <span>{category}</span>
                        <Badge variant="secondary" className="text-xs">
                          {count}
                        </Badge>
                      </button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter */}
            <Card>
              <CardHeader>
                <CardTitle>Stay Updated</CardTitle>
                <CardDescription>
                  Get the latest articles delivered to your inbox
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="w-full px-3 py-2 border rounded-lg text-sm"
                  />
                  <Button className="w-full" size="sm">
                    Subscribe
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
