import Head from 'next/head'
import { useRouter } from 'next/router'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
  price?: {
    amount: number
    currency: string
  }
  availability?: 'in_stock' | 'out_of_stock' | 'preorder'
  brand?: string
  category?: string
  sku?: string
  gtin?: string
  mpn?: string
  condition?: 'new' | 'used' | 'refurbished'
  rating?: {
    value: number
    count: number
    bestRating?: number
    worstRating?: number
  }
  noIndex?: boolean
  noFollow?: boolean
  canonical?: string
}

const defaultSEO = {
  title: 'AIDEVCOMMERCE - Advanced E-commerce Platform',
  description: 'Discover our comprehensive e-commerce platform with AI-powered recommendations, professional services, and industrial solutions. Shop smart with AIDEVCOMMERCE.',
  keywords: ['e-commerce', 'online shopping', 'AI recommendations', 'professional services', 'industrial equipment', 'technology'],
  image: '/og-image.jpg',
  type: 'website' as const,
  siteName: 'AIDEVCOMMERCE',
  locale: 'en_US',
  twitterHandle: '@aidevcommerce',
}

export function SEOHead({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  price,
  availability,
  brand,
  category,
  sku,
  gtin,
  mpn,
  condition = 'new',
  rating,
  noIndex = false,
  noFollow = false,
  canonical,
}: SEOProps) {
  const router = useRouter()
  
  // Construct full URL
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aidevcommerce.com'
  const fullUrl = url || `${baseUrl}${router.asPath}`
  const canonicalUrl = canonical || fullUrl
  
  // Construct title
  const fullTitle = title 
    ? `${title} | ${defaultSEO.siteName}`
    : defaultSEO.title
  
  // Construct description
  const metaDescription = description || defaultSEO.description
  
  // Construct keywords
  const allKeywords = [...defaultSEO.keywords, ...keywords].join(', ')
  
  // Construct image URL
  const imageUrl = image 
    ? image.startsWith('http') ? image : `${baseUrl}${image}`
    : `${baseUrl}${defaultSEO.image}`
  
  // Robots meta
  const robotsContent = []
  if (noIndex) robotsContent.push('noindex')
  if (noFollow) robotsContent.push('nofollow')
  if (robotsContent.length === 0) robotsContent.push('index', 'follow')
  
  // Generate structured data
  const generateStructuredData = () => {
    const baseStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: defaultSEO.siteName,
      url: baseUrl,
      description: defaultSEO.description,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${baseUrl}/shop?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    }

    if (type === 'product' && price) {
      return {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: title,
        description: metaDescription,
        image: imageUrl,
        url: fullUrl,
        sku: sku,
        gtin: gtin,
        mpn: mpn,
        brand: brand ? {
          '@type': 'Brand',
          name: brand
        } : undefined,
        category: category,
        offers: {
          '@type': 'Offer',
          price: price.amount,
          priceCurrency: price.currency,
          availability: `https://schema.org/${availability === 'in_stock' ? 'InStock' : availability === 'out_of_stock' ? 'OutOfStock' : 'PreOrder'}`,
          itemCondition: `https://schema.org/${condition === 'new' ? 'NewCondition' : condition === 'used' ? 'UsedCondition' : 'RefurbishedCondition'}`,
          seller: {
            '@type': 'Organization',
            name: defaultSEO.siteName
          }
        },
        aggregateRating: rating ? {
          '@type': 'AggregateRating',
          ratingValue: rating.value,
          reviewCount: rating.count,
          bestRating: rating.bestRating || 5,
          worstRating: rating.worstRating || 1
        } : undefined
      }
    }

    if (type === 'article') {
      return {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: title,
        description: metaDescription,
        image: imageUrl,
        url: fullUrl,
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        author: author ? {
          '@type': 'Person',
          name: author
        } : undefined,
        publisher: {
          '@type': 'Organization',
          name: defaultSEO.siteName,
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/logo.png`
          }
        },
        articleSection: section,
        keywords: tags.join(', ')
      }
    }

    return baseStructuredData
  }

  const structuredData = generateStructuredData()

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={allKeywords} />
      <meta name="author" content={author || defaultSEO.siteName} />
      <meta name="robots" content={robotsContent.join(', ')} />
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Viewport and Mobile */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={defaultSEO.siteName} />
      
      {/* Favicon and Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <meta name="theme-color" content="#6366f1" />
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:site_name" content={defaultSEO.siteName} />
      <meta property="og:locale" content={defaultSEO.locale} />
      
      {/* Article specific Open Graph */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && author && (
        <meta property="article:author" content={author} />
      )}
      {type === 'article' && section && (
        <meta property="article:section" content={section} />
      )}
      {type === 'article' && tags.map((tag, index) => (
        <meta key={index} property="article:tag" content={tag} />
      ))}
      
      {/* Product specific Open Graph */}
      {type === 'product' && price && (
        <>
          <meta property="product:price:amount" content={price.amount.toString()} />
          <meta property="product:price:currency" content={price.currency} />
        </>
      )}
      {type === 'product' && availability && (
        <meta property="product:availability" content={availability} />
      )}
      {type === 'product' && condition && (
        <meta property="product:condition" content={condition} />
      )}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={defaultSEO.twitterHandle} />
      <meta name="twitter:creator" content={defaultSEO.twitterHandle} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={imageUrl} />
      
      {/* Additional Meta Tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="msapplication-TileColor" content="#6366f1" />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://images.unsplash.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//images.unsplash.com" />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData, null, 2)
        }}
      />
      
      {/* Additional structured data for organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Organization',
            name: defaultSEO.siteName,
            url: baseUrl,
            logo: `${baseUrl}/logo.png`,
            description: defaultSEO.description,
            contactPoint: {
              '@type': 'ContactPoint',
              telephone: '******-123-4567',
              contactType: 'customer service',
              availableLanguage: ['English', 'Arabic']
            },
            sameAs: [
              'https://twitter.com/aidevcommerce',
              'https://facebook.com/aidevcommerce',
              'https://linkedin.com/company/aidevcommerce',
              'https://instagram.com/aidevcommerce'
            ]
          }, null, 2)
        }}
      />
    </Head>
  )
}
