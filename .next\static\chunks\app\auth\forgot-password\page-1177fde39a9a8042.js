(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{646:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2523:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(5155),a=s(2115),l=s(9434);let i=a.forwardRef((e,r)=>{let{className:s,type:a,...i}=e;return(0,t.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...i})});i.displayName="Input"},5057:(e,r,s)=>{"use strict";s.d(r,{J:()=>c});var t=s(5155),a=s(2115),l=s(968),i=s(2085),n=s(9434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)(l.b,{ref:r,className:(0,n.cn)(d(),s),...a})});c.displayName=l.b.displayName},5169:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6195:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var t=s(5155),a=s(2115),l=s(6874),i=s.n(l),n=s(2177),d=s(221),c=s(8309),o=s(285),m=s(2523),u=s(5057),x=s(6695),f=s(7481),h=s(4817),p=s(646),y=s(5169),g=s(8883);let N=c.Ik({email:c.Yj().email("Invalid email address")});function j(){let[e,r]=(0,a.useState)(!1),[s,l]=(0,a.useState)(!1),{toast:c}=(0,f.dj)(),{t:j}=(0,h.o)(),{register:w,handleSubmit:v,formState:{errors:b},getValues:k}=(0,n.mN)({resolver:(0,d.u)(N)}),C=async e=>{r(!0);try{let r=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await r.json();s.success?(l(!0),c({title:j("common.success"),description:"Password reset instructions sent to your email"})):c({title:j("common.error"),description:s.error||"Failed to send reset email",variant:"destructive"})}catch(e){c({title:j("common.error"),description:"An error occurred while sending reset email",variant:"destructive"})}finally{r(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsxs)(x.Zp,{className:"w-full",children:[(0,t.jsxs)(x.aR,{className:"space-y-1 text-center",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900",children:(0,t.jsx)(p.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),(0,t.jsx)(x.ZB,{className:"text-2xl",children:"Check your email"}),(0,t.jsxs)(x.BT,{children:["We've sent password reset instructions to"," ",(0,t.jsx)("span",{className:"font-medium",children:k("email")})]})]}),(0,t.jsx)(x.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,t.jsx)("p",{children:"Didn't receive the email? Check your spam folder or"}),(0,t.jsx)("button",{onClick:()=>l(!1),className:"text-primary hover:underline",children:"try again"})]})}),(0,t.jsx)(x.wL,{children:(0,t.jsx)(i(),{href:"/auth/login",className:"w-full",children:(0,t.jsx)(o.$,{variant:"outline",className:"w-full",children:"Back to login"})})})]})})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)(i(),{href:"/auth/login",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Back to login"]})}),(0,t.jsxs)(x.Zp,{className:"w-full",children:[(0,t.jsxs)(x.aR,{className:"space-y-1",children:[(0,t.jsx)(x.ZB,{className:"text-2xl text-center",children:"Forgot password?"}),(0,t.jsx)(x.BT,{className:"text-center",children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,t.jsx)(x.Wu,{className:"space-y-4",children:(0,t.jsxs)("form",{onSubmit:v(C),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(m.p,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...w("email")})]}),b.email&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:b.email.message})]}),(0,t.jsx)(o.$,{type:"submit",className:"w-full",disabled:e,children:e?"Sending...":"Send reset instructions"})]})}),(0,t.jsx)(x.wL,{children:(0,t.jsxs)("p",{className:"text-center text-sm text-muted-foreground w-full",children:["Remember your password?"," ",(0,t.jsx)(i(),{href:"/auth/login",className:"text-primary hover:underline",children:j("auth.login")})]})})]})]})})}},6350:(e,r,s)=>{Promise.resolve().then(s.bind(s,6195))},6695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>m});var t=s(5155),a=s(2115),l=s(9434);let i=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let n=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});n.displayName="CardHeader";let d=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent";let m=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})});m.displayName="CardFooter"},8883:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{e.O(0,[274,804,197,441,964,358],()=>e(e.s=6350)),_N_E=e.O()}]);