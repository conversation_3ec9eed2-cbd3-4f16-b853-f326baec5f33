(()=>{var a={};a.id=298,a.ids=[298],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11504:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\aidevcommerce\\\\src\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\app\\account\\page.tsx","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20477:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,11504)),"E:\\aidevcommerce\\src\\app\\account\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,15515)),"E:\\aidevcommerce\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\aidevcommerce\\src\\app\\account\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/account/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32186:(a,b,c)=>{"use strict";c.d(b,{O:()=>t});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(4780),i=c(98436),j=c(58869),k=c(97992),l=c(19080),m=c(67760),n=c(97051),o=c(85778),p=c(99891),q=c(84027);let r=(0,c(62688).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var s=c(40083);function t({children:a}){let b=(0,g.usePathname)(),{t:c}=(0,i.o)(),e=[{name:"Profile",href:"/account/profile",icon:j.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:k.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:l.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:m.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:n.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:o.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:p.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:q.A,description:"Account preferences and settings"}];return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,d.jsx)(j.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,d.jsxs)("nav",{className:"p-2",children:[(0,d.jsx)("div",{className:"space-y-1",children:e.map(a=>{let c=b===a.href;return(0,d.jsxs)(f(),{href:a.href,className:(0,h.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",c?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,d.jsx)(a.icon,{className:(0,h.cn)("mr-3 h-5 w-5",c?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{children:a.name}),(0,d.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:a.description})]})]},a.href)})}),(0,d.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"space-y-1",children:[[{name:"Help Center",href:"/help",icon:r},{name:"Contact Support",href:"/contact",icon:r}].map(a=>(0,d.jsxs)(f(),{href:a.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,d.jsx)(a.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),a.name]},a.href)),(0,d.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,d.jsx)(s.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:a})})]})})})}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},53718:(a,b,c)=>{Promise.resolve().then(c.bind(c,66882))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66882:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>C});var d=c(60687),e=c(85814),f=c.n(e),g=c(32186),h=c(29523),i=c(44493),j=c(98436),k=c(4780),l=c(19080),m=c(62688);let n=(0,m.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var o=c(67760),p=c(64398);let q=(0,m.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var r=c(5336),s=c(48730),t=c(71057),u=c(97992),v=c(85778),w=c(97051);let x=(0,m.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),y={totalOrders:12,totalSpent:1299.99,wishlistItems:8,savedAddresses:3,loyaltyPoints:2450,memberSince:new Date("2023-06-15")},z=[{id:"ORD-2024-001",orderNumber:"ORD-2024-001",status:"delivered",total:299.99,currency:"USD",createdAt:new Date("2024-01-15"),itemCount:3},{id:"ORD-2024-002",orderNumber:"ORD-2024-002",status:"shipped",total:89.99,currency:"USD",createdAt:new Date("2024-01-20"),itemCount:2}],A=[{id:"1",name:"Premium Wireless Headphones",price:199.99,currency:"USD",image:"/placeholder-product.jpg",inStock:!0},{id:"2",name:"Smart Fitness Watch",price:299.99,currency:"USD",image:"/placeholder-product.jpg",inStock:!1}],B=[{id:"1",type:"order",title:"Order Delivered",message:"Your order #ORD-2024-001 has been delivered",createdAt:new Date("2024-01-18"),read:!1},{id:"2",type:"promotion",title:"Special Offer",message:"Get 20% off on your next purchase",createdAt:new Date("2024-01-17"),read:!0}];function C(){let{t:a}=(0,j.o)();return(0,d.jsx)(g.O,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Welcome back, John!"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Here's what's happening with your account"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,d.jsx)(i.Zp,{children:(0,d.jsx)(i.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Orders"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.totalOrders})]}),(0,d.jsx)(l.A,{className:"h-8 w-8 text-primary-500"})]})})}),(0,d.jsx)(i.Zp,{children:(0,d.jsx)(i.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Spent"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(0,k.$g)(y.totalSpent)})]}),(0,d.jsx)(n,{className:"h-8 w-8 text-green-500"})]})})}),(0,d.jsx)(i.Zp,{children:(0,d.jsx)(i.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Wishlist Items"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.wishlistItems})]}),(0,d.jsx)(o.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,d.jsx)(i.Zp,{children:(0,d.jsx)(i.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Loyalty Points"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.loyaltyPoints.toLocaleString()})]}),(0,d.jsx)(p.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,d.jsxs)(i.Zp,{children:[(0,d.jsxs)(i.aR,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(i.ZB,{className:"flex items-center",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 mr-2"}),"Recent Orders"]}),(0,d.jsx)(f(),{href:"/account/orders",children:(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",children:["View All",(0,d.jsx)(q,{className:"h-4 w-4 ml-1"})]})})]}),(0,d.jsx)(i.BT,{children:"Your latest order activity"})]}),(0,d.jsx)(i.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[z.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"h-10 w-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center",children:"delivered"===a.status?(0,d.jsx)(r.A,{className:"h-5 w-5 text-green-500"}):(0,d.jsx)(s.A,{className:"h-5 w-5 text-blue-500"})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"font-medium text-sm",children:["#",a.orderNumber]}),(0,d.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[a.itemCount," items • ",(0,k.Yq)(a.createdAt)]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"font-medium",children:(0,k.$g)(a.total,{currency:a.currency})}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 capitalize",children:a.status})]})]},a.id)),0===z.length&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(t.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No orders yet"}),(0,d.jsx)(f(),{href:"/shop",children:(0,d.jsx)(h.$,{className:"mt-2",children:"Start Shopping"})})]})]})})]}),(0,d.jsxs)(i.Zp,{children:[(0,d.jsxs)(i.aR,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(i.ZB,{className:"flex items-center",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Wishlist"]}),(0,d.jsx)(f(),{href:"/account/wishlist",children:(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",children:["View All",(0,d.jsx)(q,{className:"h-4 w-4 ml-1"})]})})]}),(0,d.jsx)(i.BT,{children:"Items you've saved for later"})]}),(0,d.jsx)(i.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[A.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center",children:(0,d.jsx)(l.A,{className:"h-6 w-6 text-gray-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-sm",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(0,k.$g)(a.price,{currency:a.currency})})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:a.inStock?(0,d.jsx)(h.$,{size:"sm",children:"Add to Cart"}):(0,d.jsx)(h.$,{size:"sm",variant:"outline",disabled:!0,children:"Out of Stock"})})]},a.id)),0===A.length&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No wishlist items yet"}),(0,d.jsx)(f(),{href:"/shop",children:(0,d.jsx)(h.$,{className:"mt-2",children:"Browse Products"})})]})]})})]}),(0,d.jsxs)(i.Zp,{children:[(0,d.jsxs)(i.aR,{children:[(0,d.jsx)(i.ZB,{children:"Quick Actions"}),(0,d.jsx)(i.BT,{children:"Manage your account settings"})]}),(0,d.jsx)(i.Wu,{children:(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)(f(),{href:"/account/profile",children:(0,d.jsxs)(h.$,{variant:"outline",className:"w-full justify-start",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})}),(0,d.jsx)(f(),{href:"/account/addresses",children:(0,d.jsxs)(h.$,{variant:"outline",className:"w-full justify-start",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Addresses"]})}),(0,d.jsx)(f(),{href:"/account/payment-methods",children:(0,d.jsxs)(h.$,{variant:"outline",className:"w-full justify-start",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Payment"]})}),(0,d.jsx)(f(),{href:"/account/security",children:(0,d.jsxs)(h.$,{variant:"outline",className:"w-full justify-start",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Security"]})})]})})]}),(0,d.jsxs)(i.Zp,{children:[(0,d.jsxs)(i.aR,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(i.ZB,{className:"flex items-center",children:[(0,d.jsx)(w.A,{className:"h-5 w-5 mr-2"}),"Recent Notifications"]}),(0,d.jsx)(f(),{href:"/account/notifications",children:(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",children:["View All",(0,d.jsx)(q,{className:"h-4 w-4 ml-1"})]})})]}),(0,d.jsx)(i.BT,{children:"Latest updates and alerts"})]}),(0,d.jsx)(i.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[B.map(a=>(0,d.jsx)("div",{className:`p-4 border rounded-lg ${!a.read?"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800":""}`,children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium text-sm",children:a.title}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:a.message}),(0,d.jsx)("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-2",children:(0,k.Yq)(a.createdAt)})]}),!a.read&&(0,d.jsx)("div",{className:"h-2 w-2 bg-blue-500 rounded-full ml-2 mt-1"})]})},a.id)),0===B.length&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(w.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No notifications"})]})]})})]})]}),(0,d.jsxs)(i.Zp,{className:"mt-8",children:[(0,d.jsxs)(i.aR,{children:[(0,d.jsxs)(i.ZB,{className:"flex items-center",children:[(0,d.jsx)(x,{className:"h-5 w-5 mr-2"}),"Loyalty Program"]}),(0,d.jsxs)(i.BT,{children:["You're a valued member since ",(0,k.Yq)(y.memberSince)]})]}),(0,d.jsxs)(i.Wu,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:[y.loyaltyPoints.toLocaleString()," Points"]}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"Earn 1 point for every $1 spent"})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)(h.$,{children:"Redeem Points"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"Next reward at 3,000 points"})]})]}),(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-2",children:[(0,d.jsxs)("span",{children:["Current: ",y.loyaltyPoints]}),(0,d.jsx)("span",{children:"Next Reward: 3,000"})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:{width:`${y.loyaltyPoints/3e3*100}%`}})})]})]})]})]})})}},71057:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90670:(a,b,c)=>{Promise.resolve().then(c.bind(c,11504))},97051:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,108,368],()=>b(b.s=20477));module.exports=c})();