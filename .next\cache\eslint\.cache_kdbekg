[{"E:\\aidevcommerce\\src\\app\\api\\auth\\forgot-password\\route.ts": "1", "E:\\aidevcommerce\\src\\app\\api\\auth\\register\\route.ts": "2", "E:\\aidevcommerce\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "3", "E:\\aidevcommerce\\src\\app\\layout.tsx": "4", "E:\\aidevcommerce\\src\\app\\page.tsx": "5", "E:\\aidevcommerce\\src\\components\\providers\\language-provider.tsx": "6", "E:\\aidevcommerce\\src\\components\\providers\\query-provider.tsx": "7", "E:\\aidevcommerce\\src\\components\\providers\\theme-provider.tsx": "8", "E:\\aidevcommerce\\src\\components\\providers\\toast-provider.tsx": "9", "E:\\aidevcommerce\\src\\components\\ui\\button.tsx": "10", "E:\\aidevcommerce\\src\\components\\ui\\toast.tsx": "11", "E:\\aidevcommerce\\src\\components\\ui\\toaster.tsx": "12", "E:\\aidevcommerce\\src\\config\\constants.ts": "13", "E:\\aidevcommerce\\src\\hooks\\use-toast.ts": "14", "E:\\aidevcommerce\\src\\lib\\auth.ts": "15", "E:\\aidevcommerce\\src\\lib\\prisma.ts": "16", "E:\\aidevcommerce\\src\\lib\\utils.ts": "17", "E:\\aidevcommerce\\src\\types\\index.ts": "18", "E:\\aidevcommerce\\src\\types\\next-auth.d.ts": "19"}, {"size": 1946, "mtime": 1753789316205, "results": "20", "hashOfConfig": "21"}, {"size": 2015, "mtime": 1753790343190, "results": "22", "hashOfConfig": "21"}, {"size": 157, "mtime": 1753789279487, "results": "23", "hashOfConfig": "21"}, {"size": 3301, "mtime": 1753790030283, "results": "24", "hashOfConfig": "21"}, {"size": 5784, "mtime": 1753789570650, "results": "25", "hashOfConfig": "21"}, {"size": 4684, "mtime": 1753789431464, "results": "26", "hashOfConfig": "21"}, {"size": 1473, "mtime": 1753789402200, "results": "27", "hashOfConfig": "21"}, {"size": 327, "mtime": 1753789392182, "results": "28", "hashOfConfig": "21"}, {"size": 209, "mtime": 1753789408691, "results": "29", "hashOfConfig": "21"}, {"size": 1835, "mtime": 1753789588127, "results": "30", "hashOfConfig": "21"}, {"size": 4853, "mtime": 1753789461992, "results": "31", "hashOfConfig": "21"}, {"size": 786, "mtime": 1753789440245, "results": "32", "hashOfConfig": "21"}, {"size": 5434, "mtime": 1753788955539, "results": "33", "hashOfConfig": "21"}, {"size": 3909, "mtime": 1753789487407, "results": "34", "hashOfConfig": "21"}, {"size": 7873, "mtime": 1753789273278, "results": "35", "hashOfConfig": "21"}, {"size": 3757, "mtime": 1753789228849, "results": "36", "hashOfConfig": "21"}, {"size": 4169, "mtime": 1753788927225, "results": "37", "hashOfConfig": "21"}, {"size": 7791, "mtime": 1753788988359, "results": "38", "hashOfConfig": "21"}, {"size": 539, "mtime": 1753789287180, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "abu0uj", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\aidevcommerce\\src\\app\\api\\auth\\forgot-password\\route.ts", [], [], "E:\\aidevcommerce\\src\\app\\api\\auth\\register\\route.ts", ["97", "98"], [], "E:\\aidevcommerce\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "E:\\aidevcommerce\\src\\app\\layout.tsx", [], [], "E:\\aidevcommerce\\src\\app\\page.tsx", [], [], "E:\\aidevcommerce\\src\\components\\providers\\language-provider.tsx", [], [], "E:\\aidevcommerce\\src\\components\\providers\\query-provider.tsx", ["99", "100"], [], "E:\\aidevcommerce\\src\\components\\providers\\theme-provider.tsx", [], [], "E:\\aidevcommerce\\src\\components\\providers\\toast-provider.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\button.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\toast.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\toaster.tsx", [], [], "E:\\aidevcommerce\\src\\config\\constants.ts", [], [], "E:\\aidevcommerce\\src\\hooks\\use-toast.ts", ["101"], [], "E:\\aidevcommerce\\src\\lib\\auth.ts", ["102", "103", "104", "105", "106", "107"], [], "E:\\aidevcommerce\\src\\lib\\prisma.ts", ["108", "109", "110", "111", "112", "113", "114", "115", "116"], [], "E:\\aidevcommerce\\src\\lib\\utils.ts", ["117", "118", "119", "120"], [], "E:\\aidevcommerce\\src\\types\\index.ts", ["121", "122", "123", "124", "125"], [], "E:\\aidevcommerce\\src\\types\\next-auth.d.ts", ["126", "127"], [], {"ruleId": "128", "severity": 1, "message": "129", "line": 41, "column": 13, "nodeType": null, "messageId": "130", "endLine": 41, "endColumn": 21}, {"ruleId": "131", "severity": 2, "message": "132", "line": 41, "column": 58, "nodeType": "133", "messageId": "134", "endLine": 41, "endColumn": 61, "suggestions": "135"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 15, "column": 42, "nodeType": "133", "messageId": "134", "endLine": 15, "endColumn": 45, "suggestions": "136"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 27, "column": 42, "nodeType": "133", "messageId": "134", "endLine": 27, "endColumn": 45, "suggestions": "137"}, {"ruleId": "128", "severity": 1, "message": "138", "line": 20, "column": 7, "nodeType": null, "messageId": "139", "endLine": 20, "endColumn": 18}, {"ruleId": "131", "severity": 2, "message": "132", "line": 11, "column": 37, "nodeType": "133", "messageId": "134", "endLine": 11, "endColumn": 40, "suggestions": "140"}, {"ruleId": "128", "severity": 1, "message": "141", "line": 100, "column": 35, "nodeType": null, "messageId": "130", "endLine": 100, "endColumn": 42}, {"ruleId": "128", "severity": 1, "message": "141", "line": 137, "column": 35, "nodeType": null, "messageId": "130", "endLine": 137, "endColumn": 42}, {"ruleId": "128", "severity": 1, "message": "142", "line": 137, "column": 44, "nodeType": null, "messageId": "130", "endLine": 137, "endColumn": 53}, {"ruleId": "128", "severity": 1, "message": "143", "line": 156, "column": 21, "nodeType": null, "messageId": "130", "endLine": 156, "endColumn": 28}, {"ruleId": "128", "severity": 1, "message": "144", "line": 156, "column": 30, "nodeType": null, "messageId": "130", "endLine": 156, "endColumn": 35}, {"ruleId": "131", "severity": 2, "message": "132", "line": 109, "column": 16, "nodeType": "133", "messageId": "134", "endLine": 109, "endColumn": 19, "suggestions": "145"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 122, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 122, "endColumn": 13, "suggestions": "146"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 137, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 137, "endColumn": 13, "suggestions": "147"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 138, "column": 12, "nodeType": "133", "messageId": "134", "endLine": 138, "endColumn": 15, "suggestions": "148"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 147, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 147, "endColumn": 13, "suggestions": "149"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 148, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 148, "endColumn": 13, "suggestions": "150"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 156, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 156, "endColumn": 13, "suggestions": "151"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 157, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 157, "endColumn": 13, "suggestions": "152"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 158, "column": 11, "nodeType": "133", "messageId": "134", "endLine": 158, "endColumn": 14, "suggestions": "153"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 52, "column": 46, "nodeType": "133", "messageId": "134", "endLine": 52, "endColumn": 49, "suggestions": "154"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 52, "column": 56, "nodeType": "133", "messageId": "134", "endLine": 52, "endColumn": 59, "suggestions": "155"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 63, "column": 46, "nodeType": "133", "messageId": "134", "endLine": 63, "endColumn": 49, "suggestions": "156"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 63, "column": 56, "nodeType": "133", "messageId": "134", "endLine": 63, "endColumn": 59, "suggestions": "157"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 346, "column": 25, "nodeType": "133", "messageId": "134", "endLine": 346, "endColumn": 28, "suggestions": "158"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 353, "column": 34, "nodeType": "133", "messageId": "134", "endLine": 353, "endColumn": 37, "suggestions": "159"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 361, "column": 40, "nodeType": "133", "messageId": "134", "endLine": 361, "endColumn": 43, "suggestions": "160"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 420, "column": 35, "nodeType": "133", "messageId": "134", "endLine": 420, "endColumn": 38, "suggestions": "161"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 423, "column": 27, "nodeType": "133", "messageId": "134", "endLine": 423, "endColumn": 30, "suggestions": "162"}, {"ruleId": "128", "severity": 1, "message": "163", "line": 1, "column": 8, "nodeType": null, "messageId": "130", "endLine": 1, "endColumn": 16}, {"ruleId": "128", "severity": 1, "message": "164", "line": 2, "column": 10, "nodeType": null, "messageId": "130", "endLine": 2, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'password' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["165", "166"], ["167", "168"], ["169", "170"], "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", ["171", "172"], "'profile' is defined but never used.", "'isNewUser' is defined but never used.", "'session' is defined but never used.", "'token' is defined but never used.", ["173", "174"], ["175", "176"], ["177", "178"], ["179", "180"], ["181", "182"], ["183", "184"], ["185", "186"], ["187", "188"], ["189", "190"], ["191", "192"], ["193", "194"], ["195", "196"], ["197", "198"], ["199", "200"], ["201", "202"], ["203", "204"], ["205", "206"], ["207", "208"], "'NextAuth' is defined but never used.", "'JWT' is defined but never used.", {"messageId": "209", "fix": "210", "desc": "211"}, {"messageId": "212", "fix": "213", "desc": "214"}, {"messageId": "209", "fix": "215", "desc": "211"}, {"messageId": "212", "fix": "216", "desc": "214"}, {"messageId": "209", "fix": "217", "desc": "211"}, {"messageId": "212", "fix": "218", "desc": "214"}, {"messageId": "209", "fix": "219", "desc": "211"}, {"messageId": "212", "fix": "220", "desc": "214"}, {"messageId": "209", "fix": "221", "desc": "211"}, {"messageId": "212", "fix": "222", "desc": "214"}, {"messageId": "209", "fix": "223", "desc": "211"}, {"messageId": "212", "fix": "224", "desc": "214"}, {"messageId": "209", "fix": "225", "desc": "211"}, {"messageId": "212", "fix": "226", "desc": "214"}, {"messageId": "209", "fix": "227", "desc": "211"}, {"messageId": "212", "fix": "228", "desc": "214"}, {"messageId": "209", "fix": "229", "desc": "211"}, {"messageId": "212", "fix": "230", "desc": "214"}, {"messageId": "209", "fix": "231", "desc": "211"}, {"messageId": "212", "fix": "232", "desc": "214"}, {"messageId": "209", "fix": "233", "desc": "211"}, {"messageId": "212", "fix": "234", "desc": "214"}, {"messageId": "209", "fix": "235", "desc": "211"}, {"messageId": "212", "fix": "236", "desc": "214"}, {"messageId": "209", "fix": "237", "desc": "211"}, {"messageId": "212", "fix": "238", "desc": "214"}, {"messageId": "209", "fix": "239", "desc": "211"}, {"messageId": "212", "fix": "240", "desc": "214"}, {"messageId": "209", "fix": "241", "desc": "211"}, {"messageId": "212", "fix": "242", "desc": "214"}, {"messageId": "209", "fix": "243", "desc": "211"}, {"messageId": "212", "fix": "244", "desc": "214"}, {"messageId": "209", "fix": "245", "desc": "211"}, {"messageId": "212", "fix": "246", "desc": "214"}, {"messageId": "209", "fix": "247", "desc": "211"}, {"messageId": "212", "fix": "248", "desc": "214"}, {"messageId": "209", "fix": "249", "desc": "211"}, {"messageId": "212", "fix": "250", "desc": "214"}, {"messageId": "209", "fix": "251", "desc": "211"}, {"messageId": "212", "fix": "252", "desc": "214"}, {"messageId": "209", "fix": "253", "desc": "211"}, {"messageId": "212", "fix": "254", "desc": "214"}, {"messageId": "209", "fix": "255", "desc": "211"}, {"messageId": "212", "fix": "256", "desc": "214"}, "suggestUnknown", {"range": "257", "text": "258"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "259", "text": "260"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "261", "text": "258"}, {"range": "262", "text": "260"}, {"range": "263", "text": "258"}, {"range": "264", "text": "260"}, {"range": "265", "text": "258"}, {"range": "266", "text": "260"}, {"range": "267", "text": "258"}, {"range": "268", "text": "260"}, {"range": "269", "text": "258"}, {"range": "270", "text": "260"}, {"range": "271", "text": "258"}, {"range": "272", "text": "260"}, {"range": "273", "text": "258"}, {"range": "274", "text": "260"}, {"range": "275", "text": "258"}, {"range": "276", "text": "260"}, {"range": "277", "text": "258"}, {"range": "278", "text": "260"}, {"range": "279", "text": "258"}, {"range": "280", "text": "260"}, {"range": "281", "text": "258"}, {"range": "282", "text": "260"}, {"range": "283", "text": "258"}, {"range": "284", "text": "260"}, {"range": "285", "text": "258"}, {"range": "286", "text": "260"}, {"range": "287", "text": "258"}, {"range": "288", "text": "260"}, {"range": "289", "text": "258"}, {"range": "290", "text": "260"}, {"range": "291", "text": "258"}, {"range": "292", "text": "260"}, {"range": "293", "text": "258"}, {"range": "294", "text": "260"}, {"range": "295", "text": "258"}, {"range": "296", "text": "260"}, {"range": "297", "text": "258"}, {"range": "298", "text": "260"}, {"range": "299", "text": "258"}, {"range": "300", "text": "260"}, {"range": "301", "text": "258"}, {"range": "302", "text": "260"}, [1378, 1381], "unknown", [1378, 1381], "never", [518, 521], [518, 521], [944, 947], [944, 947], [461, 464], [461, 464], [2607, 2610], [2607, 2610], [2862, 2865], [2862, 2865], [3192, 3195], [3192, 3195], [3208, 3211], [3208, 3211], [3356, 3359], [3356, 3359], [3370, 3373], [3370, 3373], [3534, 3537], [3534, 3537], [3548, 3551], [3548, 3551], [3563, 3566], [3563, 3566], [1226, 1229], [1226, 1229], [1236, 1239], [1236, 1239], [1505, 1508], [1505, 1508], [1515, 1518], [1515, 1518], [6407, 6410], [6407, 6410], [6522, 6525], [6522, 6525], [6673, 6676], [6673, 6676], [7697, 7700], [7697, 7700], [7759, 7762], [7759, 7762]]