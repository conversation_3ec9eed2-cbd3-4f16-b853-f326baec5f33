[{"E:\\aidevcommerce\\src\\app\\api\\auth\\forgot-password\\route.ts": "1", "E:\\aidevcommerce\\src\\app\\api\\auth\\register\\route.ts": "2", "E:\\aidevcommerce\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "3", "E:\\aidevcommerce\\src\\app\\layout.tsx": "4", "E:\\aidevcommerce\\src\\app\\page.tsx": "5", "E:\\aidevcommerce\\src\\components\\providers\\language-provider.tsx": "6", "E:\\aidevcommerce\\src\\components\\providers\\query-provider.tsx": "7", "E:\\aidevcommerce\\src\\components\\providers\\theme-provider.tsx": "8", "E:\\aidevcommerce\\src\\components\\providers\\toast-provider.tsx": "9", "E:\\aidevcommerce\\src\\components\\ui\\button.tsx": "10", "E:\\aidevcommerce\\src\\components\\ui\\toast.tsx": "11", "E:\\aidevcommerce\\src\\components\\ui\\toaster.tsx": "12", "E:\\aidevcommerce\\src\\config\\constants.ts": "13", "E:\\aidevcommerce\\src\\hooks\\use-toast.ts": "14", "E:\\aidevcommerce\\src\\lib\\auth.ts": "15", "E:\\aidevcommerce\\src\\lib\\prisma.ts": "16", "E:\\aidevcommerce\\src\\lib\\utils.ts": "17", "E:\\aidevcommerce\\src\\types\\index.ts": "18", "E:\\aidevcommerce\\src\\types\\next-auth.d.ts": "19", "E:\\aidevcommerce\\src\\app\\account\\addresses\\page.tsx": "20", "E:\\aidevcommerce\\src\\app\\account\\orders\\page.tsx": "21", "E:\\aidevcommerce\\src\\app\\account\\page.tsx": "22", "E:\\aidevcommerce\\src\\app\\account\\profile\\page.tsx": "23", "E:\\aidevcommerce\\src\\app\\account\\wishlist\\page.tsx": "24", "E:\\aidevcommerce\\src\\app\\admin\\analytics\\page.tsx": "25", "E:\\aidevcommerce\\src\\app\\admin\\blog\\page.tsx": "26", "E:\\aidevcommerce\\src\\app\\admin\\emails\\page.tsx": "27", "E:\\aidevcommerce\\src\\app\\admin\\orders\\page.tsx": "28", "E:\\aidevcommerce\\src\\app\\admin\\page.tsx": "29", "E:\\aidevcommerce\\src\\app\\admin\\products\\page.tsx": "30", "E:\\aidevcommerce\\src\\app\\admin\\services\\page.tsx": "31", "E:\\aidevcommerce\\src\\app\\admin\\users\\page.tsx": "32", "E:\\aidevcommerce\\src\\app\\api\\health\\route.ts": "33", "E:\\aidevcommerce\\src\\app\\api\\robots\\route.ts": "34", "E:\\aidevcommerce\\src\\app\\api\\sitemap\\route.ts": "35", "E:\\aidevcommerce\\src\\app\\auth\\forgot-password\\page.tsx": "36", "E:\\aidevcommerce\\src\\app\\auth\\login\\page.tsx": "37", "E:\\aidevcommerce\\src\\app\\auth\\register\\page.tsx": "38", "E:\\aidevcommerce\\src\\app\\blog\\page.tsx": "39", "E:\\aidevcommerce\\src\\app\\blog\\[slug]\\page.tsx": "40", "E:\\aidevcommerce\\src\\app\\cart\\page.tsx": "41", "E:\\aidevcommerce\\src\\app\\product\\[slug]\\page.tsx": "42", "E:\\aidevcommerce\\src\\app\\production-lines\\page.tsx": "43", "E:\\aidevcommerce\\src\\app\\production-lines\\[slug]\\page.tsx": "44", "E:\\aidevcommerce\\src\\app\\services\\page.tsx": "45", "E:\\aidevcommerce\\src\\app\\services\\[slug]\\page.tsx": "46", "E:\\aidevcommerce\\src\\app\\shop\\page.tsx": "47", "E:\\aidevcommerce\\src\\components\\ai\\ai-chat-support.tsx": "48", "E:\\aidevcommerce\\src\\components\\ai\\ai-recommendations.tsx": "49", "E:\\aidevcommerce\\src\\components\\cart\\cart-sidebar.tsx": "50", "E:\\aidevcommerce\\src\\components\\home\\featured-products-section.tsx": "51", "E:\\aidevcommerce\\src\\components\\home\\features-section.tsx": "52", "E:\\aidevcommerce\\src\\components\\home\\hero-section.tsx": "53", "E:\\aidevcommerce\\src\\components\\layout\\account-layout.tsx": "54", "E:\\aidevcommerce\\src\\components\\layout\\admin-layout.tsx": "55", "E:\\aidevcommerce\\src\\components\\layout\\header.tsx": "56", "E:\\aidevcommerce\\src\\components\\seo\\seo-head.tsx": "57", "E:\\aidevcommerce\\src\\components\\ui\\avatar.tsx": "58", "E:\\aidevcommerce\\src\\components\\ui\\badge.tsx": "59", "E:\\aidevcommerce\\src\\components\\ui\\card.tsx": "60", "E:\\aidevcommerce\\src\\components\\ui\\input.tsx": "61", "E:\\aidevcommerce\\src\\components\\ui\\label.tsx": "62", "E:\\aidevcommerce\\src\\components\\ui\\tabs.tsx": "63", "E:\\aidevcommerce\\src\\components\\ui\\textarea.tsx": "64", "E:\\aidevcommerce\\src\\components\\__tests__\\ui\\button.test.tsx": "65", "E:\\aidevcommerce\\src\\lib\\ai-service.ts": "66", "E:\\aidevcommerce\\src\\lib\\email-service.ts": "67", "E:\\aidevcommerce\\src\\middleware.ts": "68", "E:\\aidevcommerce\\src\\store\\cart-store.ts": "69", "E:\\aidevcommerce\\src\\store\\wishlist-store.ts": "70"}, {"size": 1946, "mtime": 1753789316205, "results": "71", "hashOfConfig": "72"}, {"size": 2015, "mtime": 1753790343190, "results": "73", "hashOfConfig": "72"}, {"size": 157, "mtime": 1753789279487, "results": "74", "hashOfConfig": "72"}, {"size": 3495, "mtime": 1753791970016, "results": "75", "hashOfConfig": "72"}, {"size": 1235, "mtime": 1753800228702, "results": "76", "hashOfConfig": "72"}, {"size": 4684, "mtime": 1753789431464, "results": "77", "hashOfConfig": "72"}, {"size": 1473, "mtime": 1753789402200, "results": "78", "hashOfConfig": "72"}, {"size": 327, "mtime": 1753789392182, "results": "79", "hashOfConfig": "72"}, {"size": 209, "mtime": 1753789408691, "results": "80", "hashOfConfig": "72"}, {"size": 1835, "mtime": 1753789588127, "results": "81", "hashOfConfig": "72"}, {"size": 4853, "mtime": 1753789461992, "results": "82", "hashOfConfig": "72"}, {"size": 786, "mtime": 1753789440245, "results": "83", "hashOfConfig": "72"}, {"size": 5434, "mtime": 1753788955539, "results": "84", "hashOfConfig": "72"}, {"size": 3909, "mtime": 1753789487407, "results": "85", "hashOfConfig": "72"}, {"size": 7873, "mtime": 1753789273278, "results": "86", "hashOfConfig": "72"}, {"size": 3757, "mtime": 1753789228849, "results": "87", "hashOfConfig": "72"}, {"size": 4169, "mtime": 1753788927225, "results": "88", "hashOfConfig": "72"}, {"size": 7791, "mtime": 1753788988359, "results": "89", "hashOfConfig": "72"}, {"size": 539, "mtime": 1753789287180, "results": "90", "hashOfConfig": "72"}, {"size": 18420, "mtime": 1753791287030, "results": "91", "hashOfConfig": "72"}, {"size": 14314, "mtime": 1753791341175, "results": "92", "hashOfConfig": "72"}, {"size": 15902, "mtime": 1753791404688, "results": "93", "hashOfConfig": "72"}, {"size": 11418, "mtime": 1753791220986, "results": "94", "hashOfConfig": "72"}, {"size": 12297, "mtime": 1753792343815, "results": "95", "hashOfConfig": "72"}, {"size": 18187, "mtime": 1753800324945, "results": "96", "hashOfConfig": "72"}, {"size": 24319, "mtime": 1753799669122, "results": "97", "hashOfConfig": "72"}, {"size": 23486, "mtime": 1753800551302, "results": "98", "hashOfConfig": "72"}, {"size": 25788, "mtime": 1753799023252, "results": "99", "hashOfConfig": "72"}, {"size": 15840, "mtime": 1753798646843, "results": "100", "hashOfConfig": "72"}, {"size": 18664, "mtime": 1753798730429, "results": "101", "hashOfConfig": "72"}, {"size": 23839, "mtime": 1753799765359, "results": "102", "hashOfConfig": "72"}, {"size": 22874, "mtime": 1753799148800, "results": "103", "hashOfConfig": "72"}, {"size": 6281, "mtime": 1753801256418, "results": "104", "hashOfConfig": "72"}, {"size": 1528, "mtime": 1753801061335, "results": "105", "hashOfConfig": "72"}, {"size": 6848, "mtime": 1753801039341, "results": "106", "hashOfConfig": "72"}, {"size": 6159, "mtime": 1753790806367, "results": "107", "hashOfConfig": "72"}, {"size": 8696, "mtime": 1753790957555, "results": "108", "hashOfConfig": "72"}, {"size": 8848, "mtime": 1753790783792, "results": "109", "hashOfConfig": "72"}, {"size": 19501, "mtime": 1753797826960, "results": "110", "hashOfConfig": "72"}, {"size": 24196, "mtime": 1753797916550, "results": "111", "hashOfConfig": "72"}, {"size": 12608, "mtime": 1753792091725, "results": "112", "hashOfConfig": "72"}, {"size": 18477, "mtime": 1753792503914, "results": "113", "hashOfConfig": "72"}, {"size": 21283, "mtime": 1753797510394, "results": "114", "hashOfConfig": "72"}, {"size": 22928, "mtime": 1753797608856, "results": "115", "hashOfConfig": "72"}, {"size": 16893, "mtime": 1753793111570, "results": "116", "hashOfConfig": "72"}, {"size": 20322, "mtime": 1753793184611, "results": "117", "hashOfConfig": "72"}, {"size": 15994, "mtime": 1753792419584, "results": "118", "hashOfConfig": "72"}, {"size": 13321, "mtime": 1753800157083, "results": "119", "hashOfConfig": "72"}, {"size": 13312, "mtime": 1753800092536, "results": "120", "hashOfConfig": "72"}, {"size": 8615, "mtime": 1753791886945, "results": "121", "hashOfConfig": "72"}, {"size": 13659, "mtime": 1753792838247, "results": "122", "hashOfConfig": "72"}, {"size": 7850, "mtime": 1753792773923, "results": "123", "hashOfConfig": "72"}, {"size": 8595, "mtime": 1753792720406, "results": "124", "hashOfConfig": "72"}, {"size": 6219, "mtime": 1753791181822, "results": "125", "hashOfConfig": "72"}, {"size": 11475, "mtime": 1753798443790, "results": "126", "hashOfConfig": "72"}, {"size": 9298, "mtime": 1753792282092, "results": "127", "hashOfConfig": "72"}, {"size": 10205, "mtime": 1753800988019, "results": "128", "hashOfConfig": "72"}, {"size": 1419, "mtime": 1753791143098, "results": "129", "hashOfConfig": "72"}, {"size": 1128, "mtime": 1753792677120, "results": "130", "hashOfConfig": "72"}, {"size": 1849, "mtime": 1753790723107, "results": "131", "hashOfConfig": "72"}, {"size": 824, "mtime": 1753790701787, "results": "132", "hashOfConfig": "72"}, {"size": 724, "mtime": 1753790711398, "results": "133", "hashOfConfig": "72"}, {"size": 1897, "mtime": 1753791155292, "results": "134", "hashOfConfig": "72"}, {"size": 772, "mtime": 1753791131482, "results": "135", "hashOfConfig": "72"}, {"size": 4136, "mtime": 1753801152666, "results": "136", "hashOfConfig": "72"}, {"size": 11656, "mtime": 1753800029949, "results": "137", "hashOfConfig": "72"}, {"size": 14657, "mtime": 1753800465318, "results": "138", "hashOfConfig": "72"}, {"size": 7257, "mtime": 1753800920433, "results": "139", "hashOfConfig": "72"}, {"size": 3678, "mtime": 1753791850514, "results": "140", "hashOfConfig": "72"}, {"size": 2635, "mtime": 1753792241943, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "abu0uj", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\aidevcommerce\\src\\app\\api\\auth\\forgot-password\\route.ts", [], [], "E:\\aidevcommerce\\src\\app\\api\\auth\\register\\route.ts", ["352", "353"], [], "E:\\aidevcommerce\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "E:\\aidevcommerce\\src\\app\\layout.tsx", [], [], "E:\\aidevcommerce\\src\\app\\page.tsx", [], [], "E:\\aidevcommerce\\src\\components\\providers\\language-provider.tsx", [], [], "E:\\aidevcommerce\\src\\components\\providers\\query-provider.tsx", ["354", "355"], [], "E:\\aidevcommerce\\src\\components\\providers\\theme-provider.tsx", [], [], "E:\\aidevcommerce\\src\\components\\providers\\toast-provider.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\button.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\toast.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\toaster.tsx", [], [], "E:\\aidevcommerce\\src\\config\\constants.ts", [], [], "E:\\aidevcommerce\\src\\hooks\\use-toast.ts", ["356"], [], "E:\\aidevcommerce\\src\\lib\\auth.ts", ["357", "358", "359", "360", "361", "362"], [], "E:\\aidevcommerce\\src\\lib\\prisma.ts", ["363", "364", "365", "366", "367", "368", "369", "370", "371"], [], "E:\\aidevcommerce\\src\\lib\\utils.ts", ["372", "373", "374", "375"], [], "E:\\aidevcommerce\\src\\types\\index.ts", ["376", "377", "378", "379", "380"], [], "E:\\aidevcommerce\\src\\types\\next-auth.d.ts", ["381", "382"], [], "E:\\aidevcommerce\\src\\app\\account\\addresses\\page.tsx", ["383", "384", "385"], [], "E:\\aidevcommerce\\src\\app\\account\\orders\\page.tsx", ["386", "387"], [], "E:\\aidevcommerce\\src\\app\\account\\page.tsx", ["388", "389", "390", "391", "392"], [], "E:\\aidevcommerce\\src\\app\\account\\profile\\page.tsx", ["393"], [], "E:\\aidevcommerce\\src\\app\\account\\wishlist\\page.tsx", ["394", "395", "396", "397", "398", "399"], [], "E:\\aidevcommerce\\src\\app\\admin\\analytics\\page.tsx", ["400", "401", "402", "403", "404", "405"], [], "E:\\aidevcommerce\\src\\app\\admin\\blog\\page.tsx", ["406", "407", "408", "409", "410", "411", "412"], [], "E:\\aidevcommerce\\src\\app\\admin\\emails\\page.tsx", ["413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423"], [], "E:\\aidevcommerce\\src\\app\\admin\\orders\\page.tsx", ["424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434"], [], "E:\\aidevcommerce\\src\\app\\admin\\page.tsx", ["435", "436", "437", "438", "439"], [], "E:\\aidevcommerce\\src\\app\\admin\\products\\page.tsx", ["440", "441", "442", "443", "444", "445", "446", "447", "448"], [], "E:\\aidevcommerce\\src\\app\\admin\\services\\page.tsx", ["449", "450", "451", "452", "453", "454", "455"], [], "E:\\aidevcommerce\\src\\app\\admin\\users\\page.tsx", ["456", "457", "458", "459", "460", "461", "462", "463", "464"], [], "E:\\aidevcommerce\\src\\app\\api\\health\\route.ts", ["465"], [], "E:\\aidevcommerce\\src\\app\\api\\robots\\route.ts", [], [], "E:\\aidevcommerce\\src\\app\\api\\sitemap\\route.ts", [], [], "E:\\aidevcommerce\\src\\app\\auth\\forgot-password\\page.tsx", ["466", "467", "468", "469"], [], "E:\\aidevcommerce\\src\\app\\auth\\login\\page.tsx", ["470", "471", "472"], [], "E:\\aidevcommerce\\src\\app\\auth\\register\\page.tsx", ["473"], [], "E:\\aidevcommerce\\src\\app\\blog\\page.tsx", ["474", "475"], [], "E:\\aidevcommerce\\src\\app\\blog\\[slug]\\page.tsx", ["476", "477", "478", "479"], [], "E:\\aidevcommerce\\src\\app\\cart\\page.tsx", ["480", "481"], [], "E:\\aidevcommerce\\src\\app\\product\\[slug]\\page.tsx", ["482", "483", "484", "485", "486", "487", "488"], [], "E:\\aidevcommerce\\src\\app\\production-lines\\page.tsx", ["489", "490", "491", "492", "493", "494", "495", "496"], [], "E:\\aidevcommerce\\src\\app\\production-lines\\[slug]\\page.tsx", ["497", "498", "499", "500", "501", "502"], [], "E:\\aidevcommerce\\src\\app\\services\\page.tsx", ["503", "504", "505", "506", "507", "508", "509", "510"], [], "E:\\aidevcommerce\\src\\app\\services\\[slug]\\page.tsx", ["511", "512", "513", "514", "515", "516", "517"], [], "E:\\aidevcommerce\\src\\app\\shop\\page.tsx", ["518", "519", "520", "521", "522", "523", "524"], [], "E:\\aidevcommerce\\src\\components\\ai\\ai-chat-support.tsx", ["525", "526", "527", "528", "529", "530"], [], "E:\\aidevcommerce\\src\\components\\ai\\ai-recommendations.tsx", ["531", "532", "533", "534", "535", "536", "537"], [], "E:\\aidevcommerce\\src\\components\\cart\\cart-sidebar.tsx", ["538"], [], "E:\\aidevcommerce\\src\\components\\home\\featured-products-section.tsx", ["539"], [], "E:\\aidevcommerce\\src\\components\\home\\features-section.tsx", ["540", "541"], [], "E:\\aidevcommerce\\src\\components\\home\\hero-section.tsx", ["542"], [], "E:\\aidevcommerce\\src\\components\\layout\\account-layout.tsx", ["543"], [], "E:\\aidevcommerce\\src\\components\\layout\\admin-layout.tsx", ["544", "545", "546"], [], "E:\\aidevcommerce\\src\\components\\layout\\header.tsx", ["547", "548"], [], "E:\\aidevcommerce\\src\\components\\seo\\seo-head.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\avatar.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\badge.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\card.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\input.tsx", ["549"], [], "E:\\aidevcommerce\\src\\components\\ui\\label.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\tabs.tsx", [], [], "E:\\aidevcommerce\\src\\components\\ui\\textarea.tsx", ["550"], [], "E:\\aidevcommerce\\src\\components\\__tests__\\ui\\button.test.tsx", [], [], "E:\\aidevcommerce\\src\\lib\\ai-service.ts", ["551", "552", "553", "554", "555", "556", "557", "558", "559", "560"], [], "E:\\aidevcommerce\\src\\lib\\email-service.ts", ["561", "562", "563", "564"], [], "E:\\aidevcommerce\\src\\middleware.ts", [], [], "E:\\aidevcommerce\\src\\store\\cart-store.ts", [], [], "E:\\aidevcommerce\\src\\store\\wishlist-store.ts", [], [], {"ruleId": "565", "severity": 1, "message": "566", "line": 41, "column": 13, "nodeType": null, "messageId": "567", "endLine": 41, "endColumn": 21}, {"ruleId": "568", "severity": 2, "message": "569", "line": 41, "column": 58, "nodeType": "570", "messageId": "571", "endLine": 41, "endColumn": 61, "suggestions": "572"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 15, "column": 42, "nodeType": "570", "messageId": "571", "endLine": 15, "endColumn": 45, "suggestions": "573"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 27, "column": 42, "nodeType": "570", "messageId": "571", "endLine": 27, "endColumn": 45, "suggestions": "574"}, {"ruleId": "565", "severity": 1, "message": "575", "line": 20, "column": 7, "nodeType": null, "messageId": "576", "endLine": 20, "endColumn": 18}, {"ruleId": "568", "severity": 2, "message": "569", "line": 11, "column": 37, "nodeType": "570", "messageId": "571", "endLine": 11, "endColumn": 40, "suggestions": "577"}, {"ruleId": "565", "severity": 1, "message": "578", "line": 100, "column": 35, "nodeType": null, "messageId": "567", "endLine": 100, "endColumn": 42}, {"ruleId": "565", "severity": 1, "message": "578", "line": 137, "column": 35, "nodeType": null, "messageId": "567", "endLine": 137, "endColumn": 42}, {"ruleId": "565", "severity": 1, "message": "579", "line": 137, "column": 44, "nodeType": null, "messageId": "567", "endLine": 137, "endColumn": 53}, {"ruleId": "565", "severity": 1, "message": "580", "line": 156, "column": 21, "nodeType": null, "messageId": "567", "endLine": 156, "endColumn": 28}, {"ruleId": "565", "severity": 1, "message": "581", "line": 156, "column": 30, "nodeType": null, "messageId": "567", "endLine": 156, "endColumn": 35}, {"ruleId": "568", "severity": 2, "message": "569", "line": 109, "column": 16, "nodeType": "570", "messageId": "571", "endLine": 109, "endColumn": 19, "suggestions": "582"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 122, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 122, "endColumn": 13, "suggestions": "583"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 137, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 137, "endColumn": 13, "suggestions": "584"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 138, "column": 12, "nodeType": "570", "messageId": "571", "endLine": 138, "endColumn": 15, "suggestions": "585"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 147, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 147, "endColumn": 13, "suggestions": "586"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 148, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 148, "endColumn": 13, "suggestions": "587"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 156, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 156, "endColumn": 13, "suggestions": "588"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 157, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 157, "endColumn": 13, "suggestions": "589"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 158, "column": 11, "nodeType": "570", "messageId": "571", "endLine": 158, "endColumn": 14, "suggestions": "590"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 52, "column": 46, "nodeType": "570", "messageId": "571", "endLine": 52, "endColumn": 49, "suggestions": "591"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 52, "column": 56, "nodeType": "570", "messageId": "571", "endLine": 52, "endColumn": 59, "suggestions": "592"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 63, "column": 46, "nodeType": "570", "messageId": "571", "endLine": 63, "endColumn": 49, "suggestions": "593"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 63, "column": 56, "nodeType": "570", "messageId": "571", "endLine": 63, "endColumn": 59, "suggestions": "594"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 346, "column": 25, "nodeType": "570", "messageId": "571", "endLine": 346, "endColumn": 28, "suggestions": "595"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 353, "column": 34, "nodeType": "570", "messageId": "571", "endLine": 353, "endColumn": 37, "suggestions": "596"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 361, "column": 40, "nodeType": "570", "messageId": "571", "endLine": 361, "endColumn": 43, "suggestions": "597"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 420, "column": 35, "nodeType": "570", "messageId": "571", "endLine": 420, "endColumn": 38, "suggestions": "598"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 423, "column": 27, "nodeType": "570", "messageId": "571", "endLine": 423, "endColumn": 30, "suggestions": "599"}, {"ruleId": "565", "severity": 1, "message": "600", "line": 1, "column": 8, "nodeType": null, "messageId": "567", "endLine": 1, "endColumn": 16}, {"ruleId": "565", "severity": 1, "message": "601", "line": 2, "column": 10, "nodeType": null, "messageId": "567", "endLine": 2, "endColumn": 13}, {"ruleId": "565", "severity": 1, "message": "602", "line": 127, "column": 14, "nodeType": null, "messageId": "567", "endLine": 127, "endColumn": 19}, {"ruleId": "565", "severity": 1, "message": "602", "line": 162, "column": 16, "nodeType": null, "messageId": "567", "endLine": 162, "endColumn": 21}, {"ruleId": "565", "severity": 1, "message": "602", "line": 189, "column": 14, "nodeType": null, "messageId": "567", "endLine": 189, "endColumn": 19}, {"ruleId": "565", "severity": 1, "message": "603", "line": 147, "column": 11, "nodeType": null, "messageId": "567", "endLine": 147, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 399, "column": 30, "nodeType": "606", "messageId": "607", "suggestions": "608"}, {"ruleId": "565", "severity": 1, "message": "603", "line": 94, "column": 11, "nodeType": null, "messageId": "567", "endLine": 94, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 105, "column": 17, "nodeType": "606", "messageId": "607", "suggestions": "609"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 105, "column": 24, "nodeType": "606", "messageId": "607", "suggestions": "610"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 255, "column": 26, "nodeType": "606", "messageId": "607", "suggestions": "611"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 395, "column": 18, "nodeType": "606", "messageId": "607", "suggestions": "612"}, {"ruleId": "565", "severity": 1, "message": "602", "line": 70, "column": 14, "nodeType": null, "messageId": "567", "endLine": 70, "endColumn": 19}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 19, "column": 3, "nodeType": null, "messageId": "567", "endLine": 19, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "617", "line": 22, "column": 3, "nodeType": null, "messageId": "567", "endLine": 22, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "603", "line": 34, "column": 11, "nodeType": null, "messageId": "567", "endLine": 34, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "618", "line": 9, "column": 23, "nodeType": null, "messageId": "567", "endLine": 9, "endColumn": 33}, {"ruleId": "565", "severity": 1, "message": "619", "line": 13, "column": 3, "nodeType": null, "messageId": "567", "endLine": 13, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "620", "line": 19, "column": 3, "nodeType": null, "messageId": "567", "endLine": 19, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "621", "line": 20, "column": 3, "nodeType": null, "messageId": "567", "endLine": 20, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "616", "line": 22, "column": 3, "nodeType": null, "messageId": "567", "endLine": 22, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "603", "line": 102, "column": 11, "nodeType": null, "messageId": "567", "endLine": 102, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "622", "line": 16, "column": 3, "nodeType": null, "messageId": "567", "endLine": 16, "endColumn": 17}, {"ruleId": "565", "severity": 1, "message": "603", "line": 211, "column": 11, "nodeType": null, "messageId": "567", "endLine": 211, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "623", "line": 249, "column": 29, "nodeType": null, "messageId": "567", "endLine": 249, "endColumn": 35}, {"ruleId": "565", "severity": 1, "message": "616", "line": 16, "column": 3, "nodeType": null, "messageId": "567", "endLine": 16, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "622", "line": 17, "column": 3, "nodeType": null, "messageId": "567", "endLine": 17, "endColumn": 17}, {"ruleId": "565", "severity": 1, "message": "624", "line": 19, "column": 3, "nodeType": null, "messageId": "567", "endLine": 19, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "625", "line": 23, "column": 3, "nodeType": null, "messageId": "567", "endLine": 23, "endColumn": 8}, {"ruleId": "565", "severity": 1, "message": "626", "line": 31, "column": 3, "nodeType": null, "messageId": "567", "endLine": 31, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "619", "line": 36, "column": 3, "nodeType": null, "messageId": "567", "endLine": 36, "endColumn": 15}, {"ruleId": "568", "severity": 2, "message": "569", "line": 83, "column": 46, "nodeType": "570", "messageId": "571", "endLine": 83, "endColumn": 49, "suggestions": "627"}, {"ruleId": "565", "severity": 1, "message": "603", "line": 89, "column": 11, "nodeType": null, "messageId": "567", "endLine": 89, "endColumn": 12}, {"ruleId": "628", "severity": 1, "message": "629", "line": 94, "column": 6, "nodeType": "630", "endLine": 94, "endColumn": 8, "suggestions": "631"}, {"ruleId": "565", "severity": 1, "message": "602", "line": 132, "column": 14, "nodeType": null, "messageId": "567", "endLine": 132, "endColumn": 19}, {"ruleId": "568", "severity": 2, "message": "569", "line": 509, "column": 57, "nodeType": "570", "messageId": "571", "endLine": 509, "endColumn": 60, "suggestions": "632"}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 14, "column": 3, "nodeType": null, "messageId": "567", "endLine": 14, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "622", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 17}, {"ruleId": "565", "severity": 1, "message": "633", "line": 17, "column": 3, "nodeType": null, "messageId": "567", "endLine": 17, "endColumn": 7}, {"ruleId": "565", "severity": 1, "message": "634", "line": 22, "column": 3, "nodeType": null, "messageId": "567", "endLine": 22, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "635", "line": 28, "column": 3, "nodeType": null, "messageId": "567", "endLine": 28, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "636", "line": 29, "column": 3, "nodeType": null, "messageId": "567", "endLine": 29, "endColumn": 13}, {"ruleId": "565", "severity": 1, "message": "621", "line": 30, "column": 3, "nodeType": null, "messageId": "567", "endLine": 30, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "603", "line": 304, "column": 11, "nodeType": null, "messageId": "567", "endLine": 304, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "619", "line": 11, "column": 3, "nodeType": null, "messageId": "567", "endLine": 11, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "637", "line": 24, "column": 3, "nodeType": null, "messageId": "567", "endLine": 24, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "603", "line": 177, "column": 11, "nodeType": null, "messageId": "567", "endLine": 177, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 189, "column": 33, "nodeType": "606", "messageId": "607", "suggestions": "638"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 189, "column": 40, "nodeType": "606", "messageId": "607", "suggestions": "639"}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "618", "line": 11, "column": 23, "nodeType": null, "messageId": "567", "endLine": 11, "endColumn": 33}, {"ruleId": "565", "severity": 1, "message": "616", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "622", "line": 16, "column": 3, "nodeType": null, "messageId": "567", "endLine": 16, "endColumn": 17}, {"ruleId": "565", "severity": 1, "message": "619", "line": 22, "column": 3, "nodeType": null, "messageId": "567", "endLine": 22, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "603", "line": 149, "column": 11, "nodeType": null, "messageId": "567", "endLine": 149, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "640", "line": 185, "column": 32, "nodeType": null, "messageId": "567", "endLine": 185, "endColumn": 41}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "622", "line": 16, "column": 3, "nodeType": null, "messageId": "567", "endLine": 16, "endColumn": 17}, {"ruleId": "565", "severity": 1, "message": "603", "line": 221, "column": 11, "nodeType": null, "messageId": "567", "endLine": 221, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "641", "line": 258, "column": 32, "nodeType": null, "messageId": "567", "endLine": 258, "endColumn": 41}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "622", "line": 16, "column": 3, "nodeType": null, "messageId": "567", "endLine": 16, "endColumn": 17}, {"ruleId": "565", "severity": 1, "message": "624", "line": 18, "column": 3, "nodeType": null, "messageId": "567", "endLine": 18, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "642", "line": 22, "column": 3, "nodeType": null, "messageId": "567", "endLine": 22, "endColumn": 8}, {"ruleId": "565", "severity": 1, "message": "621", "line": 27, "column": 3, "nodeType": null, "messageId": "567", "endLine": 27, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "603", "line": 265, "column": 11, "nodeType": null, "messageId": "567", "endLine": 265, "endColumn": 12}, {"ruleId": "643", "severity": 2, "message": "644", "line": 187, "column": 16, "nodeType": "645", "messageId": "646", "endLine": 187, "endColumn": 29}, {"ruleId": "565", "severity": 1, "message": "602", "line": 63, "column": 14, "nodeType": null, "messageId": "567", "endLine": 63, "endColumn": 19}, {"ruleId": "604", "severity": 2, "message": "605", "line": 85, "column": 19, "nodeType": "606", "messageId": "607", "suggestions": "647"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 91, "column": 24, "nodeType": "606", "messageId": "607", "suggestions": "648"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 133, "column": 46, "nodeType": "606", "messageId": "607", "suggestions": "649"}, {"ruleId": "565", "severity": 1, "message": "602", "line": 55, "column": 14, "nodeType": null, "messageId": "567", "endLine": 55, "endColumn": 19}, {"ruleId": "565", "severity": 1, "message": "602", "line": 76, "column": 14, "nodeType": null, "messageId": "567", "endLine": 76, "endColumn": 19}, {"ruleId": "604", "severity": 2, "message": "605", "line": 222, "column": 18, "nodeType": "606", "messageId": "607", "suggestions": "650"}, {"ruleId": "565", "severity": 1, "message": "602", "line": 77, "column": 14, "nodeType": null, "messageId": "567", "endLine": 77, "endColumn": 19}, {"ruleId": "565", "severity": 1, "message": "616", "line": 12, "column": 3, "nodeType": null, "messageId": "567", "endLine": 12, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "603", "line": 180, "column": 11, "nodeType": null, "messageId": "567", "endLine": 180, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "651", "line": 18, "column": 3, "nodeType": null, "messageId": "567", "endLine": 18, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "652", "line": 28, "column": 3, "nodeType": null, "messageId": "567", "endLine": 28, "endColumn": 13}, {"ruleId": "565", "severity": 1, "message": "653", "line": 141, "column": 40, "nodeType": null, "messageId": "567", "endLine": 141, "endColumn": 46}, {"ruleId": "565", "severity": 1, "message": "603", "line": 146, "column": 11, "nodeType": null, "messageId": "567", "endLine": 146, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "603", "line": 37, "column": 11, "nodeType": null, "messageId": "567", "endLine": 37, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 75, "column": 35, "nodeType": "606", "messageId": "607", "suggestions": "654"}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "655", "line": 26, "column": 3, "nodeType": null, "messageId": "567", "endLine": 26, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "656", "line": 27, "column": 3, "nodeType": null, "messageId": "567", "endLine": 27, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "653", "line": 103, "column": 39, "nodeType": null, "messageId": "567", "endLine": 103, "endColumn": 45}, {"ruleId": "565", "severity": 1, "message": "603", "line": 109, "column": 11, "nodeType": null, "messageId": "567", "endLine": 109, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "613", "line": 6, "column": 29, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 6, "column": 46, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 6, "column": 58, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 13, "column": 3, "nodeType": null, "messageId": "567", "endLine": 13, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "657", "line": 20, "column": 3, "nodeType": null, "messageId": "567", "endLine": 20, "endColumn": 13}, {"ruleId": "565", "severity": 1, "message": "625", "line": 24, "column": 3, "nodeType": null, "messageId": "567", "endLine": 24, "endColumn": 8}, {"ruleId": "565", "severity": 1, "message": "621", "line": 25, "column": 3, "nodeType": null, "messageId": "567", "endLine": 25, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "603", "line": 256, "column": 11, "nodeType": null, "messageId": "567", "endLine": 256, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "621", "line": 26, "column": 3, "nodeType": null, "messageId": "567", "endLine": 26, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "625", "line": 27, "column": 3, "nodeType": null, "messageId": "567", "endLine": 27, "endColumn": 8}, {"ruleId": "565", "severity": 1, "message": "658", "line": 29, "column": 3, "nodeType": null, "messageId": "567", "endLine": 29, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "657", "line": 30, "column": 3, "nodeType": null, "messageId": "567", "endLine": 30, "endColumn": 13}, {"ruleId": "565", "severity": 1, "message": "653", "line": 155, "column": 46, "nodeType": null, "messageId": "567", "endLine": 155, "endColumn": 52}, {"ruleId": "565", "severity": 1, "message": "603", "line": 158, "column": 11, "nodeType": null, "messageId": "567", "endLine": 158, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "613", "line": 6, "column": 29, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 6, "column": 46, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 6, "column": 58, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 13, "column": 3, "nodeType": null, "messageId": "567", "endLine": 13, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "659", "line": 19, "column": 3, "nodeType": null, "messageId": "567", "endLine": 19, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "603", "line": 242, "column": 11, "nodeType": null, "messageId": "567", "endLine": 242, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 513, "column": 16, "nodeType": "606", "messageId": "607", "suggestions": "660"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 513, "column": 40, "nodeType": "606", "messageId": "607", "suggestions": "661"}, {"ruleId": "565", "severity": 1, "message": "621", "line": 20, "column": 3, "nodeType": null, "messageId": "567", "endLine": 20, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "653", "line": 150, "column": 39, "nodeType": null, "messageId": "567", "endLine": 150, "endColumn": 45}, {"ruleId": "565", "severity": 1, "message": "603", "line": 152, "column": 11, "nodeType": null, "messageId": "567", "endLine": 152, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 293, "column": 38, "nodeType": "606", "messageId": "607", "suggestions": "662"}, {"ruleId": "604", "severity": 2, "message": "605", "line": 315, "column": 33, "nodeType": "606", "messageId": "607", "suggestions": "663"}, {"ruleId": "604", "severity": 2, "message": "664", "line": 434, "column": 25, "nodeType": "606", "messageId": "607", "suggestions": "665"}, {"ruleId": "604", "severity": 2, "message": "664", "line": 434, "column": 47, "nodeType": "606", "messageId": "607", "suggestions": "666"}, {"ruleId": "565", "severity": 1, "message": "613", "line": 7, "column": 29, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 7, "column": 46, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 7, "column": 58, "nodeType": null, "messageId": "567", "endLine": 7, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "616", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "667", "line": 21, "column": 3, "nodeType": null, "messageId": "567", "endLine": 21, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "603", "line": 140, "column": 11, "nodeType": null, "messageId": "567", "endLine": 140, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "668", "line": 143, "column": 43, "nodeType": null, "messageId": "567", "endLine": 143, "endColumn": 55}, {"ruleId": "565", "severity": 1, "message": "669", "line": 6, "column": 10, "nodeType": null, "messageId": "567", "endLine": 6, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "670", "line": 24, "column": 3, "nodeType": null, "messageId": "567", "endLine": 24, "endColumn": 8}, {"ruleId": "565", "severity": 1, "message": "659", "line": 25, "column": 3, "nodeType": null, "messageId": "567", "endLine": 25, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "671", "line": 39, "column": 3, "nodeType": null, "messageId": "567", "endLine": 39, "endColumn": 8}, {"ruleId": "565", "severity": 1, "message": "603", "line": 49, "column": 11, "nodeType": null, "messageId": "567", "endLine": 49, "endColumn": 12}, {"ruleId": "628", "severity": 1, "message": "672", "line": 67, "column": 6, "nodeType": "630", "endLine": 67, "endColumn": 30, "suggestions": "673"}, {"ruleId": "565", "severity": 1, "message": "613", "line": 5, "column": 29, "nodeType": null, "messageId": "567", "endLine": 5, "endColumn": 44}, {"ruleId": "565", "severity": 1, "message": "614", "line": 5, "column": 46, "nodeType": null, "messageId": "567", "endLine": 5, "endColumn": 56}, {"ruleId": "565", "severity": 1, "message": "615", "line": 5, "column": 58, "nodeType": null, "messageId": "567", "endLine": 5, "endColumn": 67}, {"ruleId": "565", "severity": 1, "message": "603", "line": 112, "column": 11, "nodeType": null, "messageId": "567", "endLine": 112, "endColumn": 12}, {"ruleId": "628", "severity": 1, "message": "674", "line": 117, "column": 6, "nodeType": "630", "endLine": 117, "endColumn": 42, "suggestions": "675"}, {"ruleId": "565", "severity": 1, "message": "640", "line": 144, "column": 28, "nodeType": null, "messageId": "567", "endLine": 144, "endColumn": 37}, {"ruleId": "565", "severity": 1, "message": "640", "line": 151, "column": 32, "nodeType": null, "messageId": "567", "endLine": 151, "endColumn": 41}, {"ruleId": "565", "severity": 1, "message": "603", "line": 32, "column": 11, "nodeType": null, "messageId": "567", "endLine": 32, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "603", "line": 131, "column": 11, "nodeType": null, "messageId": "567", "endLine": 131, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "603", "line": 96, "column": 11, "nodeType": null, "messageId": "567", "endLine": 96, "endColumn": 12}, {"ruleId": "604", "severity": 2, "message": "605", "line": 160, "column": 17, "nodeType": "606", "messageId": "607", "suggestions": "676"}, {"ruleId": "565", "severity": 1, "message": "603", "line": 60, "column": 11, "nodeType": null, "messageId": "567", "endLine": 60, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "603", "line": 27, "column": 11, "nodeType": null, "messageId": "567", "endLine": 27, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "677", "line": 15, "column": 3, "nodeType": null, "messageId": "567", "endLine": 15, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "678", "line": 30, "column": 3, "nodeType": null, "messageId": "567", "endLine": 30, "endColumn": 16}, {"ruleId": "565", "severity": 1, "message": "603", "line": 147, "column": 34, "nodeType": null, "messageId": "567", "endLine": 147, "endColumn": 35}, {"ruleId": "565", "severity": 1, "message": "679", "line": 14, "column": 3, "nodeType": null, "messageId": "567", "endLine": 14, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "680", "line": 35, "column": 9, "nodeType": null, "messageId": "567", "endLine": 35, "endColumn": 15}, {"ruleId": "681", "severity": 2, "message": "682", "line": 5, "column": 18, "nodeType": "683", "messageId": "684", "endLine": 5, "endColumn": 28, "suggestions": "685"}, {"ruleId": "681", "severity": 2, "message": "682", "line": 5, "column": 18, "nodeType": "683", "messageId": "684", "endLine": 5, "endColumn": 31, "suggestions": "686"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 41, "column": 10, "nodeType": "570", "messageId": "571", "endLine": 41, "endColumn": 13, "suggestions": "687"}, {"ruleId": "565", "severity": 1, "message": "688", "line": 124, "column": 5, "nodeType": null, "messageId": "567", "endLine": 124, "endColumn": 9}, {"ruleId": "565", "severity": 1, "message": "689", "line": 125, "column": 5, "nodeType": null, "messageId": "567", "endLine": 125, "endColumn": 10}, {"ruleId": "565", "severity": 1, "message": "690", "line": 169, "column": 5, "nodeType": null, "messageId": "567", "endLine": 169, "endColumn": 12}, {"ruleId": "565", "severity": 1, "message": "691", "line": 220, "column": 5, "nodeType": null, "messageId": "567", "endLine": 220, "endColumn": 14}, {"ruleId": "565", "severity": 1, "message": "692", "line": 221, "column": 5, "nodeType": null, "messageId": "567", "endLine": 221, "endColumn": 15}, {"ruleId": "565", "severity": 1, "message": "693", "line": 305, "column": 5, "nodeType": null, "messageId": "567", "endLine": 305, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "694", "line": 306, "column": 5, "nodeType": null, "messageId": "567", "endLine": 306, "endColumn": 16}, {"ruleId": "568", "severity": 2, "message": "569", "line": 311, "column": 14, "nodeType": "570", "messageId": "571", "endLine": 311, "endColumn": 17, "suggestions": "695"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 359, "column": 13, "nodeType": "570", "messageId": "571", "endLine": 359, "endColumn": 16, "suggestions": "696"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 45, "column": 14, "nodeType": "570", "messageId": "571", "endLine": 45, "endColumn": 17, "suggestions": "697"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 280, "column": 31, "nodeType": "570", "messageId": "571", "endLine": 280, "endColumn": 34, "suggestions": "698"}, {"ruleId": "565", "severity": 1, "message": "691", "line": 398, "column": 27, "nodeType": null, "messageId": "567", "endLine": 398, "endColumn": 36}, {"ruleId": "568", "severity": 2, "message": "569", "line": 447, "column": 72, "nodeType": "570", "messageId": "571", "endLine": 447, "endColumn": 75, "suggestions": "699"}, "@typescript-eslint/no-unused-vars", "'password' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["700", "701"], ["702", "703"], ["704", "705"], "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", ["706", "707"], "'profile' is defined but never used.", "'isNewUser' is defined but never used.", "'session' is defined but never used.", "'token' is defined but never used.", ["708", "709"], ["710", "711"], ["712", "713"], ["714", "715"], ["716", "717"], ["718", "719"], ["720", "721"], ["722", "723"], ["724", "725"], ["726", "727"], ["728", "729"], ["730", "731"], ["732", "733"], ["734", "735"], ["736", "737"], ["738", "739"], ["740", "741"], ["742", "743"], "'NextAuth' is defined but never used.", "'JWT' is defined but never used.", "'error' is defined but never used.", "'t' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["744", "745", "746", "747"], ["748", "749", "750", "751"], ["752", "753", "754", "755"], ["756", "757", "758", "759"], ["760", "761", "762", "763"], "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Filter' is defined but never used.", "'ArrowUpDown' is defined but never used.", "'formatDate' is defined but never used.", "'TrendingDown' is defined but never used.", "'MousePointer' is defined but never used.", "'Calendar' is defined but never used.", "'MoreHorizontal' is defined but never used.", "'postId' is defined but never used.", "'Trash2' is defined but never used.", "'Users' is defined but never used.", "'Upload' is defined but never used.", ["764", "765"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", "ArrayExpression", ["766"], ["767", "768"], "'Edit' is defined but never used.", "'AlertCircle' is defined but never used.", "'MapPin' is defined but never used.", "'CreditCard' is defined but never used.", "'PieChart' is defined but never used.", ["769", "770", "771", "772"], ["773", "774", "775", "776"], "'productId' is defined but never used.", "'serviceId' is defined but never used.", "'UserX' is defined but never used.", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["777", "778", "779", "780"], ["781", "782", "783", "784"], ["785", "786", "787", "788"], ["789", "790", "791", "792"], "'Share2' is defined but never used.", "'ThumbsDown' is defined but never used.", "'params' is defined but never used.", ["793", "794", "795", "796"], "'ChevronLeft' is defined but never used.", "'ChevronRight' is defined but never used.", "'TrendingUp' is defined but never used.", "'Shield' is defined but never used.", "'CheckCircle' is defined but never used.", ["797", "798", "799", "800"], ["801", "802", "803", "804"], ["805", "806", "807", "808"], ["809", "810", "811", "812"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["813", "814", "815", "816"], ["817", "818", "819", "820"], "'ChevronDown' is defined but never used.", "'isInWishlist' is assigned a value but never used.", "'Badge' is defined but never used.", "'Clock' is defined but never used.", "'theme' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'messages.length'. Either include it or remove the dependency array.", ["821"], "React Hook useEffect has a missing dependency: 'loadRecommendations'. Either include it or remove the dependency array.", ["822"], ["823", "824", "825", "826"], "'FileText' is defined but never used.", "'MessageSquare' is defined but never used.", "'ShoppingBag' is defined but never used.", "'router' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["827"], ["828"], ["829", "830"], "'page' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'context' is defined but never used.", "'timeRange' is assigned a value but never used.", "'categories' is defined but never used.", "'userId' is defined but never used.", "'contentType' is defined but never used.", ["831", "832"], ["833", "834"], ["835", "836"], ["837", "838"], ["839", "840"], {"messageId": "841", "fix": "842", "desc": "843"}, {"messageId": "844", "fix": "845", "desc": "846"}, {"messageId": "841", "fix": "847", "desc": "843"}, {"messageId": "844", "fix": "848", "desc": "846"}, {"messageId": "841", "fix": "849", "desc": "843"}, {"messageId": "844", "fix": "850", "desc": "846"}, {"messageId": "841", "fix": "851", "desc": "843"}, {"messageId": "844", "fix": "852", "desc": "846"}, {"messageId": "841", "fix": "853", "desc": "843"}, {"messageId": "844", "fix": "854", "desc": "846"}, {"messageId": "841", "fix": "855", "desc": "843"}, {"messageId": "844", "fix": "856", "desc": "846"}, {"messageId": "841", "fix": "857", "desc": "843"}, {"messageId": "844", "fix": "858", "desc": "846"}, {"messageId": "841", "fix": "859", "desc": "843"}, {"messageId": "844", "fix": "860", "desc": "846"}, {"messageId": "841", "fix": "861", "desc": "843"}, {"messageId": "844", "fix": "862", "desc": "846"}, {"messageId": "841", "fix": "863", "desc": "843"}, {"messageId": "844", "fix": "864", "desc": "846"}, {"messageId": "841", "fix": "865", "desc": "843"}, {"messageId": "844", "fix": "866", "desc": "846"}, {"messageId": "841", "fix": "867", "desc": "843"}, {"messageId": "844", "fix": "868", "desc": "846"}, {"messageId": "841", "fix": "869", "desc": "843"}, {"messageId": "844", "fix": "870", "desc": "846"}, {"messageId": "841", "fix": "871", "desc": "843"}, {"messageId": "844", "fix": "872", "desc": "846"}, {"messageId": "841", "fix": "873", "desc": "843"}, {"messageId": "844", "fix": "874", "desc": "846"}, {"messageId": "841", "fix": "875", "desc": "843"}, {"messageId": "844", "fix": "876", "desc": "846"}, {"messageId": "841", "fix": "877", "desc": "843"}, {"messageId": "844", "fix": "878", "desc": "846"}, {"messageId": "841", "fix": "879", "desc": "843"}, {"messageId": "844", "fix": "880", "desc": "846"}, {"messageId": "841", "fix": "881", "desc": "843"}, {"messageId": "844", "fix": "882", "desc": "846"}, {"messageId": "841", "fix": "883", "desc": "843"}, {"messageId": "844", "fix": "884", "desc": "846"}, {"messageId": "841", "fix": "885", "desc": "843"}, {"messageId": "844", "fix": "886", "desc": "846"}, {"messageId": "841", "fix": "887", "desc": "843"}, {"messageId": "844", "fix": "888", "desc": "846"}, {"messageId": "889", "data": "890", "fix": "891", "desc": "892"}, {"messageId": "889", "data": "893", "fix": "894", "desc": "895"}, {"messageId": "889", "data": "896", "fix": "897", "desc": "898"}, {"messageId": "889", "data": "899", "fix": "900", "desc": "901"}, {"messageId": "889", "data": "902", "fix": "903", "desc": "892"}, {"messageId": "889", "data": "904", "fix": "905", "desc": "895"}, {"messageId": "889", "data": "906", "fix": "907", "desc": "898"}, {"messageId": "889", "data": "908", "fix": "909", "desc": "901"}, {"messageId": "889", "data": "910", "fix": "911", "desc": "892"}, {"messageId": "889", "data": "912", "fix": "913", "desc": "895"}, {"messageId": "889", "data": "914", "fix": "915", "desc": "898"}, {"messageId": "889", "data": "916", "fix": "917", "desc": "901"}, {"messageId": "889", "data": "918", "fix": "919", "desc": "892"}, {"messageId": "889", "data": "920", "fix": "921", "desc": "895"}, {"messageId": "889", "data": "922", "fix": "923", "desc": "898"}, {"messageId": "889", "data": "924", "fix": "925", "desc": "901"}, {"messageId": "889", "data": "926", "fix": "927", "desc": "892"}, {"messageId": "889", "data": "928", "fix": "929", "desc": "895"}, {"messageId": "889", "data": "930", "fix": "931", "desc": "898"}, {"messageId": "889", "data": "932", "fix": "933", "desc": "901"}, {"messageId": "841", "fix": "934", "desc": "843"}, {"messageId": "844", "fix": "935", "desc": "846"}, {"desc": "936", "fix": "937"}, {"messageId": "841", "fix": "938", "desc": "843"}, {"messageId": "844", "fix": "939", "desc": "846"}, {"messageId": "889", "data": "940", "fix": "941", "desc": "892"}, {"messageId": "889", "data": "942", "fix": "943", "desc": "895"}, {"messageId": "889", "data": "944", "fix": "945", "desc": "898"}, {"messageId": "889", "data": "946", "fix": "947", "desc": "901"}, {"messageId": "889", "data": "948", "fix": "949", "desc": "892"}, {"messageId": "889", "data": "950", "fix": "951", "desc": "895"}, {"messageId": "889", "data": "952", "fix": "953", "desc": "898"}, {"messageId": "889", "data": "954", "fix": "955", "desc": "901"}, {"messageId": "889", "data": "956", "fix": "957", "desc": "892"}, {"messageId": "889", "data": "958", "fix": "959", "desc": "895"}, {"messageId": "889", "data": "960", "fix": "961", "desc": "898"}, {"messageId": "889", "data": "962", "fix": "963", "desc": "901"}, {"messageId": "889", "data": "964", "fix": "965", "desc": "892"}, {"messageId": "889", "data": "966", "fix": "967", "desc": "895"}, {"messageId": "889", "data": "968", "fix": "969", "desc": "898"}, {"messageId": "889", "data": "970", "fix": "971", "desc": "901"}, {"messageId": "889", "data": "972", "fix": "973", "desc": "892"}, {"messageId": "889", "data": "974", "fix": "975", "desc": "895"}, {"messageId": "889", "data": "976", "fix": "977", "desc": "898"}, {"messageId": "889", "data": "978", "fix": "979", "desc": "901"}, {"messageId": "889", "data": "980", "fix": "981", "desc": "892"}, {"messageId": "889", "data": "982", "fix": "983", "desc": "895"}, {"messageId": "889", "data": "984", "fix": "985", "desc": "898"}, {"messageId": "889", "data": "986", "fix": "987", "desc": "901"}, {"messageId": "889", "data": "988", "fix": "989", "desc": "892"}, {"messageId": "889", "data": "990", "fix": "991", "desc": "895"}, {"messageId": "889", "data": "992", "fix": "993", "desc": "898"}, {"messageId": "889", "data": "994", "fix": "995", "desc": "901"}, {"messageId": "889", "data": "996", "fix": "997", "desc": "892"}, {"messageId": "889", "data": "998", "fix": "999", "desc": "895"}, {"messageId": "889", "data": "1000", "fix": "1001", "desc": "898"}, {"messageId": "889", "data": "1002", "fix": "1003", "desc": "901"}, {"messageId": "889", "data": "1004", "fix": "1005", "desc": "892"}, {"messageId": "889", "data": "1006", "fix": "1007", "desc": "895"}, {"messageId": "889", "data": "1008", "fix": "1009", "desc": "898"}, {"messageId": "889", "data": "1010", "fix": "1011", "desc": "901"}, {"messageId": "889", "data": "1012", "fix": "1013", "desc": "892"}, {"messageId": "889", "data": "1014", "fix": "1015", "desc": "895"}, {"messageId": "889", "data": "1016", "fix": "1017", "desc": "898"}, {"messageId": "889", "data": "1018", "fix": "1019", "desc": "901"}, {"messageId": "889", "data": "1020", "fix": "1021", "desc": "892"}, {"messageId": "889", "data": "1022", "fix": "1023", "desc": "895"}, {"messageId": "889", "data": "1024", "fix": "1025", "desc": "898"}, {"messageId": "889", "data": "1026", "fix": "1027", "desc": "901"}, {"messageId": "889", "data": "1028", "fix": "1029", "desc": "1030"}, {"messageId": "889", "data": "1031", "fix": "1032", "desc": "1033"}, {"messageId": "889", "data": "1034", "fix": "1035", "desc": "1036"}, {"messageId": "889", "data": "1037", "fix": "1038", "desc": "1039"}, {"messageId": "889", "data": "1040", "fix": "1041", "desc": "1030"}, {"messageId": "889", "data": "1042", "fix": "1043", "desc": "1033"}, {"messageId": "889", "data": "1044", "fix": "1045", "desc": "1036"}, {"messageId": "889", "data": "1046", "fix": "1047", "desc": "1039"}, {"desc": "1048", "fix": "1049"}, {"desc": "1050", "fix": "1051"}, {"messageId": "889", "data": "1052", "fix": "1053", "desc": "892"}, {"messageId": "889", "data": "1054", "fix": "1055", "desc": "895"}, {"messageId": "889", "data": "1056", "fix": "1057", "desc": "898"}, {"messageId": "889", "data": "1058", "fix": "1059", "desc": "901"}, {"messageId": "1060", "fix": "1061", "desc": "1062"}, {"messageId": "1060", "fix": "1063", "desc": "1062"}, {"messageId": "841", "fix": "1064", "desc": "843"}, {"messageId": "844", "fix": "1065", "desc": "846"}, {"messageId": "841", "fix": "1066", "desc": "843"}, {"messageId": "844", "fix": "1067", "desc": "846"}, {"messageId": "841", "fix": "1068", "desc": "843"}, {"messageId": "844", "fix": "1069", "desc": "846"}, {"messageId": "841", "fix": "1070", "desc": "843"}, {"messageId": "844", "fix": "1071", "desc": "846"}, {"messageId": "841", "fix": "1072", "desc": "843"}, {"messageId": "844", "fix": "1073", "desc": "846"}, {"messageId": "841", "fix": "1074", "desc": "843"}, {"messageId": "844", "fix": "1075", "desc": "846"}, "suggestUnknown", {"range": "1076", "text": "1077"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1078", "text": "1079"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1080", "text": "1077"}, {"range": "1081", "text": "1079"}, {"range": "1082", "text": "1077"}, {"range": "1083", "text": "1079"}, {"range": "1084", "text": "1077"}, {"range": "1085", "text": "1079"}, {"range": "1086", "text": "1077"}, {"range": "1087", "text": "1079"}, {"range": "1088", "text": "1077"}, {"range": "1089", "text": "1079"}, {"range": "1090", "text": "1077"}, {"range": "1091", "text": "1079"}, {"range": "1092", "text": "1077"}, {"range": "1093", "text": "1079"}, {"range": "1094", "text": "1077"}, {"range": "1095", "text": "1079"}, {"range": "1096", "text": "1077"}, {"range": "1097", "text": "1079"}, {"range": "1098", "text": "1077"}, {"range": "1099", "text": "1079"}, {"range": "1100", "text": "1077"}, {"range": "1101", "text": "1079"}, {"range": "1102", "text": "1077"}, {"range": "1103", "text": "1079"}, {"range": "1104", "text": "1077"}, {"range": "1105", "text": "1079"}, {"range": "1106", "text": "1077"}, {"range": "1107", "text": "1079"}, {"range": "1108", "text": "1077"}, {"range": "1109", "text": "1079"}, {"range": "1110", "text": "1077"}, {"range": "1111", "text": "1079"}, {"range": "1112", "text": "1077"}, {"range": "1113", "text": "1079"}, {"range": "1114", "text": "1077"}, {"range": "1115", "text": "1079"}, {"range": "1116", "text": "1077"}, {"range": "1117", "text": "1079"}, {"range": "1118", "text": "1077"}, {"range": "1119", "text": "1079"}, {"range": "1120", "text": "1077"}, {"range": "1121", "text": "1079"}, "replaceWithAlt", {"alt": "1122"}, {"range": "1123", "text": "1124"}, "Replace with `&apos;`.", {"alt": "1125"}, {"range": "1126", "text": "1127"}, "Replace with `&lsquo;`.", {"alt": "1128"}, {"range": "1129", "text": "1130"}, "Replace with `&#39;`.", {"alt": "1131"}, {"range": "1132", "text": "1133"}, "Replace with `&rsquo;`.", {"alt": "1122"}, {"range": "1134", "text": "1135"}, {"alt": "1125"}, {"range": "1136", "text": "1137"}, {"alt": "1128"}, {"range": "1138", "text": "1139"}, {"alt": "1131"}, {"range": "1140", "text": "1141"}, {"alt": "1122"}, {"range": "1142", "text": "1143"}, {"alt": "1125"}, {"range": "1144", "text": "1145"}, {"alt": "1128"}, {"range": "1146", "text": "1147"}, {"alt": "1131"}, {"range": "1148", "text": "1149"}, {"alt": "1122"}, {"range": "1150", "text": "1151"}, {"alt": "1125"}, {"range": "1152", "text": "1153"}, {"alt": "1128"}, {"range": "1154", "text": "1155"}, {"alt": "1131"}, {"range": "1156", "text": "1157"}, {"alt": "1122"}, {"range": "1158", "text": "1159"}, {"alt": "1125"}, {"range": "1160", "text": "1161"}, {"alt": "1128"}, {"range": "1162", "text": "1163"}, {"alt": "1131"}, {"range": "1164", "text": "1165"}, {"range": "1166", "text": "1077"}, {"range": "1167", "text": "1079"}, "Update the dependencies array to be: [loadData]", {"range": "1168", "text": "1169"}, {"range": "1170", "text": "1077"}, {"range": "1171", "text": "1079"}, {"alt": "1122"}, {"range": "1172", "text": "1173"}, {"alt": "1125"}, {"range": "1174", "text": "1175"}, {"alt": "1128"}, {"range": "1176", "text": "1177"}, {"alt": "1131"}, {"range": "1178", "text": "1179"}, {"alt": "1122"}, {"range": "1180", "text": "1181"}, {"alt": "1125"}, {"range": "1182", "text": "1183"}, {"alt": "1128"}, {"range": "1184", "text": "1185"}, {"alt": "1131"}, {"range": "1186", "text": "1187"}, {"alt": "1122"}, {"range": "1188", "text": "1189"}, {"alt": "1125"}, {"range": "1190", "text": "1191"}, {"alt": "1128"}, {"range": "1192", "text": "1193"}, {"alt": "1131"}, {"range": "1194", "text": "1195"}, {"alt": "1122"}, {"range": "1196", "text": "1197"}, {"alt": "1125"}, {"range": "1198", "text": "1199"}, {"alt": "1128"}, {"range": "1200", "text": "1201"}, {"alt": "1131"}, {"range": "1202", "text": "1203"}, {"alt": "1122"}, {"range": "1204", "text": "1205"}, {"alt": "1125"}, {"range": "1206", "text": "1207"}, {"alt": "1128"}, {"range": "1208", "text": "1209"}, {"alt": "1131"}, {"range": "1210", "text": "1211"}, {"alt": "1122"}, {"range": "1212", "text": "1213"}, {"alt": "1125"}, {"range": "1214", "text": "1215"}, {"alt": "1128"}, {"range": "1216", "text": "1217"}, {"alt": "1131"}, {"range": "1218", "text": "1219"}, {"alt": "1122"}, {"range": "1220", "text": "1221"}, {"alt": "1125"}, {"range": "1222", "text": "1223"}, {"alt": "1128"}, {"range": "1224", "text": "1225"}, {"alt": "1131"}, {"range": "1226", "text": "1227"}, {"alt": "1122"}, {"range": "1228", "text": "1229"}, {"alt": "1125"}, {"range": "1230", "text": "1231"}, {"alt": "1128"}, {"range": "1232", "text": "1233"}, {"alt": "1131"}, {"range": "1234", "text": "1235"}, {"alt": "1122"}, {"range": "1236", "text": "1237"}, {"alt": "1125"}, {"range": "1238", "text": "1239"}, {"alt": "1128"}, {"range": "1240", "text": "1241"}, {"alt": "1131"}, {"range": "1242", "text": "1243"}, {"alt": "1122"}, {"range": "1244", "text": "1245"}, {"alt": "1125"}, {"range": "1246", "text": "1247"}, {"alt": "1128"}, {"range": "1248", "text": "1249"}, {"alt": "1131"}, {"range": "1250", "text": "1251"}, {"alt": "1122"}, {"range": "1252", "text": "1253"}, {"alt": "1125"}, {"range": "1254", "text": "1255"}, {"alt": "1128"}, {"range": "1256", "text": "1257"}, {"alt": "1131"}, {"range": "1258", "text": "1259"}, {"alt": "1260"}, {"range": "1261", "text": "1262"}, "Replace with `&quot;`.", {"alt": "1263"}, {"range": "1264", "text": "1265"}, "Replace with `&ldquo;`.", {"alt": "1266"}, {"range": "1267", "text": "1268"}, "Replace with `&#34;`.", {"alt": "1269"}, {"range": "1270", "text": "1271"}, "Replace with `&rdquo;`.", {"alt": "1260"}, {"range": "1272", "text": "1273"}, {"alt": "1263"}, {"range": "1274", "text": "1275"}, {"alt": "1266"}, {"range": "1276", "text": "1277"}, {"alt": "1269"}, {"range": "1278", "text": "1279"}, "Update the dependencies array to be: [isOpen, initialMessage, messages.length]", {"range": "1280", "text": "1281"}, "Update the dependencies array to be: [userId, productId, category, limit, loadRecommendations]", {"range": "1282", "text": "1283"}, {"alt": "1122"}, {"range": "1284", "text": "1285"}, {"alt": "1125"}, {"range": "1286", "text": "1287"}, {"alt": "1128"}, {"range": "1288", "text": "1289"}, {"alt": "1131"}, {"range": "1290", "text": "1291"}, "replaceEmptyInterfaceWithSuper", {"range": "1292", "text": "1293"}, "Replace empty interface with a type alias.", {"range": "1294", "text": "1295"}, {"range": "1296", "text": "1077"}, {"range": "1297", "text": "1079"}, {"range": "1298", "text": "1077"}, {"range": "1299", "text": "1079"}, {"range": "1300", "text": "1077"}, {"range": "1301", "text": "1079"}, {"range": "1302", "text": "1077"}, {"range": "1303", "text": "1079"}, {"range": "1304", "text": "1077"}, {"range": "1305", "text": "1079"}, {"range": "1306", "text": "1077"}, {"range": "1307", "text": "1079"}, [1378, 1381], "unknown", [1378, 1381], "never", [518, 521], [518, 521], [944, 947], [944, 947], [461, 464], [461, 464], [2607, 2610], [2607, 2610], [2862, 2865], [2862, 2865], [3192, 3195], [3192, 3195], [3208, 3211], [3208, 3211], [3356, 3359], [3356, 3359], [3370, 3373], [3370, 3373], [3534, 3537], [3534, 3537], [3548, 3551], [3548, 3551], [3563, 3566], [3563, 3566], [1226, 1229], [1226, 1229], [1236, 1239], [1236, 1239], [1505, 1508], [1505, 1508], [1515, 1518], [1515, 1518], [6407, 6410], [6407, 6410], [6522, 6525], [6522, 6525], [6673, 6676], [6673, 6676], [7697, 7700], [7697, 7700], [7759, 7762], [7759, 7762], "&apos;", [14087, 14159], "\n                    You haven&apos;t cancelled any orders\n                  ", "&lsquo;", [14087, 14159], "\n                    You haven&lsquo;t cancelled any orders\n                  ", "&#39;", [14087, 14159], "\n                    You haven&#39;t cancelled any orders\n                  ", "&rsquo;", [14087, 14159], "\n                    You haven&rsquo;t cancelled any orders\n                  ", [2258, 2323], "\n            Here&apos;s what's happening with your account\n          ", [2258, 2323], "\n            Here&lsquo;s what's happening with your account\n          ", [2258, 2323], "\n            Here&#39;s what's happening with your account\n          ", [2258, 2323], "\n            Here&rsquo;s what's happening with your account\n          ", [2258, 2323], "\n            Here's what&apos;s happening with your account\n          ", [2258, 2323], "\n            Here's what&lsquo;s happening with your account\n          ", [2258, 2323], "\n            Here's what&#39;s happening with your account\n          ", [2258, 2323], "\n            Here's what&rsquo;s happening with your account\n          ", [8306, 8366], "\n                Items you&apos;ve saved for later\n              ", [8306, 8366], "\n                Items you&lsquo;ve saved for later\n              ", [8306, 8366], "\n                Items you&#39;ve saved for later\n              ", [8306, 8366], "\n                Items you&rsquo;ve saved for later\n              ", [14260, 14304], "\n              You&apos;re a valued member since ", [14260, 14304], "\n              You&lsquo;re a valued member since ", [14260, 14304], "\n              You&#39;re a valued member since ", [14260, 14304], "\n              You&rsquo;re a valued member since ", [2755, 2758], [2755, 2758], [3117, 3119], "[loadData]", [19122, 19125], [19122, 19125], [4115, 4197], "\n              Welcome back! Here&apos;s what's happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here&lsquo;s what's happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here&#39;s what's happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here&rsquo;s what's happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here's what&apos;s happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here's what&lsquo;s happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here's what&#39;s happening with your store.\n            ", [4115, 4197], "\n              Welcome back! Here's what&rsquo;s happening with your store.\n            ", [2857, 2915], "\n                We&apos;ve sent password reset instructions to", [2857, 2915], "\n                We&lsquo;ve sent password reset instructions to", [2857, 2915], "\n                We&#39;ve sent password reset instructions to", [2857, 2915], "\n                We&rsquo;ve sent password reset instructions to", [3195, 3246], "Didn&apos;t receive the email? Check your spam folder or", [3195, 3246], "Didn&lsquo;t receive the email? Check your spam folder or", [3195, 3246], "Didn&#39;t receive the email? Check your spam folder or", [3195, 3246], "Didn&rsquo;t receive the email? Check your spam folder or", [4655, 4756], "\n              Enter your email address and we&apos;ll send you a link to reset your password\n            ", [4655, 4756], "\n              Enter your email address and we&lsquo;ll send you a link to reset your password\n            ", [4655, 4756], "\n              Enter your email address and we&#39;ll send you a link to reset your password\n            ", [4655, 4756], "\n              Enter your email address and we&rsquo;ll send you a link to reset your password\n            ", [8423, 8460], "\n              Don&apos;t have an account?", [8423, 8460], "\n              Don&lsquo;t have an account?", [8423, 8460], "\n              Don&#39;t have an account?", [8423, 8460], "\n              Don&rsquo;t have an account?", [1958, 2072], "\n              Looks like you haven&apos;t added any items to your cart yet. Start shopping to fill it up!\n            ", [1958, 2072], "\n              Looks like you haven&lsquo;t added any items to your cart yet. Start shopping to fill it up!\n            ", [1958, 2072], "\n              Looks like you haven&#39;t added any items to your cart yet. Start shopping to fill it up!\n            ", [1958, 2072], "\n              Looks like you haven&rsquo;t added any items to your cart yet. Start shopping to fill it up!\n            ", [16441, 16598], "\n            Can&apos;t find exactly what you're looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can&lsquo;t find exactly what you're looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can&#39;t find exactly what you're looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can&rsquo;t find exactly what you're looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can't find exactly what you&apos;re looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can't find exactly what you&lsquo;re looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can't find exactly what you&#39;re looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [16441, 16598], "\n            Can't find exactly what you&rsquo;re looking for? We specialize in creating custom solutions tailored to your unique business requirements.\n          ", [10329, 10344], "What&apos;s Included", [10329, 10344], "What&lsquo;s Included", [10329, 10344], "What&#39;s Included", [10329, 10344], "What&rsquo;s Included", [11264, 11355], "\n                        What you&apos;ll receive upon project completion\n                      ", [11264, 11355], "\n                        What you&lsquo;ll receive upon project completion\n                      ", [11264, 11355], "\n                        What you&#39;ll receive upon project completion\n                      ", [11264, 11355], "\n                        What you&rsquo;ll receive upon project completion\n                      ", "&quot;", [16644, 16670], "\n                        &quot;", "&ldquo;", [16644, 16670], "\n                        &ldquo;", "&#34;", [16644, 16670], "\n                        &#34;", "&rdquo;", [16644, 16670], "\n                        &rdquo;", [16691, 16715], "&quot;\n                      ", [16691, 16715], "&ldquo;\n                      ", [16691, 16715], "&#34;\n                      ", [16691, 16715], "&rdquo;\n                      ", [1883, 1907], "[isOpen, initialMessage, messages.length]", [2695, 2731], "[userId, productId, category, limit, loadRecommendations]", [5669, 5804], "\n              We&apos;re committed to delivering exceptional value through innovation, quality, and customer-centric approach.\n            ", [5669, 5804], "\n              We&lsquo;re committed to delivering exceptional value through innovation, quality, and customer-centric approach.\n            ", [5669, 5804], "\n              We&#39;re committed to delivering exceptional value through innovation, quality, and customer-centric approach.\n            ", [5669, 5804], "\n              We&rsquo;re committed to delivering exceptional value through innovation, quality, and customer-centric approach.\n            ", [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [872, 875], [872, 875], [9447, 9450], [9447, 9450], [10553, 10556], [10553, 10556], [1059, 1062], [1059, 1062], [9950, 9953], [9950, 9953], [14342, 14345], [14342, 14345]]