(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7357],{381:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},749:e=>{e.exports={style:{fontFamily:"'Tajawal', 'Tajawal Fallback'",fontStyle:"normal"},className:"__className_4beeb2",variable:"__variable_4beeb2"}},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,N:()=>c});var s=r(2115),i=(e,t,r,s,i,n,a,o)=>{let u=document.documentElement,l=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,s=r&&n?i.map(e=>n[e]||e):i;r?(u.classList.remove(...s),u.classList.add(n&&n[t]?n[t]:t)):u.setAttribute(e,t)}),r=t,o&&l.includes(r)&&(u.style.colorScheme=r)}if(s)c(s);else try{let e=localStorage.getItem(t)||r,s=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(s)}catch(e){}},n=["light","dark"],a="(prefers-color-scheme: dark)",o=s.createContext(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=s.useContext(o))?e:u},c=e=>s.useContext(o)?s.createElement(s.Fragment,null,e.children):s.createElement(d,{...e}),h=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:i=!0,enableColorScheme:u=!0,storageKey:l="theme",themes:c=h,defaultTheme:d=i?"system":"light",attribute:v="data-theme",value:b,children:g,nonce:w,scriptProps:E}=e,[C,S]=s.useState(()=>p(l,d)),[x,P]=s.useState(()=>"system"===C?m():C),T=b?Object.values(b):c,O=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&i&&(t=m());let s=b?b[t]:t,a=r?y(w):null,o=document.documentElement,l=e=>{"class"===e?(o.classList.remove(...T),s&&o.classList.add(s)):e.startsWith("data-")&&(s?o.setAttribute(e,s):o.removeAttribute(e))};if(Array.isArray(v)?v.forEach(l):l(v),u){let e=n.includes(d)?d:null,r=n.includes(t)?t:e;o.style.colorScheme=r}null==a||a()},[w]),k=s.useCallback(e=>{let t="function"==typeof e?e(C):e;S(t);try{localStorage.setItem(l,t)}catch(e){}},[C]),A=s.useCallback(e=>{P(m(e)),"system"===C&&i&&!t&&O("system")},[C,t]);s.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),s.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?S(e.newValue):k(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[k]),s.useEffect(()=>{O(null!=t?t:C)},[t,C]);let D=s.useMemo(()=>({theme:C,setTheme:k,forcedTheme:t,resolvedTheme:"system"===C?x:C,themes:i?[...c,"system"]:c,systemTheme:i?x:void 0}),[C,k,t,x,i,c]);return s.createElement(o.Provider,{value:D},s.createElement(f,{forcedTheme:t,storageKey:l,attribute:v,enableSystem:i,enableColorScheme:u,defaultTheme:d,value:b,themes:c,nonce:w,scriptProps:E}),g)},f=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:n,enableSystem:a,enableColorScheme:o,defaultTheme:u,value:l,themes:c,nonce:h,scriptProps:d}=e,f=JSON.stringify([n,r,u,t,c,l,a,o]).slice(1,-1);return s.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(i.toString(),")(").concat(f,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},1976:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},2098:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2138:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2525:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},2922:(e,t,r)=>{"use strict";r.d(t,{E:()=>L});var s="undefined"==typeof window||"Deno"in globalThis;function i(){}function n(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:r="all",exact:s,fetchStatus:i,predicate:n,queryKey:a,stale:o}=e;if(a){if(s){if(t.queryHash!==u(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!i||i===t.state.fetchStatus)&&(!n||!!n(t))}function o(e,t){let{exact:r,status:s,predicate:i,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(r){if(l(t.options.mutationKey)!==l(n))return!1}else if(!c(t.options.mutationKey,n))return!1}return(!s||t.state.status===s)&&(!i||!!i(t))}function u(e,t){return(t?.queryKeyHashFn||l)(e)}function l(e){return JSON.stringify(e,(e,t)=>d(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>c(e[r],t[r]))}function h(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function d(e){if(!f(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!f(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,r=0){let s=[...e,t];return r&&s.length>r?s.slice(1):s}function y(e,t,r=0){let s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var m=Symbol();function v(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==m?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var b=e=>setTimeout(e,0),g=function(){let e=[],t=0,r=e=>{e()},s=e=>{e()},i=b,n=s=>{t?e.push(s):i(()=>{r(s)})};return{batch:n=>{let a;t++;try{a=n()}finally{--t||(()=>{let t=e;e=[],t.length&&i(()=>{s(()=>{t.forEach(e=>{r(e)})})})})()}return a},batchCalls:e=>(...t)=>{n(()=>{e(...t)})},schedule:n,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{s=e},setScheduler:e=>{i=e}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},E=new class extends w{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!s&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},C=new class extends w{#s=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!s&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#s!==e&&(this.#s=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#s}};function S(e){return Math.min(1e3*2**e,3e4)}function x(e){return(e??"online")!=="online"||C.isOnline()}var P=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function T(e){return e instanceof P}function O(e){let t,r=!1,i=0,n=!1,a=function(){let e,t,r=new Promise((r,s)=>{e=r,t=s});function s(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},r.reject=e=>{s({status:"rejected",reason:e}),t(e)},r}(),o=()=>E.isFocused()&&("always"===e.networkMode||C.isOnline())&&e.canRun(),u=()=>x(e.networkMode)&&e.canRun(),l=r=>{n||(n=!0,e.onSuccess?.(r),t?.(),a.resolve(r))},c=r=>{n||(n=!0,e.onError?.(r),t?.(),a.reject(r))},h=()=>new Promise(r=>{t=e=>{(n||o())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,n||e.onContinue?.()}),d=()=>{let t;if(n)return;let a=0===i?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch(t=>{if(n)return;let a=e.retry??3*!s,u=e.retryDelay??S,l="function"==typeof u?u(i,t):u,f=!0===a||"number"==typeof a&&i<a||"function"==typeof a&&a(i,t);if(r||!f)return void c(t);i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,l)}).then(()=>o()?void 0:h()).then(()=>{r?c(t):d()})})};return{promise:a,cancel:t=>{n||(c(new P(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:u,start:()=>(u()?d():h().then(d),a)}}var k=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},A=class extends k{#n;#a;#o;#u;#l;#c;#h;constructor(e){super(),this.#h=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#o=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#n=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){var r,s;let i=(r=this.state.data,"function"==typeof(s=this.options).structuralSharing?s.structuralSharing(r,e):!1!==s.structuralSharing?function e(t,r){if(t===r)return t;let s=h(t)&&h(r);if(s||d(t)&&d(r)){let i=s?t:Object.keys(t),n=i.length,a=s?r:Object.keys(r),o=a.length,u=s?[]:{},l=new Set(i),c=0;for(let i=0;i<o;i++){let n=s?i:a[i];(!s&&l.has(n)||s)&&void 0===t[n]&&void 0===r[n]?(u[n]=void 0,c++):(u[n]=e(t[n],r[n]),u[n]===t[n]&&void 0!==t[n]&&c++)}return n===o&&c===n?t:u}return r}(r,e):e);return this.#d({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#l?.promise;return this.#l?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(e=>{var t;return!1!==(t=e.options.enabled,"function"==typeof t?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===n(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#l&&(this.#h?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,s=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,r.signal)})},i=()=>{let e=v(this.options,t),r=(()=>{let e={client:this.#u,queryKey:this.queryKey,meta:this.meta};return s(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,r,this):e(r)},n=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:i};return s(e),e})();this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let a=e=>{T(e)&&e.silent||this.#d({type:"error",error:e}),T(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#l=O({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void a(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){a(e);return}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#l.start()}#d(e){let t=t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var r;return{...t,...(r=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:x(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return this.#a=void 0,{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=e.error;if(T(s)&&s.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}};this.state=t(this.state),g.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:e})})}},D=class extends w{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,r){let s=t.queryKey,i=t.queryHash??u(s,t),n=this.get(i);return n||(n=new A({client:e,queryKey:s,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(n)),n}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){g.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){g.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){g.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},F=class extends k{#p;#y;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#y=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#d({type:"continue"})};this.#l=O({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let r="pending"===this.state.status,s=!this.#l.canStart();try{if(r)t();else{this.#d({type:"pending",variables:e,isPaused:s}),await this.#y.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:s})}let i=await this.#l.start();return await this.#y.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#y.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#d({type:"success",data:i}),i}catch(t){try{throw await this.#y.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#y.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#y.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),g.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#y.notify({mutation:this,type:"updated",action:e})})}},M=class extends w{constructor(e={}){super(),this.config=e,this.#m=new Set,this.#v=new Map,this.#b=0}#m;#v;#b;build(e,t,r){let s=new F({mutationCache:this,mutationId:++this.#b,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#m.add(e);let t=q(e);if("string"==typeof t){let r=this.#v.get(t);r?r.push(e):this.#v.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#m.delete(e)){let t=q(e);if("string"==typeof t){let r=this.#v.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#v.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=q(e);if("string"!=typeof t)return!0;{let r=this.#v.get(t),s=r?.find(e=>"pending"===e.state.status);return!s||s===e}}runNext(e){let t=q(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#v.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){g.batch(()=>{this.#m.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>o(t,e))}findAll(e={}){return this.getAll().filter(t=>o(e,t))}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return g.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function q(e){return e.options.scope?.id}function R(e){return{onFetch:(t,r)=>{let s=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],a=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,l=async()=>{let r=!1,l=v(t.options,t.fetchOptions),c=async(e,s,i)=>{if(r)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:s,direction:i?"backward":"forward",meta:t.options.meta};return Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)}),e})(),a=await l(n),{maxPages:o}=t.options,u=i?y:p;return{pages:u(e.pages,a,o),pageParams:u(e.pageParams,s,o)}};if(i&&n.length){let e="backward"===i,t={pages:n,pageParams:a},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:j)(s,t);o=await c(t,r,e)}else{let t=e??n.length;do{let e=0===u?a[0]??s.initialPageParam:j(s,o);if(u>0&&null==e)break;o=await c(o,e),u++}while(u<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=l}}}function j(e,{pages:t,pageParams:r}){let s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}var L=class{#g;#y;#c;#w;#E;#C;#S;#x;constructor(e={}){this.#g=e.queryCache||new D,this.#y=e.mutationCache||new M,this.#c=e.defaultOptions||{},this.#w=new Map,this.#E=new Map,this.#C=0}mount(){this.#C++,1===this.#C&&(this.#S=E.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#x=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#C--,0===this.#C&&(this.#S?.(),this.#S=void 0,this.#x?.(),this.#x=void 0)}isFetching(e){return this.#g.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#y.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#g.build(this,t),s=r.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(n(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#g.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let s=this.defaultQueryOptions({queryKey:e}),i=this.#g.get(s.queryHash),n=i?.state.data,a="function"==typeof t?t(n):t;if(void 0!==a)return this.#g.build(this,s).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return g.batch(()=>this.#g.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state}removeQueries(e){let t=this.#g;g.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#g;return g.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(g.batch(()=>this.#g.findAll(e).map(e=>e.cancel(r)))).then(i).catch(i)}invalidateQueries(e,t={}){return g.batch(()=>(this.#g.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(g.batch(()=>this.#g.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#g.build(this,t);return r.isStaleByTime(n(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=R(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=R(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return C.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#w.set(l(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#w.values()],r={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#E.set(l(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#E.values()],r={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=u(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===m&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}}},3156:(e,t,r)=>{"use strict";r.d(t,{rc:()=>eu,bm:()=>el,VY:()=>eo,Kq:()=>es,bL:()=>en,hE:()=>ea,LM:()=>ei});var s,i=r(2115),n=r(7650),a=r(5185),o=r(6101),u=r(7328),l=r(6081),c=r(3655),h=r(9033),d=r(5155),f="dismissableLayer.update",p=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=i.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:l,onPointerDownOutside:y,onFocusOutside:m,onInteractOutside:g,onDismiss:w,...E}=e,C=i.useContext(p),[S,x]=i.useState(null),P=null!=(n=null==S?void 0:S.ownerDocument)?n:null==(r=globalThis)?void 0:r.document,[,T]=i.useState({}),O=(0,o.s)(t,e=>x(e)),k=Array.from(C.layers),[A]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),D=k.indexOf(A),F=S?k.indexOf(S):-1,M=C.layersWithOutsidePointerEventsDisabled.size>0,q=F>=D,R=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,s=(0,h.c)(e),n=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let t=function(){b("dismissableLayer.pointerDownOutside",s,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);n.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,s]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...C.branches].some(e=>e.contains(t));q&&!r&&(null==y||y(e),null==g||g(e),e.defaultPrevented||null==w||w())},P),j=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,s=(0,h.c)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&b("dismissableLayer.focusOutside",s,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,s]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==m||m(e),null==g||g(e),e.defaultPrevented||null==w||w())},P);return!function(e,t=globalThis?.document){let r=(0,h.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{F===C.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},P),i.useEffect(()=>{if(S)return u&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(s=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(S)),C.layers.add(S),v(),()=>{u&&1===C.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=s)}},[S,P,u,C]),i.useEffect(()=>()=>{S&&(C.layers.delete(S),C.layersWithOutsidePointerEventsDisabled.delete(S),v())},[S,C]),i.useEffect(()=>{let e=()=>T({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,d.jsx)(c.sG.div,{...E,ref:O,style:{pointerEvents:M?q?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,R.onPointerDownCapture)})});y.displayName="DismissableLayer";var m=i.forwardRef((e,t)=>{let r=i.useContext(p),s=i.useRef(null),n=(0,o.s)(t,s);return i.useEffect(()=>{let e=s.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,d.jsx)(c.sG.div,{...e,ref:n})});function v(){let e=new CustomEvent(f);document.dispatchEvent(e)}function b(e,t,r,s){let{discrete:i}=s,n=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&n.addEventListener(e,t,{once:!0}),i?(0,c.hO)(n,a):n.dispatchEvent(a)}m.displayName="DismissableLayerBranch";var g=r(2712),w=i.forwardRef((e,t)=>{var r,s;let{container:a,...o}=e,[u,l]=i.useState(!1);(0,g.N)(()=>l(!0),[]);let h=a||u&&(null==(s=globalThis)||null==(r=s.document)?void 0:r.body);return h?n.createPortal((0,d.jsx)(c.sG.div,{...o,ref:t}),h):null});w.displayName="Portal";var E=r(8905),C=r(5845),S=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),x=i.forwardRef((e,t)=>(0,d.jsx)(c.sG.span,{...e,ref:t,style:{...S,...e.style}}));x.displayName="VisuallyHidden";var P="ToastProvider",[T,O,k]=(0,u.N)("Toast"),[A,D]=(0,l.A)("Toast",[k]),[F,M]=A(P),q=e=>{let{__scopeToast:t,label:r="Notification",duration:s=5e3,swipeDirection:n="right",swipeThreshold:a=50,children:o}=e,[u,l]=i.useState(null),[c,h]=i.useState(0),f=i.useRef(!1),p=i.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(P,"`. Expected non-empty `string`.")),(0,d.jsx)(T.Provider,{scope:t,children:(0,d.jsx)(F,{scope:t,label:r,duration:s,swipeDirection:n,swipeThreshold:a,toastCount:c,viewport:u,onViewportChange:l,onToastAdd:i.useCallback(()=>h(e=>e+1),[]),onToastRemove:i.useCallback(()=>h(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:o})})};q.displayName=P;var R="ToastViewport",j=["F8"],L="toast.viewportPause",I="toast.viewportResume",N=i.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:s=j,label:n="Notifications ({hotkey})",...a}=e,u=M(R,r),l=O(r),h=i.useRef(null),f=i.useRef(null),p=i.useRef(null),y=i.useRef(null),v=(0,o.s)(t,y,u.onViewportChange),b=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=u.toastCount>0;i.useEffect(()=>{let e=e=>{var t;0!==s.length&&s.every(t=>e[t]||e.code===t)&&(null==(t=y.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[s]),i.useEffect(()=>{let e=h.current,t=y.current;if(g&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(L);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},s=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(I);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||s()},n=()=>{e.contains(document.activeElement)||s()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",n),window.addEventListener("blur",r),window.addEventListener("focus",s),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",n),window.removeEventListener("blur",r),window.removeEventListener("focus",s)}}},[g,u.isClosePausedRef]);let w=i.useCallback(e=>{let{tabbingDirection:t}=e,r=l().map(e=>{let r=e.ref.current,s=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?s:s.reverse()});return("forwards"===t?r.reverse():r).flat()},[l]);return i.useEffect(()=>{let e=y.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var s,i,n;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null==(s=f.current)||s.focus();return}let o=w({tabbingDirection:a?"backwards":"forwards"}),u=o.findIndex(e=>e===r);er(o.slice(u+1))?t.preventDefault():a?null==(i=f.current)||i.focus():null==(n=p.current)||n.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[l,w]),(0,d.jsxs)(m,{ref:h,role:"region","aria-label":n.replace("{hotkey}",b),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&(0,d.jsx)(Q,{ref:f,onFocusFromOutsideViewport:()=>{er(w({tabbingDirection:"forwards"}))}}),(0,d.jsx)(T.Slot,{scope:r,children:(0,d.jsx)(c.sG.ol,{tabIndex:-1,...a,ref:v})}),g&&(0,d.jsx)(Q,{ref:p,onFocusFromOutsideViewport:()=>{er(w({tabbingDirection:"backwards"}))}})]})});N.displayName=R;var K="ToastFocusProxy",Q=i.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:s,...i}=e,n=M(K,r);return(0,d.jsx)(x,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null==(t=n.viewport)?void 0:t.contains(r))||s()}})});Q.displayName=K;var H="Toast",U=i.forwardRef((e,t)=>{let{forceMount:r,open:s,defaultOpen:i,onOpenChange:n,...o}=e,[u,l]=(0,C.i)({prop:s,defaultProp:null==i||i,onChange:n,caller:H});return(0,d.jsx)(E.C,{present:r||u,children:(0,d.jsx)(V,{open:u,...o,ref:t,onClose:()=>l(!1),onPause:(0,h.c)(e.onPause),onResume:(0,h.c)(e.onResume),onSwipeStart:(0,a.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,a.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),l(!1)})})})});U.displayName=H;var[_,G]=A(H,{onClose(){}}),V=i.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:u,open:l,onClose:f,onEscapeKeyDown:p,onPause:m,onResume:v,onSwipeStart:b,onSwipeMove:g,onSwipeCancel:w,onSwipeEnd:E,...C}=e,S=M(H,r),[x,P]=i.useState(null),O=(0,o.s)(t,e=>P(e)),k=i.useRef(null),A=i.useRef(null),D=u||S.duration,F=i.useRef(0),q=i.useRef(D),R=i.useRef(0),{onToastAdd:j,onToastRemove:N}=S,K=(0,h.c)(()=>{var e;(null==x?void 0:x.contains(document.activeElement))&&(null==(e=S.viewport)||e.focus()),f()}),Q=i.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(R.current),F.current=new Date().getTime(),R.current=window.setTimeout(K,e))},[K]);i.useEffect(()=>{let e=S.viewport;if(e){let t=()=>{Q(q.current),null==v||v()},r=()=>{let e=new Date().getTime()-F.current;q.current=q.current-e,window.clearTimeout(R.current),null==m||m()};return e.addEventListener(L,r),e.addEventListener(I,t),()=>{e.removeEventListener(L,r),e.removeEventListener(I,t)}}},[S.viewport,D,m,v,Q]),i.useEffect(()=>{l&&!S.isClosePausedRef.current&&Q(D)},[l,D,S.isClosePausedRef,Q]),i.useEffect(()=>(j(),()=>N()),[j,N]);let U=i.useMemo(()=>x?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var s;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(s=t).nodeType===s.ELEMENT_NODE){let s=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!s)if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}),r}(x):null,[x]);return S.viewport?(0,d.jsxs)(d.Fragment,{children:[U&&(0,d.jsx)(z,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:U}),(0,d.jsx)(_,{scope:r,onClose:K,children:n.createPortal((0,d.jsx)(T.ItemSlot,{scope:r,children:(0,d.jsx)(y,{asChild:!0,onEscapeKeyDown:(0,a.m)(p,()=>{S.isFocusedToastEscapeKeyDownRef.current||K(),S.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,d.jsx)(c.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":S.swipeDirection,...C,ref:O,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(S.isFocusedToastEscapeKeyDownRef.current=!0,K()))}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{if(!k.current)return;let t=e.clientX-k.current.x,r=e.clientY-k.current.y,s=!!A.current,i=["left","right"].includes(S.swipeDirection),n=["left","up"].includes(S.swipeDirection)?Math.min:Math.max,a=i?n(0,t):0,o=i?0:n(0,r),u="touch"===e.pointerType?10:2,l={x:a,y:o},c={originalEvent:e,delta:l};s?(A.current=l,ee("toast.swipeMove",g,c,{discrete:!1})):et(l,S.swipeDirection,u)?(A.current=l,ee("toast.swipeStart",b,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(k.current=null)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=A.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),A.current=null,k.current=null,t){let r=e.currentTarget,s={originalEvent:e,delta:t};et(t,S.swipeDirection,S.swipeThreshold)?ee("toast.swipeEnd",E,s,{discrete:!0}):ee("toast.swipeCancel",w,s,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),S.viewport)})]}):null}),z=e=>{let{__scopeToast:t,children:r,...s}=e,n=M(H,t),[a,o]=i.useState(!1),[u,l]=i.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,h.c)(e);(0,g.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>o(!0)),i.useEffect(()=>{let e=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,d.jsx)(w,{asChild:!0,children:(0,d.jsx)(x,{...s,children:a&&(0,d.jsxs)(d.Fragment,{children:[n.label," ",r]})})})},W=i.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e;return(0,d.jsx)(c.sG.div,{...s,ref:t})});W.displayName="ToastTitle";var B=i.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e;return(0,d.jsx)(c.sG.div,{...s,ref:t})});B.displayName="ToastDescription";var J="ToastAction",X=i.forwardRef((e,t)=>{let{altText:r,...s}=e;return r.trim()?(0,d.jsx)(Z,{altText:r,asChild:!0,children:(0,d.jsx)($,{...s,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(J,"`. Expected non-empty `string`.")),null)});X.displayName=J;var Y="ToastClose",$=i.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e,i=G(Y,r);return(0,d.jsx)(Z,{asChild:!0,children:(0,d.jsx)(c.sG.button,{type:"button",...s,ref:t,onClick:(0,a.m)(e.onClick,i.onClose)})})});$.displayName=Y;var Z=i.forwardRef((e,t)=>{let{__scopeToast:r,altText:s,...i}=e;return(0,d.jsx)(c.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":s||void 0,...i,ref:t})});function ee(e,t,r,s){let{discrete:i}=s,n=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&n.addEventListener(e,t,{once:!0}),i?(0,c.hO)(n,a):n.dispatchEvent(a)}var et=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=Math.abs(e.x),i=Math.abs(e.y),n=s>i;return"left"===t||"right"===t?n&&s>r:!n&&i>r};function er(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var es=q,ei=N,en=U,ea=W,eo=B,eu=X,el=$},3509:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>a});var s=r(2115);let i=e=>{let t,r=new Set,s=(e,s)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=s?s:"object"!=typeof i||null===i)?i:Object.assign({},t,i),r.forEach(r=>r(t,e))}},i=()=>t,n={setState:s,getState:i,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(s,i,n);return n},n=e=>{let t=(e=>e?i(e):i)(e),r=e=>(function(e,t=e=>e){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},a=e=>e?n(e):n},6151:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},6707:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6715:(e,t,r)=>{"use strict";r.d(t,{Ht:()=>a});var s=r(2115),i=r(5155),n=s.createContext(void 0),a=e=>{let{client:t,children:r}=e;return s.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(n.Provider,{value:t,children:r})}},6786:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>i});let s=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>s(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>s(t)(e)}}},i=(e,t)=>(r,i,n)=>{let a,o={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let s=e=>null===e?null:JSON.parse(e,void 0),i=null!=(t=r.getItem(e))?t:null;return i instanceof Promise?i.then(s):s(i)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,l=new Set,c=new Set,h=o.storage;if(!h)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},i,n);let d=()=>{let e=o.partialize({...i()});return h.setItem(o.name,{state:e,version:o.version})},f=n.setState;n.setState=(e,t)=>{f(e,t),d()};let p=e((...e)=>{r(...e),d()},i,n);n.getInitialState=()=>p;let y=()=>{var e,t;if(!h)return;u=!1,l.forEach(e=>{var t;return e(null!=(t=i())?t:p)});let n=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=i())?e:p))||void 0;return s(h.getItem.bind(h))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,n]=e;if(r(a=o.merge(n,null!=(t=i())?t:p),!0),s)return d()}).then(()=>{null==n||n(a,void 0),a=i(),u=!0,c.forEach(e=>e(a))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{o={...o,...e},e.storage&&(h=e.storage)},clearStorage:()=>{null==h||h.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>y(),hasHydrated:()=>u,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||y(),a||p}},7108:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7712:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},7809:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}}]);