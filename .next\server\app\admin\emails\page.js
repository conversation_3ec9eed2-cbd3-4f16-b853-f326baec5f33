(()=>{var a={};a.id=46,a.ids=[46],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12395:(a,b,c)=>{Promise.resolve().then(c.bind(c,63093))},17971:(a,b,c)=>{Promise.resolve().then(c.bind(c,67886))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27900:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:a=>{"use strict";a.exports=require("path")},35071:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41550:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},47371:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["emails",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,67886)),"E:\\aidevcommerce\\src\\app\\admin\\emails\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,15515)),"E:\\aidevcommerce\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\aidevcommerce\\src\\app\\admin\\emails\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/emails/page",pathname:"/admin/emails",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/emails/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63093:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>M});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(88528),i=c(29523),j=c(44493),k=c(96834),l=c(98436),m=c(29867),n=c(4780);class o{constructor(){this.apiKey=process.env.EMAIL_API_KEY||"demo-key",this.baseUrl=process.env.EMAIL_API_URL||"https://api.emailservice.com"}async getEmailTemplates(){try{return[{id:"1",name:"Order Confirmation",subject:"Order Confirmation - #{orderNumber}",htmlContent:`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Thank you for your order!</h1>
              <p>Hi {{customerName}},</p>
              <p>We've received your order and are preparing it for shipment.</p>
              <div style="background: #f5f5f5; padding: 20px; margin: 20px 0;">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{orderNumber}}</p>
                <p><strong>Total:</strong> {{orderTotal}}</p>
                <p><strong>Estimated Delivery:</strong> {{deliveryDate}}</p>
              </div>
              <p>You can track your order <a href="{{trackingUrl}}">here</a>.</p>
              <p>Thank you for shopping with us!</p>
            </div>
          `,textContent:"Thank you for your order! Order #{{orderNumber}} - Total: {{orderTotal}}",variables:["customerName","orderNumber","orderTotal","deliveryDate","trackingUrl"],category:"transactional",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-15")},{id:"2",name:"Welcome Email",subject:"Welcome to AIDEVCOMMERCE!",htmlContent:`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #6366f1;">Welcome to AIDEVCOMMERCE!</h1>
              <p>Hi {{firstName}},</p>
              <p>Welcome to our community! We're excited to have you on board.</p>
              <div style="background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; margin: 20px 0; border-radius: 8px;">
                <h3>Get Started</h3>
                <p>Explore our products and discover amazing deals!</p>
                <a href="{{shopUrl}}" style="background: white; color: #6366f1; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;">Start Shopping</a>
              </div>
              <p>If you have any questions, feel free to contact our support team.</p>
            </div>
          `,textContent:"Welcome to AIDEVCOMMERCE! Start exploring our products at {{shopUrl}}",variables:["firstName","shopUrl"],category:"marketing",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-10")},{id:"3",name:"Password Reset",subject:"Reset Your Password",htmlContent:`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Reset Your Password</h1>
              <p>Hi {{firstName}},</p>
              <p>You requested to reset your password. Click the button below to create a new password:</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{resetUrl}}" style="background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
              </div>
              <p>This link will expire in 24 hours. If you didn't request this, please ignore this email.</p>
              <p>For security reasons, please don't share this link with anyone.</p>
            </div>
          `,textContent:"Reset your password: {{resetUrl}}",variables:["firstName","resetUrl"],category:"transactional",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-05")},{id:"4",name:"Newsletter Template",subject:"Weekly Newsletter - {{weekDate}}",htmlContent:`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <header style="background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; text-align: center;">
                <h1>AIDEVCOMMERCE Newsletter</h1>
                <p>{{weekDate}}</p>
              </header>
              <div style="padding: 20px;">
                <h2>This Week's Highlights</h2>
                <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-left: 4px solid #6366f1;">
                  <h3>{{highlightTitle}}</h3>
                  <p>{{highlightContent}}</p>
                </div>
                <h2>Featured Products</h2>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0;">
                  {{#each featuredProducts}}
                  <div style="border: 1px solid #e2e8f0; padding: 15px; border-radius: 8px;">
                    <h4>{{name}}</h4>
                    <p style="color: #6366f1; font-weight: bold;">{{price}}</p>
                  </div>
                  {{/each}}
                </div>
              </div>
            </div>
          `,textContent:"AIDEVCOMMERCE Newsletter - {{weekDate}}",variables:["weekDate","highlightTitle","highlightContent","featuredProducts"],category:"marketing",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-20")}]}catch(a){return console.error("Failed to fetch email templates:",a),[]}}async createEmailTemplate(a){try{let b={...a,id:`template-${Date.now()}`,createdAt:new Date,updatedAt:new Date};return console.log("Creating email template:",b),b}catch(a){throw console.error("Failed to create email template:",a),a}}async getEmailCampaigns(){try{return[{id:"1",name:"Welcome Series - Week 1",subject:"Welcome to AIDEVCOMMERCE!",templateId:"2",recipients:["<EMAIL>","<EMAIL>"],status:"sent",stats:{sent:1250,delivered:1235,opened:892,clicked:234,bounced:15,unsubscribed:8},sentAt:new Date("2024-01-15T10:00:00"),createdAt:new Date("2024-01-14"),updatedAt:new Date("2024-01-15")},{id:"2",name:"Product Launch - Smart Watch",subject:"Introducing Our New Smart Watch!",templateId:"4",recipients:[],segmentId:"electronics-interested",scheduledAt:new Date("2024-01-25T09:00:00"),status:"scheduled",stats:{sent:0,delivered:0,opened:0,clicked:0,bounced:0,unsubscribed:0},createdAt:new Date("2024-01-20"),updatedAt:new Date("2024-01-22")}]}catch(a){return console.error("Failed to fetch email campaigns:",a),[]}}async createEmailCampaign(a){try{let b={...a,id:`campaign-${Date.now()}`,stats:{sent:0,delivered:0,opened:0,clicked:0,bounced:0,unsubscribed:0},createdAt:new Date,updatedAt:new Date};return console.log("Creating email campaign:",b),b}catch(a){throw console.error("Failed to create email campaign:",a),a}}async sendTransactionalEmail(a,b,c,d){try{return console.log("Sending transactional email:",{templateId:a,recipient:b,variables:c,options:d}),{success:!0,messageId:`msg-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}}catch(a){return console.error("Failed to send transactional email:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error"}}}async sendMarketingEmail(a){try{return console.log("Sending marketing email campaign:",a),{success:!0}}catch(a){return console.error("Failed to send marketing email:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error"}}}async sendOrderConfirmation(a){try{return(await this.sendTransactionalEmail("1",a.customerEmail,{customerName:a.customerName,orderNumber:a.orderNumber,orderTotal:a.orderTotal,deliveryDate:a.deliveryDate,trackingUrl:`https://aidevcommerce.com/track/${a.orderNumber}`})).success}catch(a){return console.error("Failed to send order confirmation:",a),!1}}async sendWelcomeEmail(a){try{return(await this.sendTransactionalEmail("2",a.email,{firstName:a.firstName,shopUrl:"https://aidevcommerce.com/shop"})).success}catch(a){return console.error("Failed to send welcome email:",a),!1}}async sendPasswordResetEmail(a){try{return(await this.sendTransactionalEmail("3",a.email,{firstName:a.firstName,resetUrl:`https://aidevcommerce.com/auth/reset-password?token=${a.resetToken}`})).success}catch(a){return console.error("Failed to send password reset email:",a),!1}}async getEmailAnalytics(a="30d"){try{return{totalSent:15420,totalDelivered:15180,totalOpened:8945,totalClicked:2134,deliveryRate:98.4,openRate:58.9,clickRate:13.8,bounceRate:1.6,unsubscribeRate:.3}}catch(a){return console.error("Failed to fetch email analytics:",a),{totalSent:0,totalDelivered:0,totalOpened:0,totalClicked:0,deliveryRate:0,openRate:0,clickRate:0,bounceRate:0,unsubscribeRate:0}}}validateEmail(a){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)}replaceTemplateVariables(a,b){let c=a;return Object.entries(b).forEach(([a,b])=>{let d=RegExp(`{{${a}}}`,"g");c=c.replace(d,String(b))}),c}}let p=new o;var q=c(5336),r=c(93613),s=c(35071),t=c(48730),u=c(97840);let v=(0,c(62688).A)("pause",[["rect",{x:"14",y:"3",width:"5",height:"18",rx:"1",key:"kaeet6"}],["rect",{x:"5",y:"3",width:"5",height:"18",rx:"1",key:"1wsw3u"}]]);var w=c(13861),x=c(63143),y=c(70615),z=c(40228),A=c(27900),B=c(41550),C=c(25541),D=c(31158),E=c(96474),F=c(53411),G=c(99270);let H=["All Categories","transactional","marketing","notification"],I=["All Status","active","draft","archived"],J=["All Status","draft","scheduled","sending","sent","paused"],K=(a,b)=>({template:{active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",draft:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",archived:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},campaign:{draft:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",sending:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",sent:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",paused:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}})[b][a]||"bg-gray-100 text-gray-800",L=(a,b)=>({template:{active:q.A,draft:r.A,archived:s.A},campaign:{draft:r.A,scheduled:t.A,sending:u.A,sent:q.A,paused:v}})[b][a]||r.A;function M(){let[a,b]=(0,e.useState)("templates"),[c,f]=(0,e.useState)([]),[o,r]=(0,e.useState)([]),[s,t]=(0,e.useState)(null),[u,v]=(0,e.useState)(!0),[M,N]=(0,e.useState)(""),[O,P]=(0,e.useState)("All Categories"),[Q,R]=(0,e.useState)("All Status"),{t:S}=(0,l.o)(),{toast:T}=(0,m.dj)(),U=async()=>{try{v(!0);let[a,b,c]=await Promise.all([p.getEmailTemplates(),p.getEmailCampaigns(),p.getEmailAnalytics()]);f(a),r(b),t(c)}catch(a){console.error("Failed to load email data:",a),T({title:"Error",description:"Failed to load email data",variant:"destructive"})}finally{v(!1)}},V=async a=>{try{let b=await p.sendMarketingEmail(a);if(b.success)T({title:"Campaign Sent",description:"Email campaign has been sent successfully"}),U();else throw Error(b.error)}catch(a){T({title:"Error",description:"Failed to send campaign",variant:"destructive"})}},W=c.filter(a=>{let b=a.name.toLowerCase().includes(M.toLowerCase())||a.subject.toLowerCase().includes(M.toLowerCase()),c="All Categories"===O||a.category===O,d="All Status"===Q||a.status===Q;return b&&c&&d}),X=o.filter(a=>{let b=a.name.toLowerCase().includes(M.toLowerCase())||a.subject.toLowerCase().includes(M.toLowerCase()),c="All Status"===Q||a.status===Q;return b&&c}),Y=({template:a})=>{let b=L(a.status,"template");return(0,d.jsx)(j.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsxs)(j.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg mb-2",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:a.subject}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsxs)(k.E,{className:K(a.status,"template"),children:[(0,d.jsx)(b,{className:"h-3 w-3 mr-1"}),a.status]}),(0,d.jsx)(k.E,{variant:"outline",className:"capitalize",children:a.category})]})]})}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,d.jsxs)("span",{children:["Variables: ",a.variables.length]}),(0,d.jsxs)("span",{children:["Updated ",(0,n.Yq)(a.updatedAt)]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g(),{href:`/admin/emails/templates/${a.id}`,children:(0,d.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Preview"]})}),(0,d.jsx)(g(),{href:`/admin/emails/templates/${a.id}/edit`,children:(0,d.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Edit"]})}),(0,d.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(y.A,{className:"h-4 w-4 mr-1"}),"Duplicate"]})]})]})})},Z=({campaign:a})=>{let b=L(a.status,"campaign"),c=a.stats.sent>0?(a.stats.opened/a.stats.sent*100).toFixed(1):0,e=a.stats.sent>0?(a.stats.clicked/a.stats.sent*100).toFixed(1):0;return(0,d.jsx)(j.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsxs)(j.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg mb-2",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:a.subject}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsxs)(k.E,{className:K(a.status,"campaign"),children:[(0,d.jsx)(b,{className:"h-3 w-3 mr-1"}),a.status]}),a.scheduledAt&&(0,d.jsxs)(k.E,{variant:"outline",children:[(0,d.jsx)(z.A,{className:"h-3 w-3 mr-1"}),(0,n.Yq)(a.scheduledAt)]})]})]})}),a.stats.sent>0&&(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-lg font-semibold",children:a.stats.sent.toLocaleString()}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Sent"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("p",{className:"text-lg font-semibold",children:[c,"%"]}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Opened"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("p",{className:"text-lg font-semibold",children:[e,"%"]}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Clicked"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-lg font-semibold",children:a.stats.unsubscribed}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Unsubscribed"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,d.jsxs)("span",{children:["Recipients: ",a.recipients.length||"Segment"]}),(0,d.jsxs)("span",{children:["Created ",(0,n.Yq)(a.createdAt)]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:["draft"===a.status&&(0,d.jsxs)(i.$,{size:"sm",onClick:()=>V(a.id),className:"bg-green-600 hover:bg-green-700",children:[(0,d.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"Send Now"]}),(0,d.jsx)(g(),{href:`/admin/emails/campaigns/${a.id}`,children:(0,d.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"View"]})}),(0,d.jsx)(g(),{href:`/admin/emails/campaigns/${a.id}/edit`,children:(0,d.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Edit"]})})]})]})})};return u?(0,d.jsx)(h.U,{children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-64"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[...Array(6)].map((a,b)=>(0,d.jsx)("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg h-64 animate-pulse"},b))})]})}):(0,d.jsx)(h.U,{children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Email Management"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage email templates, campaigns, and analytics"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)(i.$,{variant:"outline",children:[(0,d.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,d.jsx)(g(),{href:"/admin/emails/templates/new",children:(0,d.jsxs)(i.$,{children:[(0,d.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"New Template"]})})]})]}),(0,d.jsx)("div",{className:"border-b",children:(0,d.jsx)("nav",{className:"flex space-x-8",children:[{id:"templates",label:"Templates",icon:B.A},{id:"campaigns",label:"Campaigns",icon:A.A},{id:"analytics",label:"Analytics",icon:F.A}].map(c=>{let e=c.icon;return(0,d.jsxs)("button",{onClick:()=>b(c.id),className:`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${a===c.id?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[(0,d.jsx)(e,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:c.label})]},c.id)})})}),"analytics"===a?(0,d.jsx)(()=>s?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Sent"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:s.totalSent.toLocaleString()})]}),(0,d.jsx)(B.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Open Rate"}),(0,d.jsxs)("p",{className:"text-2xl font-bold",children:[s.openRate,"%"]})]}),(0,d.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Click Rate"}),(0,d.jsxs)("p",{className:"text-2xl font-bold",children:[s.clickRate,"%"]})]}),(0,d.jsx)(C.A,{className:"h-8 w-8 text-purple-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Delivery Rate"}),(0,d.jsxs)("p",{className:"text-2xl font-bold",children:[s.deliveryRate,"%"]})]}),(0,d.jsx)(q.A,{className:"h-8 w-8 text-orange-500"})]})})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsx)(j.ZB,{children:"Email Performance"}),(0,d.jsx)(j.BT,{children:"Detailed engagement metrics"})]}),(0,d.jsx)(j.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Delivered"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${s.deliveryRate}%`}})}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[s.deliveryRate,"%"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Opened"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${s.openRate}%`}})}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[s.openRate,"%"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Clicked"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:`${s.clickRate}%`}})}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[s.clickRate,"%"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Bounced"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-red-500 h-2 rounded-full",style:{width:`${s.bounceRate}%`}})}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[s.bounceRate,"%"]})]})]})]})})]}),(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsx)(j.ZB,{children:"Recent Campaigns"}),(0,d.jsx)(j.BT,{children:"Latest campaign performance"})]}),(0,d.jsx)(j.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:o.slice(0,3).map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:a.stats.sent>0?`${a.stats.sent} sent`:"Not sent yet"})]}),(0,d.jsx)(k.E,{className:K(a.status,"campaign"),children:a.status})]},a.id))})})]})]})]}):null,{}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(G.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)("input",{type:"text",placeholder:`Search ${a}...`,value:M,onChange:a=>N(a.target.value),className:"pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:["templates"===a&&(0,d.jsx)("select",{value:O,onChange:a=>P(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:H.map(a=>(0,d.jsx)("option",{value:a,children:a},a))}),(0,d.jsx)("select",{value:Q,onChange:a=>R(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:("templates"===a?I:J).map(a=>(0,d.jsx)("option",{value:a,children:a},a))})]})]})})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:"templates"===a?W.map(a=>(0,d.jsx)(Y,{template:a},a.id)):X.map(a=>(0,d.jsx)(Z,{campaign:a},a.id))}),("templates"===a&&0===W.length||"campaigns"===a&&0===X.length)&&(0,d.jsx)(j.Zp,{children:(0,d.jsxs)(j.Wu,{className:"text-center py-12",children:[(0,d.jsx)(B.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:["No ",a," found"]}),(0,d.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["Get started by creating your first ",a.slice(0,-1)]}),(0,d.jsx)(g(),{href:`/admin/emails/${a}/new`,children:(0,d.jsxs)(i.$,{children:[(0,d.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Create ",a.slice(0,-1)]})})]})})]})]})})}},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},67886:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\aidevcommerce\\\\src\\\\app\\\\admin\\\\emails\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\app\\admin\\emails\\page.tsx","default")},70615:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93613:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97840:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,54,776,300],()=>b(b.s=47371));module.exports=c})();