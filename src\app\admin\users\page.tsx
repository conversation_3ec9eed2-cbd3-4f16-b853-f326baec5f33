"use client"

import { useState } from "react"
import Link from "next/link"
import { AdminLayout } from "@/components/layout/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Users,
  UserCheck,
  UserX,
  Shield,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Activity,
  Download,
  Upload,
  Ban,
  CheckCircle,
  AlertCircle,
  Crown,
  User,
} from "lucide-react"

// Mock users data
const mockUsers = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    role: "customer",
    status: "active",
    avatar: "/user-1.jpg",
    address: {
      street: "123 Main St",
      city: "New York",
      state: "NY",
      zip: "10001",
      country: "USA"
    },
    stats: {
      totalOrders: 12,
      totalSpent: 2450.99,
      avgOrderValue: 204.25,
      lastOrderDate: new Date("2024-01-20")
    },
    createdAt: new Date("2023-06-15"),
    lastLoginAt: new Date("2024-01-21T10:30:00"),
    emailVerified: true,
    phoneVerified: true,
    notes: "VIP customer - provides excellent feedback"
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    phone: "+****************",
    role: "customer",
    status: "active",
    avatar: "/user-2.jpg",
    address: {
      street: "456 Oak Ave",
      city: "Los Angeles",
      state: "CA",
      zip: "90210",
      country: "USA"
    },
    stats: {
      totalOrders: 8,
      totalSpent: 1299.99,
      avgOrderValue: 162.50,
      lastOrderDate: new Date("2024-01-19")
    },
    createdAt: new Date("2023-08-22"),
    lastLoginAt: new Date("2024-01-20T15:45:00"),
    emailVerified: true,
    phoneVerified: false,
    notes: ""
  },
  {
    id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    role: "customer",
    status: "inactive",
    avatar: "/user-3.jpg",
    address: {
      street: "789 Pine St",
      city: "Chicago",
      state: "IL",
      zip: "60601",
      country: "USA"
    },
    stats: {
      totalOrders: 3,
      totalSpent: 450.99,
      avgOrderValue: 150.33,
      lastOrderDate: new Date("2023-12-15")
    },
    createdAt: new Date("2023-04-10"),
    lastLoginAt: new Date("2023-12-20T09:15:00"),
    emailVerified: true,
    phoneVerified: true,
    notes: "Has not placed orders recently"
  },
  {
    id: "4",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    phone: "+****************",
    role: "admin",
    status: "active",
    avatar: "/user-4.jpg",
    address: {
      street: "321 Elm St",
      city: "Miami",
      state: "FL",
      zip: "33101",
      country: "USA"
    },
    stats: {
      totalOrders: 0,
      totalSpent: 0,
      avgOrderValue: 0,
      lastOrderDate: null
    },
    createdAt: new Date("2023-01-15"),
    lastLoginAt: new Date("2024-01-21T14:20:00"),
    emailVerified: true,
    phoneVerified: true,
    notes: "Admin user - full system access"
  },
  {
    id: "5",
    name: "David Brown",
    email: "<EMAIL>",
    phone: "+****************",
    role: "customer",
    status: "suspended",
    avatar: "/user-5.jpg",
    address: {
      street: "654 Maple Ave",
      city: "Seattle",
      state: "WA",
      zip: "98101",
      country: "USA"
    },
    stats: {
      totalOrders: 15,
      totalSpent: 3200.50,
      avgOrderValue: 213.37,
      lastOrderDate: new Date("2024-01-10")
    },
    createdAt: new Date("2023-03-08"),
    lastLoginAt: new Date("2024-01-15T11:45:00"),
    emailVerified: true,
    phoneVerified: true,
    notes: "Suspended due to payment disputes"
  },
  {
    id: "6",
    name: "Emily Davis",
    email: "<EMAIL>",
    phone: "+****************",
    role: "moderator",
    status: "active",
    avatar: "/user-6.jpg",
    address: {
      street: "987 Cedar St",
      city: "Boston",
      state: "MA",
      zip: "02101",
      country: "USA"
    },
    stats: {
      totalOrders: 2,
      totalSpent: 299.99,
      avgOrderValue: 150.00,
      lastOrderDate: new Date("2024-01-05")
    },
    createdAt: new Date("2023-09-12"),
    lastLoginAt: new Date("2024-01-21T16:10:00"),
    emailVerified: true,
    phoneVerified: true,
    notes: "Content moderator - manages blog and reviews"
  }
]

const userRoles = ["All Roles", "customer", "admin", "moderator"]
const userStatuses = ["All Status", "active", "inactive", "suspended"]

const getRoleColor = (role: string) => {
  switch (role) {
    case "admin":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    case "moderator":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    case "customer":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    case "inactive":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    case "suspended":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case "admin":
      return Crown
    case "moderator":
      return Shield
    case "customer":
      return User
    default:
      return User
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return CheckCircle
    case "inactive":
      return AlertCircle
    case "suspended":
      return Ban
    default:
      return AlertCircle
  }
}

export default function UsersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedRole, setSelectedRole] = useState("All Roles")
  const [selectedStatus, setSelectedStatus] = useState("All Status")
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  
  const { t } = useLanguage()
  const { toast } = useToast()

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.phone.includes(searchQuery)
    const matchesRole = selectedRole === "All Roles" || user.role === selectedRole
    const matchesStatus = selectedStatus === "All Status" || user.status === selectedStatus
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSelectAll = () => {
    setSelectedUsers(
      selectedUsers.length === filteredUsers.length 
        ? [] 
        : filteredUsers.map(u => u.id)
    )
  }

  const handleBulkAction = (action: string) => {
    toast({
      title: "Bulk Action",
      description: `${action} applied to ${selectedUsers.length} users`,
    })
    setSelectedUsers([])
  }

  const handleUpdateUserStatus = (userId: string, newStatus: string) => {
    toast({
      title: "User Updated",
      description: `User status updated to ${newStatus}`,
    })
  }

  const UserCard = ({ user }: { user: typeof mockUsers[0] }) => {
    const RoleIcon = getRoleIcon(user.role)
    const StatusIcon = getStatusIcon(user.status)

    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              <input
                type="checkbox"
                checked={selectedUsers.includes(user.id)}
                onChange={() => handleSelectUser(user.id)}
                className="mt-1"
              />
              
              <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-gray-500" />
              </div>
              
              <div className="flex-1 min-w-0">
                {/* User Header */}
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="font-semibold text-lg flex items-center space-x-2">
                      <span>{user.name}</span>
                      {user.emailVerified && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {user.email}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      Joined {formatDate(user.createdAt)}
                    </p>
                    <p className="text-xs text-gray-400">
                      Last login: {formatDate(user.lastLoginAt)}
                    </p>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="flex items-center space-x-4 mb-3 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Phone className="h-4 w-4" />
                    <span>{user.phone}</span>
                    {user.phoneVerified && (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>{user.address.city}, {user.address.state}</span>
                  </div>
                </div>

                {/* Role and Status */}
                <div className="flex items-center space-x-2 mb-4">
                  <Badge className={`flex items-center space-x-1 ${getRoleColor(user.role)}`}>
                    <RoleIcon className="h-3 w-3" />
                    <span className="capitalize">{user.role}</span>
                  </Badge>
                  <Badge className={`flex items-center space-x-1 ${getStatusColor(user.status)}`}>
                    <StatusIcon className="h-3 w-3" />
                    <span className="capitalize">{user.status}</span>
                  </Badge>
                </div>

                {/* Customer Stats */}
                {user.role === 'customer' && (
                  <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <p className="text-lg font-semibold">{user.stats.totalOrders}</p>
                      <p className="text-xs text-gray-500">Orders</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold">${user.stats.totalSpent.toFixed(2)}</p>
                      <p className="text-xs text-gray-500">Total Spent</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold">${user.stats.avgOrderValue.toFixed(2)}</p>
                      <p className="text-xs text-gray-500">Avg Order</p>
                    </div>
                  </div>
                )}

                {/* Notes */}
                {user.notes && (
                  <div className="mb-4">
                    <p className="text-sm font-medium mb-1">Notes:</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                      {user.notes}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Mail className="h-4 w-4 mr-1" />
                      Email
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Phone className="h-4 w-4 mr-1" />
                      Call
                    </Button>
                    {user.role === 'customer' && (
                      <Link href={`/admin/users/${user.id}/orders`}>
                        <Button variant="ghost" size="sm">
                          <Activity className="h-4 w-4 mr-1" />
                          Orders
                        </Button>
                      </Link>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Link href={`/admin/users/${user.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </Link>
                    <Link href={`/admin/users/${user.id}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </Link>
                    <select
                      value={user.status}
                      onChange={(e) => handleUpdateUserStatus(user.id, e.target.value)}
                      className="px-2 py-1 text-sm border rounded"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="suspended">Suspended</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Users
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage user accounts, roles, and permissions
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Link href="/admin/users/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Users
                  </p>
                  <p className="text-2xl font-bold">
                    {mockUsers.length}
                  </p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active Users
                  </p>
                  <p className="text-2xl font-bold">
                    {mockUsers.filter(u => u.status === 'active').length}
                  </p>
                </div>
                <UserCheck className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Customers
                  </p>
                  <p className="text-2xl font-bold">
                    {mockUsers.filter(u => u.role === 'customer').length}
                  </p>
                </div>
                <User className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Admins
                  </p>
                  <p className="text-2xl font-bold">
                    {mockUsers.filter(u => u.role === 'admin' || u.role === 'moderator').length}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-4">
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {userRoles.map((role) => (
                    <option key={role} value={role}>
                      {role}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {userStatuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedUsers.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">
                    {selectedUsers.length} users selected
                  </span>
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {selectedUsers.length === filteredUsers.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Activate')}>
                    Activate
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Suspend')}>
                    Suspend
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Export Selected')}>
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Users List */}
        <div className="space-y-4">
          {filteredUsers.map((user) => (
            <UserCard key={user.id} user={user} />
          ))}
        </div>

        {/* Empty State */}
        {filteredUsers.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No users found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search or filter criteria
              </p>
              <Button onClick={() => {
                setSearchQuery("")
                setSelectedRole("All Roles")
                setSelectedStatus("All Status")
              }}>
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {filteredUsers.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredUsers.length} of {mockUsers.length} users
            </p>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" className="bg-primary-50 dark:bg-primary-900">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
