(()=>{var a={};a.id=575,a.ids=[575],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6943:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10486:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>F});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(88528),i=c(29523),j=c(44493),k=c(96834),l=c(98436),m=c(29867),n=c(4780),o=c(62688);let p=(0,o.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var q=c(5336),r=c(19080),s=c(25541),t=c(13861),u=c(63143),v=c(88233),w=c(31158);let x=(0,o.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var y=c(96474),z=c(99270),A=c(6943),B=c(25366);let C=[{id:"1",name:"Premium Wireless Headphones",sku:"PWH-001",category:"Electronics",price:199.99,comparePrice:249.99,stock:45,lowStockThreshold:10,status:"active",featured:!0,sales:234,revenue:46800,image:"/product-1.jpg",createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-15")},{id:"2",name:"Smart Fitness Watch",sku:"SFW-002",category:"Electronics",price:299.99,comparePrice:null,stock:8,lowStockThreshold:10,status:"active",featured:!1,sales:189,revenue:56700,image:"/product-2.jpg",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-12")},{id:"3",name:"Ergonomic Office Chair",sku:"EOC-003",category:"Furniture",price:449.99,comparePrice:599.99,stock:0,lowStockThreshold:5,status:"out_of_stock",featured:!0,sales:156,revenue:70200,image:"/product-3.jpg",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-10")},{id:"4",name:"Wireless Charging Pad",sku:"WCP-004",category:"Electronics",price:49.99,comparePrice:null,stock:120,lowStockThreshold:20,status:"active",featured:!1,sales:298,revenue:14900,image:"/product-4.jpg",createdAt:new Date("2024-01-03"),updatedAt:new Date("2024-01-08")},{id:"5",name:"Professional Camera Lens",sku:"PCL-005",category:"Photography",price:899.99,comparePrice:null,stock:15,lowStockThreshold:5,status:"draft",featured:!1,sales:67,revenue:60300,image:"/product-5.jpg",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-05")}],D=["All Categories","Electronics","Furniture","Photography","Clothing"],E=["All Status","active","draft","out_of_stock"];function F(){let[a,b]=(0,e.useState)(""),[c,f]=(0,e.useState)("All Categories"),[o,F]=(0,e.useState)("All Status"),[G,H]=(0,e.useState)("list"),[I,J]=(0,e.useState)([]),{t:K}=(0,l.o)(),{toast:L}=(0,m.dj)(),M=C.filter(b=>{let d=b.name.toLowerCase().includes(a.toLowerCase())||b.sku.toLowerCase().includes(a.toLowerCase()),e="All Categories"===c||b.category===c,f="All Status"===o||b.status===o;return d&&e&&f}),N=a=>{L({title:"Bulk Action",description:`${a} applied to ${I.length} products`}),J([])},O=({product:a})=>{var b,c;let e=(b=a.stock,c=a.lowStockThreshold,0===b?{status:"out",color:"text-red-600",icon:p}:b<=c?{status:"low",color:"text-yellow-600",icon:p}:{status:"good",color:"text-green-600",icon:q.A}),f=e.icon;return(0,d.jsx)(j.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("input",{type:"checkbox",checked:I.includes(a.id),onChange:()=>{var b;return b=a.id,void J(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])},className:"mt-1"}),(0,d.jsx)("div",{className:"w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center",children:(0,d.jsx)(r.A,{className:"h-8 w-8 text-gray-400"})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg line-clamp-1",children:a.name}),a.featured&&(0,d.jsx)(k.E,{variant:"secondary",className:"text-xs",children:"Featured"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-2",children:[(0,d.jsxs)("span",{children:["SKU: ",a.sku]}),(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{children:a.category})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"font-semibold text-lg",children:(0,n.$g)(a.price)}),a.comparePrice&&(0,d.jsx)("span",{className:"text-sm text-gray-500 line-through",children:(0,n.$g)(a.comparePrice)})]}),(0,d.jsx)(k.E,{className:(a=>{switch(a){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"draft":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"out_of_stock":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(a.status),children:a.status.replace("_"," ")})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,d.jsxs)("div",{className:`flex items-center space-x-1 ${e.color}`,children:[(0,d.jsx)(f,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[a.stock," in stock"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 text-gray-500",children:[(0,d.jsx)(s.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[a.sales," sold"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g(),{href:`/admin/products/${a.id}`,children:(0,d.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})}),(0,d.jsx)(g(),{href:`/admin/products/${a.id}/edit`,children:(0,d.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})}),(0,d.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>(a.id,void L({title:"Product Deleted",description:"Product has been successfully deleted"})),className:"text-red-600 hover:text-red-700",children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})]})]})]})]})})})})};return(0,d.jsx)(h.U,{children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Products"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage your product catalog and inventory"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)(i.$,{variant:"outline",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,d.jsxs)(i.$,{variant:"outline",children:[(0,d.jsx)(x,{className:"h-4 w-4 mr-2"}),"Import"]}),(0,d.jsx)(g(),{href:"/admin/products/new",children:(0,d.jsxs)(i.$,{children:[(0,d.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Products"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:C.length})]}),(0,d.jsx)(r.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Active Products"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:C.filter(a=>"active"===a.status).length})]}),(0,d.jsx)(q.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Low Stock"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:C.filter(a=>a.stock<=a.lowStockThreshold&&a.stock>0).length})]}),(0,d.jsx)(p,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Out of Stock"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:C.filter(a=>0===a.stock).length})]}),(0,d.jsx)(p,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)("input",{type:"text",placeholder:"Search products...",value:a,onChange:a=>b(a.target.value),className:"pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("select",{value:c,onChange:a=>f(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:D.map(a=>(0,d.jsx)("option",{value:a,children:a},a))}),(0,d.jsx)("select",{value:o,onChange:a=>F(a.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:E.map(a=>(0,d.jsx)("option",{value:a,children:a},a))}),(0,d.jsxs)("div",{className:"flex border rounded-lg",children:[(0,d.jsx)(i.$,{variant:"grid"===G?"default":"ghost",size:"sm",onClick:()=>H("grid"),className:"rounded-r-none",children:(0,d.jsx)(A.A,{className:"h-4 w-4"})}),(0,d.jsx)(i.$,{variant:"list"===G?"default":"ghost",size:"sm",onClick:()=>H("list"),className:"rounded-l-none",children:(0,d.jsx)(B.A,{className:"h-4 w-4"})})]})]})]})})}),I.length>0&&(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("span",{className:"text-sm font-medium",children:[I.length," products selected"]}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{J(I.length===M.length?[]:M.map(a=>a.id))},children:I.length===M.length?"Deselect All":"Select All"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>N("Activate"),children:"Activate"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>N("Deactivate"),children:"Deactivate"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>N("Delete"),className:"text-red-600",children:"Delete"})]})]})})}),(0,d.jsx)("div",{className:"space-y-4",children:M.map(a=>(0,d.jsx)(O,{product:a},a.id))}),0===M.length&&(0,d.jsx)(j.Zp,{children:(0,d.jsxs)(j.Wu,{className:"text-center py-12",children:[(0,d.jsx)(r.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"No products found"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Try adjusting your search or filter criteria"}),(0,d.jsx)(i.$,{onClick:()=>{b(""),f("All Categories"),F("All Status")},children:"Clear Filters"})]})}),M.length>0&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Showing ",M.length," of ",C.length," products"]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",disabled:!0,children:"Previous"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",className:"bg-primary-50 dark:bg-primary-900",children:"1"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",children:"2"}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",children:"Next"})]})]})]})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24856:(a,b,c)=>{Promise.resolve().then(c.bind(c,38571))},25366:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},25528:(a,b,c)=>{Promise.resolve().then(c.bind(c,10486))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:a=>{"use strict";a.exports=require("path")},38571:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\aidevcommerce\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\app\\admin\\products\\page.tsx","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89937:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,38571)),"E:\\aidevcommerce\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,15515)),"E:\\aidevcommerce\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\aidevcommerce\\src\\app\\admin\\products\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/products/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,54,776,464],()=>b(b.s=89937));module.exports=c})();