import { NextResponse } from 'next/server'

// Mock data - in a real app, this would come from your database
const mockProducts = [
  { id: '1', slug: 'premium-wireless-headphones', updatedAt: '2024-01-20' },
  { id: '2', slug: 'smart-fitness-watch', updatedAt: '2024-01-18' },
  { id: '3', slug: 'ergonomic-office-chair', updatedAt: '2024-01-15' },
  { id: '4', slug: 'wireless-charging-pad', updatedAt: '2024-01-12' },
  { id: '5', slug: 'professional-camera-lens', updatedAt: '2024-01-10' },
]

const mockServices = [
  { id: '1', slug: 'web-development', updatedAt: '2024-01-20' },
  { id: '2', slug: 'mobile-app-development', updatedAt: '2024-01-18' },
  { id: '3', slug: 'ui-ux-design', updatedAt: '2024-01-15' },
  { id: '4', slug: 'digital-marketing', updatedAt: '2024-01-12' },
  { id: '5', slug: 'cloud-solutions', updatedAt: '2024-01-10' },
]

const mockBlogPosts = [
  { id: '1', slug: 'future-ai-ecommerce-transforming-online-shopping', updatedAt: '2024-01-16' },
  { id: '2', slug: 'sustainable-manufacturing-green-production-lines', updatedAt: '2024-01-13' },
  { id: '3', slug: 'digital-transformation-small-businesses-guide', updatedAt: '2024-01-20' },
  { id: '4', slug: 'ux-design-trends-2024-user-experience', updatedAt: '2024-01-18' },
  { id: '5', slug: 'cloud-infrastructure-best-practices-scalable-applications', updatedAt: '2024-01-10' },
]

const mockProductionLines = [
  { id: '1', slug: 'automated-assembly-line', updatedAt: '2024-01-15' },
  { id: '2', slug: 'quality-control-system', updatedAt: '2024-01-12' },
  { id: '3', slug: 'packaging-automation', updatedAt: '2024-01-10' },
  { id: '4', slug: 'robotic-welding-station', updatedAt: '2024-01-08' },
  { id: '5', slug: 'conveyor-belt-system', updatedAt: '2024-01-05' },
]

interface SitemapUrl {
  loc: string
  lastmod?: string
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: number
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aidevcommerce.com'
  
  const urlElements = urls.map(url => `
  <url>
    <loc>${baseUrl}${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${urlElements}
</urlset>`
}

export async function GET() {
  try {
    const urls: SitemapUrl[] = []
    
    // Static pages
    const staticPages = [
      { loc: '/', changefreq: 'daily' as const, priority: 1.0 },
      { loc: '/shop', changefreq: 'daily' as const, priority: 0.9 },
      { loc: '/services', changefreq: 'weekly' as const, priority: 0.8 },
      { loc: '/production-lines', changefreq: 'weekly' as const, priority: 0.8 },
      { loc: '/blog', changefreq: 'daily' as const, priority: 0.7 },
      { loc: '/about', changefreq: 'monthly' as const, priority: 0.6 },
      { loc: '/contact', changefreq: 'monthly' as const, priority: 0.6 },
      { loc: '/auth/login', changefreq: 'monthly' as const, priority: 0.3 },
      { loc: '/auth/register', changefreq: 'monthly' as const, priority: 0.3 },
    ]
    
    staticPages.forEach(page => {
      urls.push({
        loc: page.loc,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: page.changefreq,
        priority: page.priority
      })
    })
    
    // Product pages
    mockProducts.forEach(product => {
      urls.push({
        loc: `/products/${product.slug}`,
        lastmod: product.updatedAt,
        changefreq: 'weekly',
        priority: 0.8
      })
    })
    
    // Service pages
    mockServices.forEach(service => {
      urls.push({
        loc: `/services/${service.slug}`,
        lastmod: service.updatedAt,
        changefreq: 'monthly',
        priority: 0.7
      })
    })
    
    // Blog posts
    mockBlogPosts.forEach(post => {
      urls.push({
        loc: `/blog/${post.slug}`,
        lastmod: post.updatedAt,
        changefreq: 'monthly',
        priority: 0.6
      })
    })
    
    // Production line pages
    mockProductionLines.forEach(line => {
      urls.push({
        loc: `/production-lines/${line.slug}`,
        lastmod: line.updatedAt,
        changefreq: 'monthly',
        priority: 0.7
      })
    })
    
    // Category pages
    const categories = [
      'electronics',
      'furniture',
      'accessories',
      'photography',
      'gaming'
    ]
    
    categories.forEach(category => {
      urls.push({
        loc: `/shop/category/${category}`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'weekly',
        priority: 0.6
      })
    })
    
    // Service categories
    const serviceCategories = [
      'development',
      'design',
      'marketing',
      'infrastructure',
      'analytics'
    ]
    
    serviceCategories.forEach(category => {
      urls.push({
        loc: `/services/category/${category}`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.5
      })
    })
    
    // Blog categories
    const blogCategories = [
      'technology',
      'manufacturing',
      'business',
      'design',
      'marketing'
    ]
    
    blogCategories.forEach(category => {
      urls.push({
        loc: `/blog/category/${category}`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'weekly',
        priority: 0.5
      })
    })
    
    const sitemapXML = generateSitemapXML(urls)
    
    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    })
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

// Generate sitemap index for large sites
export async function generateSitemapIndex(): Promise<string> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aidevcommerce.com'
  const lastmod = new Date().toISOString().split('T')[0]
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/api/sitemap</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${baseUrl}/api/sitemap/products</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${baseUrl}/api/sitemap/services</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${baseUrl}/api/sitemap/blog</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
</sitemapindex>`
}
