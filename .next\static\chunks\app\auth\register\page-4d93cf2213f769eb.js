(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{416:(e,s,a)=>{Promise.resolve().then(a.bind(a,1630))},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1630:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>A});var t=a(5155),r=a(2115),l=a(6874),o=a.n(l),d=a(5695),c=a(2177),i=a(221),n=a(8309),m=a(285),u=a(2523),p=a(5057),h=a(6695),x=a(7481),f=a(4817),j=a(5169),N=a(1007),y=a(8883);let w=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var v=a(2919),g=a(8749),b=a(2657);let k=n.Ik({name:n.Yj().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters"),email:n.Yj().email("Invalid email address"),phone:n.Yj().optional(),password:n.Yj().min(8,"Password must be at least 8 characters").max(128,"Password must be less than 128 characters"),confirmPassword:n.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function A(){let[e,s]=(0,r.useState)(!1),[a,l]=(0,r.useState)(!1),[n,A]=(0,r.useState)(!1),C=(0,d.useRouter)(),{toast:P}=(0,x.dj)(),{t:R}=(0,f.o)(),{register:F,handleSubmit:E,formState:{errors:J}}=(0,c.mN)({resolver:(0,i.u)(k)}),M=async e=>{A(!0);try{let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,email:e.email,phone:e.phone,password:e.password})}),a=await s.json();a.success?(P({title:R("common.success"),description:"Account created successfully! Please login."}),C.push("/auth/login")):P({title:R("common.error"),description:a.error||"Failed to create account",variant:"destructive"})}catch(e){P({title:R("common.error"),description:"An error occurred during registration",variant:"destructive"})}finally{A(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)(o(),{href:"/",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),(0,t.jsxs)(h.Zp,{className:"w-full",children:[(0,t.jsxs)(h.aR,{className:"space-y-1",children:[(0,t.jsx)(h.ZB,{className:"text-2xl text-center",children:R("auth.register")}),(0,t.jsx)(h.BT,{className:"text-center",children:"Create your account to get started"})]}),(0,t.jsx)(h.Wu,{className:"space-y-4",children:(0,t.jsxs)("form",{onSubmit:E(M),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"name",children:"Full Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(N.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(u.p,{id:"name",type:"text",placeholder:"Enter your full name",className:"pl-10",...F("name")})]}),J.name&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:J.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"email",children:"Email"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(u.p,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...F("email")})]}),J.email&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:J.email.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"phone",children:"Phone (Optional)"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(u.p,{id:"phone",type:"tel",placeholder:"Enter your phone number",className:"pl-10",...F("phone")})]}),J.phone&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:J.phone.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(u.p,{id:"password",type:e?"text":"password",placeholder:"Create a password",className:"pl-10 pr-10",...F("password")}),(0,t.jsx)("button",{type:"button",onClick:()=>s(!e),className:"absolute right-3 top-3 text-muted-foreground hover:text-foreground",children:e?(0,t.jsx)(g.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})})]}),J.password&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:J.password.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(u.p,{id:"confirmPassword",type:a?"text":"password",placeholder:"Confirm your password",className:"pl-10 pr-10",...F("confirmPassword")}),(0,t.jsx)("button",{type:"button",onClick:()=>l(!a),className:"absolute right-3 top-3 text-muted-foreground hover:text-foreground",children:a?(0,t.jsx)(g.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})})]}),J.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:J.confirmPassword.message})]}),(0,t.jsx)(m.$,{type:"submit",className:"w-full",disabled:n,children:n?"Creating account...":R("auth.register")})]})}),(0,t.jsx)(h.wL,{children:(0,t.jsxs)("p",{className:"text-center text-sm text-muted-foreground w-full",children:["Already have an account?"," ",(0,t.jsx)(o(),{href:"/auth/login",className:"text-primary hover:underline",children:R("auth.login")})]})})]})]})})}},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>o});var t=a(5155),r=a(2115),l=a(9434);let o=r.forwardRef((e,s)=>{let{className:a,type:r,...o}=e;return(0,t.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...o})});o.displayName="Input"},2657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(5155),r=a(2115),l=a(968),o=a(2085),d=a(9434);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:(0,d.cn)(c(),a),...r})});i.displayName=l.b.displayName},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>i,Wu:()=>n,ZB:()=>c,Zp:()=>o,aR:()=>d,wL:()=>m});var t=a(5155),r=a(2115),l=a(9434);let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});o.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...r})});d.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});i.displayName="CardDescription";let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",a),...r})});n.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",a),...r})});m.displayName="CardFooter"},8749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}},e=>{e.O(0,[455,56,110,197,441,964,358],()=>e(e.s=416)),_N_E=e.O()}]);