"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useTheme } from "next-themes"
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  FileText,
  Settings,
  BarChart3,
  Bell,
  Search,
  Menu,
  X,
  Sun,
  Moon,
  LogOut,
  User,
  ChevronDown,
  Wrench,
  Factory,
  BookOpen,
  MessageSquare,
  Globe,
  Shield,
} from "lucide-react"

const adminNavigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
    current: false,
  },
  {
    name: "Products",
    href: "/admin/products",
    icon: Package,
    current: false,
    children: [
      { name: "All Products", href: "/admin/products" },
      { name: "Categories", href: "/admin/products/categories" },
      { name: "Inventory", href: "/admin/products/inventory" },
    ]
  },
  {
    name: "Orders",
    href: "/admin/orders",
    icon: ShoppingCart,
    current: false,
    badge: "12",
    children: [
      { name: "All Orders", href: "/admin/orders" },
      { name: "Pending", href: "/admin/orders/pending" },
      { name: "Shipped", href: "/admin/orders/shipped" },
      { name: "Returns", href: "/admin/orders/returns" },
    ]
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: Users,
    current: false,
    children: [
      { name: "All Users", href: "/admin/users" },
      { name: "Customers", href: "/admin/users/customers" },
      { name: "Admins", href: "/admin/users/admins" },
      { name: "Roles", href: "/admin/users/roles" },
    ]
  },
  {
    name: "Services",
    href: "/admin/services",
    icon: Wrench,
    current: false,
    children: [
      { name: "All Services", href: "/admin/services" },
      { name: "Categories", href: "/admin/services/categories" },
      { name: "Inquiries", href: "/admin/services/inquiries" },
    ]
  },
  {
    name: "Production Lines",
    href: "/admin/production-lines",
    icon: Factory,
    current: false,
    children: [
      { name: "All Lines", href: "/admin/production-lines" },
      { name: "Categories", href: "/admin/production-lines/categories" },
      { name: "Inquiries", href: "/admin/production-lines/inquiries" },
    ]
  },
  {
    name: "Blog",
    href: "/admin/blog",
    icon: BookOpen,
    current: false,
    children: [
      { name: "All Posts", href: "/admin/blog" },
      { name: "Categories", href: "/admin/blog/categories" },
      { name: "Comments", href: "/admin/blog/comments" },
      { name: "Authors", href: "/admin/blog/authors" },
    ]
  },
  {
    name: "Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
    current: false,
    children: [
      { name: "Overview", href: "/admin/analytics" },
      { name: "Sales", href: "/admin/analytics/sales" },
      { name: "Traffic", href: "/admin/analytics/traffic" },
      { name: "Reports", href: "/admin/analytics/reports" },
    ]
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: Settings,
    current: false,
    children: [
      { name: "General", href: "/admin/settings" },
      { name: "Payments", href: "/admin/settings/payments" },
      { name: "Shipping", href: "/admin/settings/shipping" },
      { name: "Notifications", href: "/admin/settings/notifications" },
    ]
  },
]

interface AdminLayoutProps {
  children: React.ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const { language, setLanguage, t } = useLanguage()

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev => 
      prev.includes(href) 
        ? prev.filter(item => item !== href)
        : [...prev, href]
    )
  }

  const isCurrentPath = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/')
  }

  const NavigationItem = ({ item }: { item: typeof adminNavigation[0] }) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.href)
    const isCurrent = isCurrentPath(item.href)

    return (
      <div>
        <div
          className={`group flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors cursor-pointer ${
            isCurrent
              ? "bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300"
              : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          }`}
          onClick={() => hasChildren ? toggleExpanded(item.href) : null}
        >
          <Link href={hasChildren ? "#" : item.href} className="flex items-center flex-1">
            <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
            <span>{item.name}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto text-xs">
                {item.badge}
              </Badge>
            )}
          </Link>
          {hasChildren && (
            <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          )}
        </div>
        
        {hasChildren && isExpanded && (
          <div className="ml-8 mt-1 space-y-1">
            {item.children?.map((child) => (
              <Link
                key={child.href}
                href={child.href}
                className={`block px-3 py-2 text-sm rounded-lg transition-colors ${
                  isCurrentPath(child.href)
                    ? "bg-primary-50 dark:bg-primary-950 text-primary-600 dark:text-primary-400"
                    : "text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800"
                }`}
              >
                {child.name}
              </Link>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:static lg:inset-0`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
            <Link href="/admin" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-white" />
              </div>
              <span className="text-lg font-bold text-gray-900 dark:text-white">
                Admin
              </span>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {adminNavigation.map((item) => (
              <NavigationItem key={item.href} item={item} />
            ))}
          </nav>

          {/* User Menu */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-500" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  Admin User
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  <EMAIL>
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="flex-1"
              >
                {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
                className="flex-1"
              >
                <Globe className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="flex-1">
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16">
          <div className="flex items-center justify-between h-full px-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>
              
              {/* Search */}
              <div className="relative hidden md:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 w-64 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </Button>

              {/* Quick Actions */}
              <div className="hidden md:flex items-center space-x-2">
                <Link href="/admin/products/new">
                  <Button size="sm">
                    <Package className="h-4 w-4 mr-2" />
                    Add Product
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
