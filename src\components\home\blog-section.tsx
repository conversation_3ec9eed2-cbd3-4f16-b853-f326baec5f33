"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  BookOpen,
  Calendar,
  User,
  Clock,
  ArrowRight,
  Eye,
  MessageCircle,
  Share2,
  TrendingUp,
  Zap,
  Lightbulb,
  Target,
  Shield,
} from "lucide-react"

interface BlogArticle {
  id: string
  title: string
  excerpt: string
  content: string
  author: {
    name: string
    avatar: string
    role: string
  }
  category: string
  tags: string[]
  publishedAt: Date
  readTime: number
  views: number
  comments: number
  featured: boolean
  image: string
}

const blogArticles: BlogArticle[] = [
  {
    id: "ai-ecommerce-future",
    title: "The Future of AI in E-commerce: Transforming Online Shopping",
    excerpt: "Discover how artificial intelligence is revolutionizing the e-commerce landscape with personalized recommendations, chatbots, and predictive analytics.",
    content: "Full article content here...",
    author: {
      name: "<PERSON>",
      avatar: "/author-1.jpg",
      role: "AI Research Lead"
    },
    category: "Technology",
    tags: ["AI", "E-commerce", "Machine Learning", "Innovation"],
    publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    readTime: 8,
    views: 12500,
    comments: 47,
    featured: true,
    image: "/blog-1.jpg"
  },
  {
    id: "sustainable-manufacturing",
    title: "Sustainable Manufacturing: Building a Greener Future",
    excerpt: "Learn about eco-friendly production methods and how modern manufacturing can reduce environmental impact while maintaining efficiency.",
    content: "Full article content here...",
    author: {
      name: "Michael Chen",
      avatar: "/author-2.jpg",
      role: "Sustainability Expert"
    },
    category: "Sustainability",
    tags: ["Green Tech", "Manufacturing", "Environment", "Innovation"],
    publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    readTime: 6,
    views: 8900,
    comments: 23,
    featured: false,
    image: "/blog-2.jpg"
  },
  {
    id: "cybersecurity-trends-2024",
    title: "Cybersecurity Trends 2024: Protecting Your Digital Assets",
    excerpt: "Stay ahead of cyber threats with the latest security trends, best practices, and emerging technologies in cybersecurity.",
    content: "Full article content here...",
    author: {
      name: "Emily Rodriguez",
      avatar: "/author-3.jpg",
      role: "Security Analyst"
    },
    category: "Security",
    tags: ["Cybersecurity", "Data Protection", "Privacy", "Technology"],
    publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    readTime: 10,
    views: 15200,
    comments: 62,
    featured: true,
    image: "/blog-3.jpg"
  },
  {
    id: "digital-transformation-guide",
    title: "Digital Transformation: A Complete Guide for Modern Businesses",
    excerpt: "Navigate the digital transformation journey with practical strategies, tools, and insights for successful business modernization.",
    content: "Full article content here...",
    author: {
      name: "David Thompson",
      avatar: "/author-4.jpg",
      role: "Digital Strategy Consultant"
    },
    category: "Business",
    tags: ["Digital Transformation", "Strategy", "Innovation", "Growth"],
    publishedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    readTime: 12,
    views: 9800,
    comments: 34,
    featured: false,
    image: "/blog-4.jpg"
  }
]

function formatDate(date: Date): string {
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return "1 day ago"
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} week${Math.ceil(diffDays / 7) > 1 ? 's' : ''} ago`
  return date.toLocaleDateString()
}

function getCategoryIcon(category: string) {
  switch (category.toLowerCase()) {
    case 'technology':
      return Zap
    case 'sustainability':
      return Target
    case 'security':
      return Shield
    case 'business':
      return TrendingUp
    default:
      return Lightbulb
  }
}

function getCategoryColor(category: string) {
  switch (category.toLowerCase()) {
    case 'technology':
      return 'from-blue-500 to-cyan-500'
    case 'sustainability':
      return 'from-green-500 to-emerald-500'
    case 'security':
      return 'from-red-500 to-orange-500'
    case 'business':
      return 'from-purple-500 to-pink-500'
    default:
      return 'from-gray-500 to-gray-600'
  }
}

export function BlogSection() {
  const [hoveredArticle, setHoveredArticle] = useState<string | null>(null)
  const { t } = useLanguage()

  const latestArticles = blogArticles.slice(0, 4)

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900/20 dark:to-indigo-900/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
            <BookOpen className="h-4 w-4 mr-2" />
            Latest Insights
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Expert Insights
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">& Industry News</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Stay informed with the latest trends, insights, and expert analysis from our team of 
            industry professionals and thought leaders.
          </p>
        </div>

        {/* Featured Article */}
        <div className="mb-16">
          {blogArticles.filter(article => article.featured)[0] && (
            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* Image */}
                <div className="relative h-80 lg:h-auto">
                  <div className="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 flex items-center justify-center relative overflow-hidden">
                    <div className="text-8xl opacity-20">📰</div>
                    
                    {/* Featured Badge */}
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4">
                      <Badge className={`bg-gradient-to-r ${getCategoryColor(blogArticles[0].category)} text-white`}>
                        {blogArticles[0].category}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="space-y-6">
                    {/* Meta Info */}
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(blogArticles[0].publishedAt)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{blogArticles[0].readTime} min read</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{blogArticles[0].views.toLocaleString()} views</span>
                      </div>
                    </div>

                    {/* Title */}
                    <h3 className="text-3xl font-bold text-gray-900 dark:text-white leading-tight">
                      {blogArticles[0].title}
                    </h3>

                    {/* Excerpt */}
                    <p className="text-gray-600 dark:text-gray-400 text-lg leading-relaxed">
                      {blogArticles[0].excerpt}
                    </p>

                    {/* Author */}
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {blogArticles[0].author.name}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {blogArticles[0].author.role}
                        </p>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {blogArticles[0].tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* CTA */}
                    <div className="flex items-center space-x-4">
                      <Link href={`/blog/${blogArticles[0].id}`}>
                        <Button className="group bg-gradient-to-r from-blue-500 to-indigo-500 hover:shadow-lg">
                          Read Full Article
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                      </Link>
                      <div className="flex items-center space-x-3 text-gray-600 dark:text-gray-400">
                        <Button variant="ghost" size="sm" className="p-2">
                          <MessageCircle className="h-4 w-4" />
                          <span className="ml-1 text-sm">{blogArticles[0].comments}</span>
                        </Button>
                        <Button variant="ghost" size="sm" className="p-2">
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Latest Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {latestArticles.map((article) => {
            const CategoryIcon = getCategoryIcon(article.category)
            const isHovered = hoveredArticle === article.id
            
            return (
              <Card 
                key={article.id}
                className={`overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group cursor-pointer ${
                  isHovered ? 'ring-2 ring-blue-500' : ''
                }`}
                onMouseEnter={() => setHoveredArticle(article.id)}
                onMouseLeave={() => setHoveredArticle(null)}
              >
                {/* Image */}
                <div className="relative h-48">
                  <div className={`w-full h-full bg-gradient-to-br ${getCategoryColor(article.category)} opacity-20 flex items-center justify-center relative overflow-hidden`}>
                    <div className="text-6xl opacity-30">📄</div>
                    
                    {/* Category Icon */}
                    <div className="absolute top-4 left-4">
                      <div className={`w-8 h-8 bg-gradient-to-br ${getCategoryColor(article.category)} rounded-lg flex items-center justify-center`}>
                        <CategoryIcon className="h-4 w-4 text-white" />
                      </div>
                    </div>

                    {/* Read Time */}
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-black/50 text-white text-xs">
                        {article.readTime} min
                      </Badge>
                    </div>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Category & Date */}
                    <div className="flex items-center justify-between text-sm">
                      <Badge className={`bg-gradient-to-r ${getCategoryColor(article.category)} text-white`}>
                        {article.category}
                      </Badge>
                      <span className="text-gray-500">{formatDate(article.publishedAt)}</span>
                    </div>

                    {/* Title */}
                    <h4 className="font-bold text-gray-900 dark:text-white leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {article.title}
                    </h4>

                    {/* Excerpt */}
                    <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-3">
                      {article.excerpt}
                    </p>

                    {/* Author */}
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <User className="h-3 w-3 text-gray-600 dark:text-gray-300" />
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {article.author.name}
                      </span>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center space-x-1">
                          <Eye className="h-3 w-3" />
                          <span>{(article.views / 1000).toFixed(1)}k</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <MessageCircle className="h-3 w-3" />
                          <span>{article.comments}</span>
                        </span>
                      </div>
                      <Link href={`/blog/${article.id}`}>
                        <Button variant="ghost" size="sm" className="text-xs p-1 h-auto">
                          Read More
                          <ArrowRight className="ml-1 h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-200 dark:border-blue-800">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Stay Updated with Industry Insights
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
              Subscribe to our newsletter and never miss the latest trends, expert analysis, 
              and actionable insights from the world of technology and business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/blog">
                <Button size="lg" className="px-8 py-4 text-lg font-semibold bg-gradient-to-r from-blue-500 to-indigo-500 hover:shadow-lg group">
                  Explore All Articles
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-blue-300 text-blue-600 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20">
                <BookOpen className="mr-2 h-5 w-5" />
                Subscribe to Newsletter
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
