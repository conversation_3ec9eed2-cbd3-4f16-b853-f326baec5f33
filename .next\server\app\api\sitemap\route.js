(()=>{var a={};a.id=407,a.ids=[407],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75378:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{GET:()=>z,generateSitemapIndex:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190);let v=[{id:"1",slug:"premium-wireless-headphones",updatedAt:"2024-01-20"},{id:"2",slug:"smart-fitness-watch",updatedAt:"2024-01-18"},{id:"3",slug:"ergonomic-office-chair",updatedAt:"2024-01-15"},{id:"4",slug:"wireless-charging-pad",updatedAt:"2024-01-12"},{id:"5",slug:"professional-camera-lens",updatedAt:"2024-01-10"}],w=[{id:"1",slug:"web-development",updatedAt:"2024-01-20"},{id:"2",slug:"mobile-app-development",updatedAt:"2024-01-18"},{id:"3",slug:"ui-ux-design",updatedAt:"2024-01-15"},{id:"4",slug:"digital-marketing",updatedAt:"2024-01-12"},{id:"5",slug:"cloud-solutions",updatedAt:"2024-01-10"}],x=[{id:"1",slug:"future-ai-ecommerce-transforming-online-shopping",updatedAt:"2024-01-16"},{id:"2",slug:"sustainable-manufacturing-green-production-lines",updatedAt:"2024-01-13"},{id:"3",slug:"digital-transformation-small-businesses-guide",updatedAt:"2024-01-20"},{id:"4",slug:"ux-design-trends-2024-user-experience",updatedAt:"2024-01-18"},{id:"5",slug:"cloud-infrastructure-best-practices-scalable-applications",updatedAt:"2024-01-10"}],y=[{id:"1",slug:"automated-assembly-line",updatedAt:"2024-01-15"},{id:"2",slug:"quality-control-system",updatedAt:"2024-01-12"},{id:"3",slug:"packaging-automation",updatedAt:"2024-01-10"},{id:"4",slug:"robotic-welding-station",updatedAt:"2024-01-08"},{id:"5",slug:"conveyor-belt-system",updatedAt:"2024-01-05"}];async function z(){try{let a=[];[{loc:"/",changefreq:"daily",priority:1},{loc:"/shop",changefreq:"daily",priority:.9},{loc:"/services",changefreq:"weekly",priority:.8},{loc:"/production-lines",changefreq:"weekly",priority:.8},{loc:"/blog",changefreq:"daily",priority:.7},{loc:"/about",changefreq:"monthly",priority:.6},{loc:"/contact",changefreq:"monthly",priority:.6},{loc:"/auth/login",changefreq:"monthly",priority:.3},{loc:"/auth/register",changefreq:"monthly",priority:.3}].forEach(b=>{a.push({loc:b.loc,lastmod:new Date().toISOString().split("T")[0],changefreq:b.changefreq,priority:b.priority})}),v.forEach(b=>{a.push({loc:`/products/${b.slug}`,lastmod:b.updatedAt,changefreq:"weekly",priority:.8})}),w.forEach(b=>{a.push({loc:`/services/${b.slug}`,lastmod:b.updatedAt,changefreq:"monthly",priority:.7})}),x.forEach(b=>{a.push({loc:`/blog/${b.slug}`,lastmod:b.updatedAt,changefreq:"monthly",priority:.6})}),y.forEach(b=>{a.push({loc:`/production-lines/${b.slug}`,lastmod:b.updatedAt,changefreq:"monthly",priority:.7})}),["electronics","furniture","accessories","photography","gaming"].forEach(b=>{a.push({loc:`/shop/category/${b}`,lastmod:new Date().toISOString().split("T")[0],changefreq:"weekly",priority:.6})}),["development","design","marketing","infrastructure","analytics"].forEach(b=>{a.push({loc:`/services/category/${b}`,lastmod:new Date().toISOString().split("T")[0],changefreq:"monthly",priority:.5})}),["technology","manufacturing","business","design","marketing"].forEach(b=>{a.push({loc:`/blog/category/${b}`,lastmod:new Date().toISOString().split("T")[0],changefreq:"weekly",priority:.5})});let b=function(a){let b=process.env.NEXT_PUBLIC_BASE_URL||"https://aidevcommerce.com",c=a.map(a=>`
  <url>
    <loc>${b}${a.loc}</loc>
    ${a.lastmod?`<lastmod>${a.lastmod}</lastmod>`:""}
    ${a.changefreq?`<changefreq>${a.changefreq}</changefreq>`:""}
    ${a.priority?`<priority>${a.priority}</priority>`:""}
  </url>`).join("");return`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${c}
</urlset>`}(a);return new u.NextResponse(b,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(a){return console.error("Error generating sitemap:",a),new u.NextResponse("Internal Server Error",{status:500})}}async function A(){let a=process.env.NEXT_PUBLIC_BASE_URL||"https://aidevcommerce.com",b=new Date().toISOString().split("T")[0];return`<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${a}/api/sitemap</loc>
    <lastmod>${b}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${a}/api/sitemap/products</loc>
    <lastmod>${b}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${a}/api/sitemap/services</loc>
    <lastmod>${b}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${a}/api/sitemap/blog</loc>
    <lastmod>${b}</lastmod>
  </sitemap>
</sitemapindex>`}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/sitemap/route",pathname:"/api/sitemap",filename:"route",bundlePath:"app/api/sitemap/route"},distDir:".next",projectDir:"",resolvedPagePath:"E:\\aidevcommerce\\src\\app\\api\\sitemap\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/sitemap/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,55],()=>b(b.s=75378));module.exports=c})();