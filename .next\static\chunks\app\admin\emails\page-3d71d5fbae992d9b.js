(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8046],{646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1788:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2172:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>z});var s=a(5155),r=a(2115),l=a(6874),n=a.n(l),i=a(3117),c=a(285),d=a(6695),o=a(6126),m=a(4817),x=a(7481),h=a(9434),p=a(1890);class g{async getEmailTemplates(){try{return[{id:"1",name:"Order Confirmation",subject:"Order Confirmation - #{orderNumber}",htmlContent:'\n            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">\n              <h1 style="color: #333;">Thank you for your order!</h1>\n              <p>Hi {{customerName}},</p>\n              <p>We\'ve received your order and are preparing it for shipment.</p>\n              <div style="background: #f5f5f5; padding: 20px; margin: 20px 0;">\n                <h3>Order Details</h3>\n                <p><strong>Order Number:</strong> {{orderNumber}}</p>\n                <p><strong>Total:</strong> {{orderTotal}}</p>\n                <p><strong>Estimated Delivery:</strong> {{deliveryDate}}</p>\n              </div>\n              <p>You can track your order <a href="{{trackingUrl}}">here</a>.</p>\n              <p>Thank you for shopping with us!</p>\n            </div>\n          ',textContent:"Thank you for your order! Order #{{orderNumber}} - Total: {{orderTotal}}",variables:["customerName","orderNumber","orderTotal","deliveryDate","trackingUrl"],category:"transactional",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-15")},{id:"2",name:"Welcome Email",subject:"Welcome to AIDEVCOMMERCE!",htmlContent:'\n            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">\n              <h1 style="color: #6366f1;">Welcome to AIDEVCOMMERCE!</h1>\n              <p>Hi {{firstName}},</p>\n              <p>Welcome to our community! We\'re excited to have you on board.</p>\n              <div style="background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; margin: 20px 0; border-radius: 8px;">\n                <h3>Get Started</h3>\n                <p>Explore our products and discover amazing deals!</p>\n                <a href="{{shopUrl}}" style="background: white; color: #6366f1; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;">Start Shopping</a>\n              </div>\n              <p>If you have any questions, feel free to contact our support team.</p>\n            </div>\n          ',textContent:"Welcome to AIDEVCOMMERCE! Start exploring our products at {{shopUrl}}",variables:["firstName","shopUrl"],category:"marketing",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-10")},{id:"3",name:"Password Reset",subject:"Reset Your Password",htmlContent:'\n            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">\n              <h1 style="color: #333;">Reset Your Password</h1>\n              <p>Hi {{firstName}},</p>\n              <p>You requested to reset your password. Click the button below to create a new password:</p>\n              <div style="text-align: center; margin: 30px 0;">\n                <a href="{{resetUrl}}" style="background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>\n              </div>\n              <p>This link will expire in 24 hours. If you didn\'t request this, please ignore this email.</p>\n              <p>For security reasons, please don\'t share this link with anyone.</p>\n            </div>\n          ',textContent:"Reset your password: {{resetUrl}}",variables:["firstName","resetUrl"],category:"transactional",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-05")},{id:"4",name:"Newsletter Template",subject:"Weekly Newsletter - {{weekDate}}",htmlContent:'\n            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">\n              <header style="background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; text-align: center;">\n                <h1>AIDEVCOMMERCE Newsletter</h1>\n                <p>{{weekDate}}</p>\n              </header>\n              <div style="padding: 20px;">\n                <h2>This Week\'s Highlights</h2>\n                <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-left: 4px solid #6366f1;">\n                  <h3>{{highlightTitle}}</h3>\n                  <p>{{highlightContent}}</p>\n                </div>\n                <h2>Featured Products</h2>\n                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0;">\n                  {{#each featuredProducts}}\n                  <div style="border: 1px solid #e2e8f0; padding: 15px; border-radius: 8px;">\n                    <h4>{{name}}</h4>\n                    <p style="color: #6366f1; font-weight: bold;">{{price}}</p>\n                  </div>\n                  {{/each}}\n                </div>\n              </div>\n            </div>\n          ',textContent:"AIDEVCOMMERCE Newsletter - {{weekDate}}",variables:["weekDate","highlightTitle","highlightContent","featuredProducts"],category:"marketing",status:"active",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-20")}]}catch(e){return console.error("Failed to fetch email templates:",e),[]}}async createEmailTemplate(e){try{let t={...e,id:"template-".concat(Date.now()),createdAt:new Date,updatedAt:new Date};return console.log("Creating email template:",t),t}catch(e){throw console.error("Failed to create email template:",e),e}}async getEmailCampaigns(){try{return[{id:"1",name:"Welcome Series - Week 1",subject:"Welcome to AIDEVCOMMERCE!",templateId:"2",recipients:["<EMAIL>","<EMAIL>"],status:"sent",stats:{sent:1250,delivered:1235,opened:892,clicked:234,bounced:15,unsubscribed:8},sentAt:new Date("2024-01-15T10:00:00"),createdAt:new Date("2024-01-14"),updatedAt:new Date("2024-01-15")},{id:"2",name:"Product Launch - Smart Watch",subject:"Introducing Our New Smart Watch!",templateId:"4",recipients:[],segmentId:"electronics-interested",scheduledAt:new Date("2024-01-25T09:00:00"),status:"scheduled",stats:{sent:0,delivered:0,opened:0,clicked:0,bounced:0,unsubscribed:0},createdAt:new Date("2024-01-20"),updatedAt:new Date("2024-01-22")}]}catch(e){return console.error("Failed to fetch email campaigns:",e),[]}}async createEmailCampaign(e){try{let t={...e,id:"campaign-".concat(Date.now()),stats:{sent:0,delivered:0,opened:0,clicked:0,bounced:0,unsubscribed:0},createdAt:new Date,updatedAt:new Date};return console.log("Creating email campaign:",t),t}catch(e){throw console.error("Failed to create email campaign:",e),e}}async sendTransactionalEmail(e,t,a,s){try{return console.log("Sending transactional email:",{templateId:e,recipient:t,variables:a,options:s}),{success:!0,messageId:"msg-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))}}catch(e){return console.error("Failed to send transactional email:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async sendMarketingEmail(e){try{return console.log("Sending marketing email campaign:",e),{success:!0}}catch(e){return console.error("Failed to send marketing email:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async sendOrderConfirmation(e){try{return(await this.sendTransactionalEmail("1",e.customerEmail,{customerName:e.customerName,orderNumber:e.orderNumber,orderTotal:e.orderTotal,deliveryDate:e.deliveryDate,trackingUrl:"https://aidevcommerce.com/track/".concat(e.orderNumber)})).success}catch(e){return console.error("Failed to send order confirmation:",e),!1}}async sendWelcomeEmail(e){try{return(await this.sendTransactionalEmail("2",e.email,{firstName:e.firstName,shopUrl:"https://aidevcommerce.com/shop"})).success}catch(e){return console.error("Failed to send welcome email:",e),!1}}async sendPasswordResetEmail(e){try{return(await this.sendTransactionalEmail("3",e.email,{firstName:e.firstName,resetUrl:"https://aidevcommerce.com/auth/reset-password?token=".concat(e.resetToken)})).success}catch(e){return console.error("Failed to send password reset email:",e),!1}}async getEmailAnalytics(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return{totalSent:15420,totalDelivered:15180,totalOpened:8945,totalClicked:2134,deliveryRate:98.4,openRate:58.9,clickRate:13.8,bounceRate:1.6,unsubscribeRate:.3}}catch(e){return console.error("Failed to fetch email analytics:",e),{totalSent:0,totalDelivered:0,totalOpened:0,totalClicked:0,deliveryRate:0,openRate:0,clickRate:0,bounceRate:0,unsubscribeRate:0}}}validateEmail(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}replaceTemplateVariables(e,t){let a=e;return Object.entries(t).forEach(e=>{let[t,s]=e,r=RegExp("{{".concat(t,"}}"),"g");a=a.replace(r,String(s))}),a}constructor(){this.apiKey=p.env.EMAIL_API_KEY||"demo-key",this.baseUrl=p.env.EMAIL_API_URL||"https://api.emailservice.com"}}let u=new g;var y=a(646),j=a(5339),b=a(4861),f=a(4186),v=a(5690);let N=(0,a(9946).A)("pause",[["rect",{x:"14",y:"3",width:"5",height:"18",rx:"1",key:"kaeet6"}],["rect",{x:"5",y:"3",width:"5",height:"18",rx:"1",key:"1wsw3u"}]]);var w=a(2657),k=a(3717),A=a(4357),E=a(9074),C=a(2486),D=a(8883),R=a(3109),S=a(1788),M=a(4616),T=a(2713),W=a(7924);let O=["All Categories","transactional","marketing","notification"],U=["All Status","active","draft","archived"],F=["All Status","draft","scheduled","sending","sent","paused"],L=(e,t)=>({template:{active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",draft:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",archived:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},campaign:{draft:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",sending:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",sent:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",paused:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}})[t][e]||"bg-gray-100 text-gray-800",I=(e,t)=>({template:{active:y.A,draft:j.A,archived:b.A},campaign:{draft:j.A,scheduled:f.A,sending:v.A,sent:y.A,paused:N}})[t][e]||j.A;function z(){let[e,t]=(0,r.useState)("templates"),[a,l]=(0,r.useState)([]),[p,g]=(0,r.useState)([]),[j,b]=(0,r.useState)(null),[f,v]=(0,r.useState)(!0),[N,z]=(0,r.useState)(""),[P,Z]=(0,r.useState)("All Categories"),[q,V]=(0,r.useState)("All Status"),{t:$}=(0,m.o)(),{toast:_}=(0,x.dj)();(0,r.useEffect)(()=>{Y()},[]);let Y=async()=>{try{v(!0);let[e,t,a]=await Promise.all([u.getEmailTemplates(),u.getEmailCampaigns(),u.getEmailAnalytics()]);l(e),g(t),b(a)}catch(e){console.error("Failed to load email data:",e),_({title:"Error",description:"Failed to load email data",variant:"destructive"})}finally{v(!1)}},H=async e=>{try{let t=await u.sendMarketingEmail(e);if(t.success)_({title:"Campaign Sent",description:"Email campaign has been sent successfully"}),Y();else throw Error(t.error)}catch(e){_({title:"Error",description:"Failed to send campaign",variant:"destructive"})}},B=a.filter(e=>{let t=e.name.toLowerCase().includes(N.toLowerCase())||e.subject.toLowerCase().includes(N.toLowerCase()),a="All Categories"===P||e.category===P,s="All Status"===q||e.status===q;return t&&a&&s}),G=p.filter(e=>{let t=e.name.toLowerCase().includes(N.toLowerCase())||e.subject.toLowerCase().includes(N.toLowerCase()),a="All Status"===q||e.status===q;return t&&a}),K=e=>{let{template:t}=e,a=I(t.status,"template");return(0,s.jsx)(d.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,s.jsxs)(d.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2",children:t.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:t.subject}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,s.jsxs)(o.E,{className:L(t.status,"template"),children:[(0,s.jsx)(a,{className:"h-3 w-3 mr-1"}),t.status]}),(0,s.jsx)(o.E,{variant:"outline",className:"capitalize",children:t.category})]})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,s.jsxs)("span",{children:["Variables: ",t.variables.length]}),(0,s.jsxs)("span",{children:["Updated ",(0,h.Yq)(t.updatedAt)]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n(),{href:"/admin/emails/templates/".concat(t.id),children:(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Preview"]})}),(0,s.jsx)(n(),{href:"/admin/emails/templates/".concat(t.id,"/edit"),children:(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-1"}),"Edit"]})}),(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"Duplicate"]})]})]})})},J=e=>{let{campaign:t}=e,a=I(t.status,"campaign"),r=t.stats.sent>0?(t.stats.opened/t.stats.sent*100).toFixed(1):0,l=t.stats.sent>0?(t.stats.clicked/t.stats.sent*100).toFixed(1):0;return(0,s.jsx)(d.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,s.jsxs)(d.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2",children:t.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:t.subject}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,s.jsxs)(o.E,{className:L(t.status,"campaign"),children:[(0,s.jsx)(a,{className:"h-3 w-3 mr-1"}),t.status]}),t.scheduledAt&&(0,s.jsxs)(o.E,{variant:"outline",children:[(0,s.jsx)(E.A,{className:"h-3 w-3 mr-1"}),(0,h.Yq)(t.scheduledAt)]})]})]})}),t.stats.sent>0&&(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-lg font-semibold",children:t.stats.sent.toLocaleString()}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Sent"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-lg font-semibold",children:[r,"%"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Opened"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-lg font-semibold",children:[l,"%"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Clicked"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-lg font-semibold",children:t.stats.unsubscribed}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Unsubscribed"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,s.jsxs)("span",{children:["Recipients: ",t.recipients.length||"Segment"]}),(0,s.jsxs)("span",{children:["Created ",(0,h.Yq)(t.createdAt)]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["draft"===t.status&&(0,s.jsxs)(c.$,{size:"sm",onClick:()=>H(t.id),className:"bg-green-600 hover:bg-green-700",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"Send Now"]}),(0,s.jsx)(n(),{href:"/admin/emails/campaigns/".concat(t.id),children:(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"View"]})}),(0,s.jsx)(n(),{href:"/admin/emails/campaigns/".concat(t.id,"/edit"),children:(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-1"}),"Edit"]})})]})]})})};return f?(0,s.jsx)(i.U,{children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-64"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsx)("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg h-64 animate-pulse"},t))})]})}):(0,s.jsx)(i.U,{children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Email Management"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage email templates, campaigns, and analytics"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(c.$,{variant:"outline",children:[(0,s.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,s.jsx)(n(),{href:"/admin/emails/templates/new",children:(0,s.jsxs)(c.$,{children:[(0,s.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"New Template"]})})]})]}),(0,s.jsx)("div",{className:"border-b",children:(0,s.jsx)("nav",{className:"flex space-x-8",children:[{id:"templates",label:"Templates",icon:D.A},{id:"campaigns",label:"Campaigns",icon:C.A},{id:"analytics",label:"Analytics",icon:T.A}].map(a=>{let r=a.icon;return(0,s.jsxs)("button",{onClick:()=>t(a.id),className:"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ".concat(e===a.id?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,s.jsx)(r,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:a.label})]},a.id)})})}),"analytics"===e?(0,s.jsx)(()=>j?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Sent"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:j.totalSent.toLocaleString()})]}),(0,s.jsx)(D.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Open Rate"}),(0,s.jsxs)("p",{className:"text-2xl font-bold",children:[j.openRate,"%"]})]}),(0,s.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Click Rate"}),(0,s.jsxs)("p",{className:"text-2xl font-bold",children:[j.clickRate,"%"]})]}),(0,s.jsx)(R.A,{className:"h-8 w-8 text-purple-500"})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Delivery Rate"}),(0,s.jsxs)("p",{className:"text-2xl font-bold",children:[j.deliveryRate,"%"]})]}),(0,s.jsx)(y.A,{className:"h-8 w-8 text-orange-500"})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"Email Performance"}),(0,s.jsx)(d.BT,{children:"Detailed engagement metrics"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Delivered"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"".concat(j.deliveryRate,"%")}})}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[j.deliveryRate,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Opened"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"".concat(j.openRate,"%")}})}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[j.openRate,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Clicked"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:"".concat(j.clickRate,"%")}})}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[j.clickRate,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Bounced"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-red-500 h-2 rounded-full",style:{width:"".concat(j.bounceRate,"%")}})}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[j.bounceRate,"%"]})]})]})]})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"Recent Campaigns"}),(0,s.jsx)(d.BT,{children:"Latest campaign performance"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:p.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.stats.sent>0?"".concat(e.stats.sent," sent"):"Not sent yet"})]}),(0,s.jsx)(o.E,{className:L(e.status,"campaign"),children:e.status})]},e.id))})})]})]})]}):null,{}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search ".concat(e,"..."),value:N,onChange:e=>z(e.target.value),className:"pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:["templates"===e&&(0,s.jsx)("select",{value:P,onChange:e=>Z(e.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:O.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),(0,s.jsx)("select",{value:q,onChange:e=>V(e.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:("templates"===e?U:F).map(e=>(0,s.jsx)("option",{value:e,children:e},e))})]})]})})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:"templates"===e?B.map(e=>(0,s.jsx)(K,{template:e},e.id)):G.map(e=>(0,s.jsx)(J,{campaign:e},e.id))}),("templates"===e&&0===B.length||"campaigns"===e&&0===G.length)&&(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"text-center py-12",children:[(0,s.jsx)(D.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,s.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:["No ",e," found"]}),(0,s.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["Get started by creating your first ",e.slice(0,-1)]}),(0,s.jsx)(n(),{href:"/admin/emails/".concat(e,"/new"),children:(0,s.jsxs)(c.$,{children:[(0,s.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Create ",e.slice(0,-1)]})})]})})]})]})})}},2486:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2725:(e,t,a)=>{Promise.resolve().then(a.bind(a,2172))},3109:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3717:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4357:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4616:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5690:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{e.O(0,[3274,3830,9197,7563,8441,5964,7358],()=>e(e.s=2725)),_N_E=e.O()}]);