(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1957:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6707,23)),Promise.resolve().then(s.t.bind(s,749,23)),Promise.resolve().then(s.bind(s,3672)),Promise.resolve().then(s.bind(s,2262)),Promise.resolve().then(s.bind(s,4817)),Promise.resolve().then(s.bind(s,6415)),Promise.resolve().then(s.bind(s,7890)),Promise.resolve().then(s.bind(s,7484)),Promise.resolve().then(s.t.bind(s,5786,23))},2262:(e,t,s)=>{"use strict";s.d(t,{Header:()=>N});var a=s(5155),r=s(2115),i=s(6874),n=s.n(i),l=s(5695),d=s(1362),c=s(285),o=s(4817),m=s(7481),u=s(2921),x=s(6468),h=s(7924),f=s(2098),v=s(3509),p=s(7809),g=s(1976),j=(s(1007),s(7108),s(381),s(4835),s(4416)),b=s(4783);function N(){let[e,t]=(0,r.useState)(!1),{theme:s,setTheme:i}=(0,d.D)(),{language:N,setLanguage:y,t:w}=(0,o.o)(),{toast:k}=(0,m.dj)();(0,l.useRouter)();let{totalItems:C,openCart:I}=(0,u.x)(),{totalItems:A}=(0,x.q)(),$=()=>{y("en"===N?"ar":"en"),k({title:w("common.success"),description:"Language changed to ".concat("en"===N?"العربية":"English")})},q=()=>{i("dark"===s?"light":"dark")},z=[{name:w("nav.home"),href:"/"},{name:w("nav.shop"),href:"/shop"},{name:w("nav.services"),href:"/services"},{name:w("nav.production-lines"),href:"/production-lines"},{name:w("nav.blog"),href:"/blog"},{name:w("nav.about"),href:"/about"},{name:w("nav.contact"),href:"/contact"}];return(0,a.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsx)(n(),{href:"/",className:"flex items-center space-x-2",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-gradient-primary",children:"AIDEVCOMMERCE"})}),(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:z.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:e.name},e.href))}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.$,{variant:"ghost",size:"sm",className:"hidden sm:flex",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)(c.$,{variant:"ghost",size:"sm",onClick:q,className:"hidden sm:flex",children:"dark"===s?(0,a.jsx)(f.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsx)(c.$,{variant:"ghost",size:"sm",onClick:$,className:"hidden sm:flex text-xs",children:"en"===N?"عربي":"EN"}),(0,a.jsxs)(c.$,{variant:"ghost",size:"sm",className:"relative",onClick:I,children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),C>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-primary text-xs text-primary-foreground flex items-center justify-center",children:C>99?"99+":C})]}),(0,a.jsx)(n(),{href:"/account/wishlist",children:(0,a.jsxs)(c.$,{variant:"ghost",size:"sm",className:"relative hidden sm:flex",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),A>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center",children:A>99?"99+":A})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n(),{href:"/auth/login",children:(0,a.jsx)(c.$,{variant:"ghost",size:"sm",children:w("auth.login")})}),(0,a.jsx)(n(),{href:"/auth/register",children:(0,a.jsx)(c.$,{size:"sm",children:w("auth.register")})})]}),(0,a.jsx)(c.$,{variant:"ghost",size:"sm",className:"md:hidden",onClick:()=>t(!e),children:e?(0,a.jsx)(j.A,{className:"h-4 w-4"}):(0,a.jsx)(b.A,{className:"h-4 w-4"})})]})]}),e&&(0,a.jsx)("div",{className:"md:hidden border-t py-4",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-4",children:[z.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",onClick:()=>t(!1),children:e.name},e.href)),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:q,className:"flex items-center space-x-2",children:["dark"===s?(0,a.jsx)(f.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"dark"===s?"Light":"Dark"})]}),(0,a.jsx)(c.$,{variant:"ghost",size:"sm",onClick:$,className:"flex items-center space-x-2",children:(0,a.jsx)("span",{children:"en"===N?"عربي":"English"})})]})]})})]})})}},2921:(e,t,s)=>{"use strict";s.d(t,{x:()=>i});var a=s(5453),r=s(6786);let i=(0,a.v)()((0,r.Zr)((e,t)=>({items:[],isOpen:!1,addItem:s=>{let a=t().items,r=a.findIndex(e=>{var t,a;return e.productId===s.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(a=s.variant)?void 0:a.id)});if(r>-1){let t=a[r],i=t.quantity+(s.quantity||1),n=Math.min(s.maxQuantity,t.maxQuantity);e({items:a.map((e,t)=>t===r?{...e,quantity:Math.min(i,n)}:e)})}else{var i;e({items:[...a,{...s,id:"".concat(s.productId,"-").concat((null==(i=s.variant)?void 0:i.id)||"default","-").concat(Date.now()),quantity:s.quantity||1}]})}},removeItem:s=>{e({items:t().items.filter(e=>e.id!==s)})},updateQuantity:(s,a)=>{if(a<=0)return void t().removeItem(s);e({items:t().items.map(e=>e.id===s?{...e,quantity:Math.min(a,e.maxQuantity)}:e)})},clearCart:()=>{e({items:[]})},toggleCart:()=>{e({isOpen:!t().isOpen})},openCart:()=>{e({isOpen:!0})},closeCart:()=>{e({isOpen:!1})},get totalItems(){return t().items.reduce((e,t)=>e+t.quantity,0)},get subtotal(){return t().items.reduce((e,t)=>{var s;return e+((null==(s=t.variant)?void 0:s.price)||t.price)*t.quantity},0)},get tax(){return .08*t().subtotal},get shipping(){return t().subtotal>=100?0:10},get total(){return t().subtotal+t().tax+t().shipping}}),{name:"cart-storage",partialize:e=>({items:e.items})}))},3672:(e,t,s)=>{"use strict";s.d(t,{CartSidebar:()=>p});var a=s(5155),r=s(2115),i=s(6874),n=s.n(i),l=s(285),d=s(2921),c=s(4817),o=s(9434),m=s(4416),u=s(6151),x=s(7712),h=s(4616),f=s(2525),v=s(2138);function p(){let{items:e,isOpen:t,closeCart:s,updateQuantity:i,removeItem:p,totalItems:g,subtotal:j,tax:b,shipping:N,total:y}=(0,d.x)(),{t:w}=(0,c.o)();return((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,s]),t)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 transition-opacity",onClick:s}),(0,a.jsx)("div",{className:"fixed right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 z-50 shadow-xl transform transition-transform",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold",children:["Shopping Cart (",g,")"]}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:0===e.length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Add some products to get started"}),(0,a.jsx)(n(),{href:"/shop",children:(0,a.jsx)(l.$,{onClick:s,children:"Continue Shopping"})})]}):(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-start space-x-4 p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"h-16 w-16 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("div",{className:"text-2xl",children:"\uD83D\uDCE6"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)(n(),{href:"/product/".concat(e.slug),onClick:s,className:"font-medium text-sm hover:text-primary-600 transition-colors line-clamp-2",children:e.name}),e.variant&&(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.variant.name}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)("span",{className:"font-medium text-sm",children:(0,o.$g)((null==(t=e.variant)?void 0:t.price)||e.price)}),!e.inStock&&(0,a.jsx)("span",{className:"text-xs text-red-500",children:"Out of Stock"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center border rounded",children:[(0,a.jsx)("button",{onClick:()=>i(e.id,e.quantity-1),className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",disabled:e.quantity<=1,children:(0,a.jsx)(x.A,{className:"h-3 w-3"})}),(0,a.jsx)("span",{className:"px-3 py-1 text-sm border-x",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>i(e.id,e.quantity+1),className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",disabled:e.quantity>=e.maxQuantity,children:(0,a.jsx)(h.A,{className:"h-3 w-3"})})]}),(0,a.jsx)("button",{onClick:()=>p(e.id),className:"text-red-500 hover:text-red-700 transition-colors p-1",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})]})]},e.id)})})}),e.length>0&&(0,a.jsxs)("div",{className:"border-t p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsx)("span",{children:(0,o.$g)(j)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Tax"}),(0,a.jsx)("span",{children:(0,o.$g)(b)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{children:0===N?(0,a.jsx)("span",{className:"text-green-600",children:"Free"}):(0,o.$g)(N)})]}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold text-base pt-2 border-t",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsx)("span",{children:(0,o.$g)(y)})]})]}),N>0&&(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:[(0,a.jsx)("div",{className:"flex justify-between text-sm mb-2",children:(0,a.jsxs)("span",{className:"text-blue-700 dark:text-blue-300",children:["Add ",(0,o.$g)(100-j)," for free shipping"]})}),(0,a.jsx)("div",{className:"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(j/100*100,100),"%")}})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n(),{href:"/cart",className:"block",children:(0,a.jsx)(l.$,{variant:"outline",className:"w-full",onClick:s,children:"View Cart"})}),(0,a.jsx)(n(),{href:"/checkout",className:"block",children:(0,a.jsxs)(l.$,{className:"w-full",onClick:s,children:["Checkout",(0,a.jsx)(v.A,{className:"h-4 w-4 ml-2"})]})})]})]})]})})]}):null}},5786:()=>{},6415:(e,t,s)=>{"use strict";s.d(t,{QueryProvider:()=>l});var a=s(5155),r=s(2922),i=s(6715),n=s(2115);function l(e){let{children:t}=e,[s]=(0,n.useState)(()=>new r.E({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<2}}}));return(0,a.jsxs)(i.Ht,{client:s,children:[t,!1]})}},6468:(e,t,s)=>{"use strict";s.d(t,{q:()=>i});var a=s(5453),r=s(6786);let i=(0,a.v)()((0,r.Zr)((e,t)=>({items:[],addItem:s=>{let a=t().items;if(-1===a.findIndex(e=>{var t,a;return e.productId===s.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(a=s.variant)?void 0:a.id)})){var r;e({items:[...a,{...s,id:"".concat(s.productId,"-").concat((null==(r=s.variant)?void 0:r.id)||"default","-").concat(Date.now()),addedAt:new Date}]})}},removeItem:s=>{e({items:t().items.filter(e=>e.id!==s)})},toggleItem:s=>{let a=t().items,r=a.findIndex(e=>{var t,a;return e.productId===s.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(a=s.variant)?void 0:a.id)});return r>-1?(e({items:a.filter((e,t)=>t!==r)}),!1):(t().addItem(s),!0)},clearWishlist:()=>{e({items:[]})},isInWishlist:(e,s)=>t().items.some(t=>{var a;return t.productId===e&&(null==(a=t.variant)?void 0:a.id)===s}),get totalItems(){return t().items.length}}),{name:"wishlist-storage",partialize:e=>({items:e.items})}))},7484:(e,t,s)=>{"use strict";s.d(t,{ToastProvider:()=>g});var a=s(5155),r=s(7481),i=s(2115),n=s(3156),l=s(2085),d=s(4416),c=s(9434);let o=n.Kq,m=i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.LM,{ref:t,className:(0,c.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...r})});m.displayName=n.LM.displayName;let u=(0,l.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=i.forwardRef((e,t)=>{let{className:s,variant:r,...i}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,c.cn)(u({variant:r}),s),...i})});x.displayName=n.bL.displayName,i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.rc,{ref:t,className:(0,c.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...r})}).displayName=n.rc.displayName;let h=i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.bm,{ref:t,className:(0,c.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});h.displayName=n.bm.displayName;let f=i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,c.cn)("text-sm font-semibold",s),...r})});f.displayName=n.hE.displayName;let v=i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,c.cn)("text-sm opacity-90",s),...r})});function p(){let{toasts:e}=(0,r.dj)();return(0,a.jsxs)(o,{children:[e.map(function(e){let{id:t,title:s,description:r,action:i,...n}=e;return(0,a.jsxs)(x,{...n,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[s&&(0,a.jsx)(f,{children:s}),r&&(0,a.jsx)(v,{children:r})]}),i,(0,a.jsx)(h,{})]},t)}),(0,a.jsx)(m,{})]})}function g(e){let{children:t}=e;return(0,a.jsxs)(a.Fragment,{children:[t,(0,a.jsx)(p,{})]})}v.displayName=n.VY.displayName},7890:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>i});var a=s(5155);s(2115);var r=s(1362);function i(e){let{children:t,...s}=e;return(0,a.jsx)(r.N,{...s,children:t})}}},e=>{e.O(0,[337,274,670,357,197,441,964,358],()=>e(e.s=1957)),_N_E=e.O()}]);