(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8733],{289:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var a=t(5155),r=t(2115),l=t(6874),i=t.n(l),n=t(3117),c=t(285),d=t(6695),x=t(6126),m=t(4817),o=t(7481),h=t(9434),u=t(9946);let p=(0,u.A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var g=t(5525),j=t(1007),v=t(646);let y=(0,u.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),N=(0,u.A)("ban",[["path",{d:"M4.929 4.929 19.07 19.071",key:"196cmz"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var f=t(9420),A=t(4516),b=t(8883),k=t(9397),w=t(2657),S=t(3717),z=t(1788),D=t(9869),O=t(4616),C=t(7580);let V=(0,u.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var M=t(7924);let L=[{id:"1",name:"John Doe",email:"<EMAIL>",phone:"+****************",role:"customer",status:"active",avatar:"/user-1.jpg",address:{street:"123 Main St",city:"New York",state:"NY",zip:"10001",country:"USA"},stats:{totalOrders:12,totalSpent:2450.99,avgOrderValue:204.25,lastOrderDate:new Date("2024-01-20")},createdAt:new Date("2023-06-15"),lastLoginAt:new Date("2024-01-21T10:30:00"),emailVerified:!0,phoneVerified:!0,notes:"VIP customer - provides excellent feedback"},{id:"2",name:"Jane Smith",email:"<EMAIL>",phone:"+****************",role:"customer",status:"active",avatar:"/user-2.jpg",address:{street:"456 Oak Ave",city:"Los Angeles",state:"CA",zip:"90210",country:"USA"},stats:{totalOrders:8,totalSpent:1299.99,avgOrderValue:162.5,lastOrderDate:new Date("2024-01-19")},createdAt:new Date("2023-08-22"),lastLoginAt:new Date("2024-01-20T15:45:00"),emailVerified:!0,phoneVerified:!1,notes:""},{id:"3",name:"Mike Johnson",email:"<EMAIL>",phone:"+****************",role:"customer",status:"inactive",avatar:"/user-3.jpg",address:{street:"789 Pine St",city:"Chicago",state:"IL",zip:"60601",country:"USA"},stats:{totalOrders:3,totalSpent:450.99,avgOrderValue:150.33,lastOrderDate:new Date("2023-12-15")},createdAt:new Date("2023-04-10"),lastLoginAt:new Date("2023-12-20T09:15:00"),emailVerified:!0,phoneVerified:!0,notes:"Has not placed orders recently"},{id:"4",name:"Sarah Wilson",email:"<EMAIL>",phone:"+****************",role:"admin",status:"active",avatar:"/user-4.jpg",address:{street:"321 Elm St",city:"Miami",state:"FL",zip:"33101",country:"USA"},stats:{totalOrders:0,totalSpent:0,avgOrderValue:0,lastOrderDate:null},createdAt:new Date("2023-01-15"),lastLoginAt:new Date("2024-01-21T14:20:00"),emailVerified:!0,phoneVerified:!0,notes:"Admin user - full system access"},{id:"5",name:"David Brown",email:"<EMAIL>",phone:"+****************",role:"customer",status:"suspended",avatar:"/user-5.jpg",address:{street:"654 Maple Ave",city:"Seattle",state:"WA",zip:"98101",country:"USA"},stats:{totalOrders:15,totalSpent:3200.5,avgOrderValue:213.37,lastOrderDate:new Date("2024-01-10")},createdAt:new Date("2023-03-08"),lastLoginAt:new Date("2024-01-15T11:45:00"),emailVerified:!0,phoneVerified:!0,notes:"Suspended due to payment disputes"},{id:"6",name:"Emily Davis",email:"<EMAIL>",phone:"+****************",role:"moderator",status:"active",avatar:"/user-6.jpg",address:{street:"987 Cedar St",city:"Boston",state:"MA",zip:"02101",country:"USA"},stats:{totalOrders:2,totalSpent:299.99,avgOrderValue:150,lastOrderDate:new Date("2024-01-05")},createdAt:new Date("2023-09-12"),lastLoginAt:new Date("2024-01-21T16:10:00"),emailVerified:!0,phoneVerified:!0,notes:"Content moderator - manages blog and reviews"}],$=["All Roles","customer","admin","moderator"],U=["All Status","active","inactive","suspended"];function E(){let[e,s]=(0,r.useState)(""),[t,l]=(0,r.useState)("All Roles"),[u,E]=(0,r.useState)("All Status"),[q,W]=(0,r.useState)([]),{t:T}=(0,m.o)(),{toast:Z}=(0,o.dj)(),H=L.filter(s=>{let a=s.name.toLowerCase().includes(e.toLowerCase())||s.email.toLowerCase().includes(e.toLowerCase())||s.phone.includes(e),r="All Roles"===t||s.role===t,l="All Status"===u||s.status===u;return a&&r&&l}),_=e=>{Z({title:"Bulk Action",description:"".concat(e," applied to ").concat(q.length," users")}),W([])},F=e=>{let{user:s}=e,t=(e=>{switch(e){case"admin":return p;case"moderator":return g.A;default:return j.A}})(s.role),r=(e=>{switch(e){case"active":return v.A;case"inactive":default:return y;case"suspended":return N}})(s.status);return(0,a.jsx)(d.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:q.includes(s.id),onChange:()=>{var e;return e=s.id,void W(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},className:"mt-1"}),(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center",children:(0,a.jsx)(j.A,{className:"h-6 w-6 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-lg flex items-center space-x-2",children:[(0,a.jsx)("span",{children:s.name}),s.emailVerified&&(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-500"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:s.email})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Joined ",(0,h.Yq)(s.createdAt)]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:["Last login: ",(0,h.Yq)(s.lastLoginAt)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-3 text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:s.phone}),s.phoneVerified&&(0,a.jsx)(v.A,{className:"h-3 w-3 text-green-500"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[s.address.city,", ",s.address.state]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsxs)(x.E,{className:"flex items-center space-x-1 ".concat((e=>{switch(e){case"admin":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";case"moderator":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"customer":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(s.role)),children:[(0,a.jsx)(t,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"capitalize",children:s.role})]}),(0,a.jsxs)(x.E,{className:"flex items-center space-x-1 ".concat((e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"inactive":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"suspended":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(s.status)),children:[(0,a.jsx)(r,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"capitalize",children:s.status})]})]}),"customer"===s.role&&(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold",children:s.stats.totalOrders}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Orders"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-lg font-semibold",children:["$",s.stats.totalSpent.toFixed(2)]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Total Spent"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-lg font-semibold",children:["$",s.stats.avgOrderValue.toFixed(2)]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Avg Order"})]})]}),s.notes&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium mb-1",children:"Notes:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded",children:s.notes})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"Email"]}),(0,a.jsxs)(c.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Call"]}),"customer"===s.role&&(0,a.jsx)(i(),{href:"/admin/users/".concat(s.id,"/orders"),children:(0,a.jsxs)(c.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-1"}),"Orders"]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i(),{href:"/admin/users/".concat(s.id),children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"View"]})}),(0,a.jsx)(i(),{href:"/admin/users/".concat(s.id,"/edit"),children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-1"}),"Edit"]})}),(0,a.jsxs)("select",{value:s.status,onChange:e=>{var t;return s.id,t=e.target.value,void Z({title:"User Updated",description:"User status updated to ".concat(t)})},className:"px-2 py-1 text-sm border rounded",children:[(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"suspended",children:"Suspended"})]})]})]})]})]})})})})};return(0,a.jsx)(n.U,{children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Users"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage user accounts, roles, and permissions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(c.$,{variant:"outline",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(c.$,{variant:"outline",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Import"]}),(0,a.jsx)(i(),{href:"/admin/users/new",children:(0,a.jsxs)(c.$,{children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Add User"]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:L.length})]}),(0,a.jsx)(C.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Active Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:L.filter(e=>"active"===e.status).length})]}),(0,a.jsx)(V,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:L.filter(e=>"customer"===e.role).length})]}),(0,a.jsx)(j.A,{className:"h-8 w-8 text-purple-500"})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Admins"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:L.filter(e=>"admin"===e.role||"moderator"===e.role).length})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(M.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search users...",value:e,onChange:e=>s(e.target.value),className:"pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("select",{value:t,onChange:e=>l(e.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:$.map(e=>(0,a.jsx)("option",{value:e,children:e},e))}),(0,a.jsx)("select",{value:u,onChange:e=>E(e.target.value),className:"px-3 py-2 border rounded-lg bg-white dark:bg-gray-800",children:U.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]})]})})}),q.length>0&&(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:[q.length," users selected"]}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>{W(q.length===H.length?[]:H.map(e=>e.id))},children:q.length===H.length?"Deselect All":"Select All"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>_("Activate"),children:"Activate"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>_("Suspend"),children:"Suspend"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>_("Export Selected"),children:"Export"})]})]})})}),(0,a.jsx)("div",{className:"space-y-4",children:H.map(e=>(0,a.jsx)(F,{user:e},e.id))}),0===H.length&&(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.Wu,{className:"text-center py-12",children:[(0,a.jsx)(C.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"No users found"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Try adjusting your search or filter criteria"}),(0,a.jsx)(c.$,{onClick:()=>{s(""),l("All Roles"),E("All Status")},children:"Clear Filters"})]})}),H.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Showing ",H.length," of ",L.length," users"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",disabled:!0,children:"Previous"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",className:"bg-primary-50 dark:bg-primary-900",children:"1"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",children:"2"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",children:"Next"})]})]})]})})}},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},8702:(e,s,t)=>{Promise.resolve().then(t.bind(t,289))},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9397:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9420:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}},e=>{e.O(0,[3274,3830,9197,7563,8441,5964,7358],()=>e(e.s=8702)),_N_E=e.O()}]);