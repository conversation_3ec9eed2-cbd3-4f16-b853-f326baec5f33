"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { AccountLayout } from "@/components/layout/account-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Plus,
  MapPin,
  Edit,
  Trash2,
  Home,
  Building,
  Star,
  Check,
} from "lucide-react"

const addressSchema = z.object({
  type: z.enum(["shipping", "billing"]),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  company: z.string().optional(),
  address1: z.string().min(5, "Address must be at least 5 characters"),
  address2: z.string().optional(),
  city: z.string().min(2, "City must be at least 2 characters"),
  state: z.string().min(2, "State must be at least 2 characters"),
  postalCode: z.string().min(5, "Postal code must be at least 5 characters"),
  country: z.string().min(2, "Country must be at least 2 characters"),
  phone: z.string().optional(),
  isDefault: z.boolean().default(false),
})

type AddressFormData = z.infer<typeof addressSchema>

// Mock data
const mockAddresses = [
  {
    id: "1",
    type: "shipping" as const,
    firstName: "John",
    lastName: "Doe",
    company: "",
    address1: "123 Main Street",
    address2: "Apt 4B",
    city: "New York",
    state: "NY",
    postalCode: "10001",
    country: "United States",
    phone: "+****************",
    isDefault: true,
  },
  {
    id: "2",
    type: "billing" as const,
    firstName: "John",
    lastName: "Doe",
    company: "Acme Corp",
    address1: "456 Business Ave",
    address2: "Suite 200",
    city: "New York",
    state: "NY",
    postalCode: "10002",
    country: "United States",
    phone: "+****************",
    isDefault: false,
  },
]

export default function AddressesPage() {
  const [addresses, setAddresses] = useState(mockAddresses)
  const [isAddingAddress, setIsAddingAddress] = useState(false)
  const [editingAddress, setEditingAddress] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const { t } = useLanguage()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
  })

  const onSubmit = async (data: AddressFormData) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (editingAddress) {
        // Update existing address
        setAddresses(prev => prev.map(addr => 
          addr.id === editingAddress 
            ? { ...addr, ...data }
            : addr
        ))
        toast({
          title: t('common.success'),
          description: "Address updated successfully",
        })
        setEditingAddress(null)
      } else {
        // Add new address
        const newAddress = {
          id: Date.now().toString(),
          ...data,
        }
        setAddresses(prev => [...prev, newAddress])
        toast({
          title: t('common.success'),
          description: "Address added successfully",
        })
        setIsAddingAddress(false)
      }
      
      reset()
    } catch (error) {
      toast({
        title: t('common.error'),
        description: "Failed to save address",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEdit = (address: typeof mockAddresses[0]) => {
    setEditingAddress(address.id)
    setIsAddingAddress(true)
    
    // Populate form with address data
    Object.entries(address).forEach(([key, value]) => {
      if (key !== 'id') {
        setValue(key as keyof AddressFormData, value)
      }
    })
  }

  const handleDelete = async (addressId: string) => {
    if (confirm("Are you sure you want to delete this address?")) {
      setIsLoading(true)
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
        
        setAddresses(prev => prev.filter(addr => addr.id !== addressId))
        toast({
          title: t('common.success'),
          description: "Address deleted successfully",
        })
      } catch (error) {
        toast({
          title: t('common.error'),
          description: "Failed to delete address",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleSetDefault = async (addressId: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      setAddresses(prev => prev.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      })))
      
      toast({
        title: t('common.success'),
        description: "Default address updated",
      })
    } catch (error) {
      toast({
        title: t('common.error'),
        description: "Failed to update default address",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setIsAddingAddress(false)
    setEditingAddress(null)
    reset()
  }

  return (
    <AccountLayout>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Addresses
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your shipping and billing addresses
            </p>
          </div>
          
          {!isAddingAddress && (
            <Button onClick={() => setIsAddingAddress(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </Button>
          )}
        </div>

        {/* Add/Edit Address Form */}
        {isAddingAddress && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>
                {editingAddress ? "Edit Address" : "Add New Address"}
              </CardTitle>
              <CardDescription>
                {editingAddress 
                  ? "Update your address information"
                  : "Add a new shipping or billing address"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Address Type */}
                <div className="space-y-2">
                  <Label>Address Type</Label>
                  <div className="flex space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        value="shipping"
                        {...register("type")}
                        className="text-primary-600"
                      />
                      <Home className="h-4 w-4" />
                      <span>Shipping</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        value="billing"
                        {...register("type")}
                        className="text-primary-600"
                      />
                      <Building className="h-4 w-4" />
                      <span>Billing</span>
                    </label>
                  </div>
                  {errors.type && (
                    <p className="text-sm text-destructive">{errors.type.message}</p>
                  )}
                </div>

                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      {...register("firstName")}
                      placeholder="Enter first name"
                    />
                    {errors.firstName && (
                      <p className="text-sm text-destructive">{errors.firstName.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      {...register("lastName")}
                      placeholder="Enter last name"
                    />
                    {errors.lastName && (
                      <p className="text-sm text-destructive">{errors.lastName.message}</p>
                    )}
                  </div>
                </div>

                {/* Company */}
                <div className="space-y-2">
                  <Label htmlFor="company">Company (Optional)</Label>
                  <Input
                    id="company"
                    {...register("company")}
                    placeholder="Enter company name"
                  />
                </div>

                {/* Address Fields */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address1">Address Line 1</Label>
                    <Input
                      id="address1"
                      {...register("address1")}
                      placeholder="Enter street address"
                    />
                    {errors.address1 && (
                      <p className="text-sm text-destructive">{errors.address1.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address2">Address Line 2 (Optional)</Label>
                    <Input
                      id="address2"
                      {...register("address2")}
                      placeholder="Apartment, suite, etc."
                    />
                  </div>
                </div>

                {/* Location Fields */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      {...register("city")}
                      placeholder="Enter city"
                    />
                    {errors.city && (
                      <p className="text-sm text-destructive">{errors.city.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="state">State/Province</Label>
                    <Input
                      id="state"
                      {...register("state")}
                      placeholder="Enter state"
                    />
                    {errors.state && (
                      <p className="text-sm text-destructive">{errors.state.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postal Code</Label>
                    <Input
                      id="postalCode"
                      {...register("postalCode")}
                      placeholder="Enter postal code"
                    />
                    {errors.postalCode && (
                      <p className="text-sm text-destructive">{errors.postalCode.message}</p>
                    )}
                  </div>
                </div>

                {/* Country and Phone */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Input
                      id="country"
                      {...register("country")}
                      placeholder="Enter country"
                    />
                    {errors.country && (
                      <p className="text-sm text-destructive">{errors.country.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone (Optional)</Label>
                    <Input
                      id="phone"
                      type="tel"
                      {...register("phone")}
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>

                {/* Default Address */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    {...register("isDefault")}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <Label htmlFor="isDefault">Set as default address</Label>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading 
                      ? "Saving..." 
                      : editingAddress 
                        ? "Update Address" 
                        : "Add Address"
                    }
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Address List */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {addresses.map((address) => (
            <Card key={address.id} className="relative">
              {address.isDefault && (
                <div className="absolute top-4 right-4">
                  <div className="flex items-center space-x-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-full text-xs font-medium">
                    <Star className="h-3 w-3 fill-current" />
                    <span>Default</span>
                  </div>
                </div>
              )}
              
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  {address.type === 'shipping' ? (
                    <Home className="h-5 w-5 mr-2" />
                  ) : (
                    <Building className="h-5 w-5 mr-2" />
                  )}
                  {address.type === 'shipping' ? 'Shipping' : 'Billing'} Address
                </CardTitle>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-2 text-sm">
                  <p className="font-medium">
                    {address.firstName} {address.lastName}
                  </p>
                  {address.company && (
                    <p className="text-gray-600 dark:text-gray-400">
                      {address.company}
                    </p>
                  )}
                  <p>{address.address1}</p>
                  {address.address2 && <p>{address.address2}</p>}
                  <p>
                    {address.city}, {address.state} {address.postalCode}
                  </p>
                  <p>{address.country}</p>
                  {address.phone && (
                    <p className="text-gray-600 dark:text-gray-400">
                      {address.phone}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(address)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(address.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                  
                  {!address.isDefault && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleSetDefault(address.id)}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Set Default
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {addresses.length === 0 && !isAddingAddress && (
          <Card>
            <CardContent className="text-center py-12">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No addresses yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Add your first address to get started with orders
              </p>
              <Button onClick={() => setIsAddingAddress(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Address
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </AccountLayout>
  )
}
