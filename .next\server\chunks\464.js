"use strict";exports.id=464,exports.ids=[464],exports.modules={11437:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},13861:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17426:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("factory",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M16 16h.01",key:"1f9h7w"}],["path",{d:"M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a.5.5 0 0 0-.769-.422l-4.462 2.844A.5.5 0 0 1 15 10.5v-2a.5.5 0 0 0-.769-.422L9.77 10.922A.5.5 0 0 1 9 10.5V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z",key:"1iv0i2"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},25541:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},41312:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},53411:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},82080:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},82679:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.106-3.105c.32-.322.863-.22.983.218a6 6 0 0 1-8.259 7.057l-7.91 7.91a1 1 0 0 1-2.999-3l7.91-7.91a6 6 0 0 1 7.057-8.259c.438.12.54.662.219.984z",key:"1ngwbx"}]])},88528:(a,b,c)=>{c.d(b,{U:()=>I});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(16189),i=c(29523),j=c(96834),k=c(98436),l=c(10218),m=c(62688);let n=(0,m.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var o=c(19080),p=c(28561),q=c(41312),r=c(82679),s=c(17426),t=c(82080),u=c(53411),v=c(84027);let w=(0,m.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var x=c(99891),y=c(11860),z=c(58869),A=c(21134),B=c(363),C=c(11437),D=c(40083),E=c(12941),F=c(99270),G=c(97051);let H=[{name:"Dashboard",href:"/admin",icon:n,current:!1},{name:"Products",href:"/admin/products",icon:o.A,current:!1,children:[{name:"All Products",href:"/admin/products"},{name:"Categories",href:"/admin/products/categories"},{name:"Inventory",href:"/admin/products/inventory"}]},{name:"Orders",href:"/admin/orders",icon:p.A,current:!1,badge:"12",children:[{name:"All Orders",href:"/admin/orders"},{name:"Pending",href:"/admin/orders/pending"},{name:"Shipped",href:"/admin/orders/shipped"},{name:"Returns",href:"/admin/orders/returns"}]},{name:"Users",href:"/admin/users",icon:q.A,current:!1,children:[{name:"All Users",href:"/admin/users"},{name:"Customers",href:"/admin/users/customers"},{name:"Admins",href:"/admin/users/admins"},{name:"Roles",href:"/admin/users/roles"}]},{name:"Services",href:"/admin/services",icon:r.A,current:!1,children:[{name:"All Services",href:"/admin/services"},{name:"Categories",href:"/admin/services/categories"},{name:"Inquiries",href:"/admin/services/inquiries"}]},{name:"Production Lines",href:"/admin/production-lines",icon:s.A,current:!1,children:[{name:"All Lines",href:"/admin/production-lines"},{name:"Categories",href:"/admin/production-lines/categories"},{name:"Inquiries",href:"/admin/production-lines/inquiries"}]},{name:"Blog",href:"/admin/blog",icon:t.A,current:!1,children:[{name:"All Posts",href:"/admin/blog"},{name:"Categories",href:"/admin/blog/categories"},{name:"Comments",href:"/admin/blog/comments"},{name:"Authors",href:"/admin/blog/authors"}]},{name:"Analytics",href:"/admin/analytics",icon:u.A,current:!1,children:[{name:"Overview",href:"/admin/analytics"},{name:"Sales",href:"/admin/analytics/sales"},{name:"Traffic",href:"/admin/analytics/traffic"},{name:"Reports",href:"/admin/analytics/reports"}]},{name:"Settings",href:"/admin/settings",icon:v.A,current:!1,children:[{name:"General",href:"/admin/settings"},{name:"Payments",href:"/admin/settings/payments"},{name:"Shipping",href:"/admin/settings/shipping"},{name:"Notifications",href:"/admin/settings/notifications"}]}];function I({children:a}){let[b,c]=(0,e.useState)(!1),[f,m]=(0,e.useState)([]),n=(0,h.usePathname)(),{theme:p,setTheme:q}=(0,l.D)(),{language:r,setLanguage:s,t}=(0,k.o)(),u=a=>n===a||n.startsWith(a+"/"),v=({item:a})=>{let b=a.children&&a.children.length>0,c=f.includes(a.href),e=u(a.href);return(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:`group flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors cursor-pointer ${e?"bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"}`,onClick:()=>{var c;return b?(c=a.href,void m(a=>a.includes(c)?a.filter(a=>a!==c):[...a,c])):null},children:[(0,d.jsxs)(g(),{href:b?"#":a.href,className:"flex items-center flex-1",children:[(0,d.jsx)(a.icon,{className:"mr-3 h-5 w-5 flex-shrink-0"}),(0,d.jsx)("span",{children:a.name}),a.badge&&(0,d.jsx)(j.E,{variant:"secondary",className:"ml-auto text-xs",children:a.badge})]}),b&&(0,d.jsx)(w,{className:`h-4 w-4 transition-transform ${c?"rotate-180":""}`})]}),b&&c&&(0,d.jsx)("div",{className:"ml-8 mt-1 space-y-1",children:a.children?.map(a=>(0,d.jsx)(g(),{href:a.href,className:`block px-3 py-2 text-sm rounded-lg transition-colors ${u(a.href)?"bg-primary-50 dark:bg-primary-950 text-primary-600 dark:text-primary-400":"text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800"}`,children:a.name},a.href))})]})};return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[b&&(0,d.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 lg:hidden",onClick:()=>c(!1)}),(0,d.jsx)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform lg:translate-x-0 ${b?"translate-x-0":"-translate-x-full"} lg:static lg:inset-0`,children:(0,d.jsxs)("div",{className:"flex flex-col h-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700",children:[(0,d.jsxs)(g(),{href:"/admin",className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,d.jsx)("span",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Admin"})]}),(0,d.jsx)(i.$,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>c(!1),children:(0,d.jsx)(y.A,{className:"h-5 w-5"})})]}),(0,d.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2 overflow-y-auto",children:H.map(a=>(0,d.jsx)(v,{item:a},a.href))}),(0,d.jsxs)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center",children:(0,d.jsx)(z.A,{className:"h-4 w-4 text-gray-500"})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:"Admin User"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:"<EMAIL>"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>q("dark"===p?"light":"dark"),className:"flex-1",children:"dark"===p?(0,d.jsx)(A.A,{className:"h-4 w-4"}):(0,d.jsx)(B.A,{className:"h-4 w-4"})}),(0,d.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>s("en"===r?"ar":"en"),className:"flex-1",children:(0,d.jsx)(C.A,{className:"h-4 w-4"})}),(0,d.jsx)(i.$,{variant:"ghost",size:"sm",className:"flex-1",children:(0,d.jsx)(D.A,{className:"h-4 w-4"})})]})]})]})}),(0,d.jsxs)("div",{className:"lg:ml-64",children:[(0,d.jsx)("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-full px-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(i.$,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>c(!0),children:(0,d.jsx)(E.A,{className:"h-5 w-5"})}),(0,d.jsxs)("div",{className:"relative hidden md:block",children:[(0,d.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)("input",{type:"text",placeholder:"Search...",className:"pl-10 pr-4 py-2 w-64 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)(i.$,{variant:"ghost",size:"sm",className:"relative",children:[(0,d.jsx)(G.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"3"})]}),(0,d.jsx)("div",{className:"hidden md:flex items-center space-x-2",children:(0,d.jsx)(g(),{href:"/admin/products/new",children:(0,d.jsxs)(i.$,{size:"sm",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})})})]})]})}),(0,d.jsx)("main",{className:"p-6",children:a})]})]})}},96834:(a,b,c)=>{c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},97051:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},99891:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};