exports.id=368,exports.ids=[368],exports.modules={3546:(a,b,c)=>{"use strict";c.d(b,{ToastProvider:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\components\\providers\\toast-provider.tsx","ToastProvider")},4780:(a,b,c)=>{"use strict";c.d(b,{$g:()=>g,Yq:()=>h,cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a,b={}){let{currency:c="USD",notation:d="standard",locale:e="en-US"}=b;return new Intl.NumberFormat(e,{style:"currency",currency:c,notation:d,maximumFractionDigits:2}).format(a)}function h(a,b={},c="en-US"){return new Intl.DateTimeFormat(c,{month:"long",day:"numeric",year:"numeric",...b}).format(new Date(a))}},5388:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\components\\providers\\theme-provider.tsx","ThemeProvider")},7662:(a,b,c)=>{Promise.resolve().then(c.bind(c,10590)),Promise.resolve().then(c.bind(c,88683)),Promise.resolve().then(c.bind(c,38241)),Promise.resolve().then(c.bind(c,5388)),Promise.resolve().then(c.bind(c,3546))},10375:(a,b,c)=>{"use strict";c.d(b,{QueryProvider:()=>h});var d=c(60687),e=c(92314),f=c(8693),g=c(43210);function h({children:a}){let[b]=(0,g.useState)(()=>new e.E({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(a,b)=>!(b?.status>=400&&b?.status<500)&&a<3,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:(a,b)=>!(b?.status>=400&&b?.status<500)&&a<2}}}));return(0,d.jsxs)(f.Ht,{client:b,children:[a,!1]})}},10590:(a,b,c)=>{"use strict";c.d(b,{Header:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\components\\layout\\header.tsx","Header")},15515:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q,metadata:()=>p});var d=c(37413),e=c(51194),f=c.n(e),g=c(62488),h=c.n(g),i=c(5388),j=c(38241),k=c(3546),l=c(88683),m=c(10590);let n={name:"AIDEVCOMMERCE",url:"http://localhost:3000",author:"AIDEVCOMMERCE Team"},o={TITLE_TEMPLATE:"%s | AIDEVCOMMERCE",DESCRIPTION:"Advanced e-commerce platform with AI integration, offering products, services, and production lines.",KEYWORDS:"e-commerce, ai, products, services, production lines, online shopping",OG_IMAGE:"/images/og-image.jpg",TWITTER_HANDLE:"@aidevcommerce"};c(35692);let p={title:{default:n.name,template:o.TITLE_TEMPLATE},description:o.DESCRIPTION,keywords:o.KEYWORDS,authors:[{name:n.author}],creator:n.author,publisher:n.author,formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(n.url),alternates:{canonical:"/",languages:{"en-US":"/en","ar-SA":"/ar"}},openGraph:{type:"website",locale:"en_US",url:n.url,title:n.name,description:o.DESCRIPTION,siteName:n.name,images:[{url:o.OG_IMAGE,width:1200,height:630,alt:n.name}]},twitter:{card:"summary_large_image",title:n.name,description:o.DESCRIPTION,images:[o.OG_IMAGE],creator:o.TWITTER_HANDLE},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"google-site-verification-code",yandex:"yandex-verification-code",yahoo:"yahoo-site-verification-code"}};function q({children:a}){return(0,d.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"}),(0,d.jsx)("link",{rel:"icon",href:"/icon.svg",type:"image/svg+xml"}),(0,d.jsx)("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),(0,d.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,d.jsx)("meta",{name:"theme-color",content:"#a855f7"}),(0,d.jsx)("meta",{name:"color-scheme",content:"light dark"})]}),(0,d.jsx)("body",{className:`${f().variable} ${h().variable} font-sans antialiased`,suppressHydrationWarning:!0,children:(0,d.jsx)(j.QueryProvider,{children:(0,d.jsx)(i.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,d.jsx)(l.LanguageProvider,{children:(0,d.jsxs)(k.ToastProvider,{children:[(0,d.jsx)(m.Header,{}),(0,d.jsx)("main",{className:"min-h-screen",children:a})]})})})})})]})}},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(4780);let i=(0,g.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},29867:(a,b,c)=>{"use strict";c.d(b,{dj:()=>l});var d=c(43210);let e=0,f=new Map,g=a=>{if(f.has(a))return;let b=setTimeout(()=>{f.delete(a),j({type:"REMOVE_TOAST",toastId:a})},1e6);f.set(a,b)},h=[],i={toasts:[]};function j(a){i=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?g(c):a.toasts.forEach(a=>{g(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(i,a),h.forEach(a=>{a(i)})}function k({...a}){let b=(e=(e+1)%Number.MAX_SAFE_INTEGER).toString(),c=()=>j({type:"DISMISS_TOAST",toastId:b});return j({type:"ADD_TOAST",toast:{...a,id:b,open:!0,onOpenChange:a=>{a||c()}}}),{id:b,dismiss:c,update:a=>j({type:"UPDATE_TOAST",toast:{...a,id:b}})}}function l(){let[a,b]=d.useState(i);return d.useEffect(()=>(h.push(b),()=>{let a=h.indexOf(b);a>-1&&h.splice(a,1)}),[a]),{...a,toast:k,dismiss:a=>j({type:"DISMISS_TOAST",toastId:a})}}},35692:()=>{},35862:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>f});var d=c(60687);c(43210);var e=c(10218);function f({children:a,...b}){return(0,d.jsx)(e.N,{...b,children:a})}},38241:(a,b,c)=>{"use strict";c.d(b,{QueryProvider:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\components\\providers\\query-provider.tsx","QueryProvider")},38524:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},70710:(a,b,c)=>{Promise.resolve().then(c.bind(c,74456)),Promise.resolve().then(c.bind(c,98436)),Promise.resolve().then(c.bind(c,10375)),Promise.resolve().then(c.bind(c,35862)),Promise.resolve().then(c.bind(c,98621))},74456:(a,b,c)=>{"use strict";c.d(b,{Header:()=>t});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(16189),i=c(10218),j=c(29523),k=c(98436),l=c(29867),m=c(99270),n=c(21134),o=c(363),p=c(28561),q=c(67760),r=(c(58869),c(19080),c(84027),c(40083),c(11860)),s=c(12941);function t(){let[a,b]=(0,e.useState)(!1),{theme:c,setTheme:f}=(0,i.D)(),{language:t,setLanguage:u,t:v}=(0,k.o)(),{toast:w}=(0,l.dj)();(0,h.useRouter)();let x=()=>{u("en"===t?"ar":"en"),w({title:v("common.success"),description:`Language changed to ${"en"===t?"العربية":"English"}`})},y=()=>{f("dark"===c?"light":"dark")},z=[{name:v("nav.home"),href:"/"},{name:v("nav.shop"),href:"/shop"},{name:v("nav.services"),href:"/services"},{name:v("nav.production-lines"),href:"/production-lines"},{name:v("nav.blog"),href:"/blog"},{name:v("nav.about"),href:"/about"},{name:v("nav.contact"),href:"/contact"}];return(0,d.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsx)(g(),{href:"/",className:"flex items-center space-x-2",children:(0,d.jsx)("div",{className:"text-2xl font-bold text-gradient-primary",children:"AIDEVCOMMERCE"})}),(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:z.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:a.name},a.href))}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(j.$,{variant:"ghost",size:"sm",className:"hidden sm:flex",children:(0,d.jsx)(m.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:y,className:"hidden sm:flex",children:"dark"===c?(0,d.jsx)(n.A,{className:"h-4 w-4"}):(0,d.jsx)(o.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:x,className:"hidden sm:flex text-xs",children:"en"===t?"عربي":"EN"}),(0,d.jsxs)(j.$,{variant:"ghost",size:"sm",className:"relative",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-primary text-xs text-primary-foreground flex items-center justify-center",children:"0"})]}),(0,d.jsxs)(j.$,{variant:"ghost",size:"sm",className:"relative hidden sm:flex",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center",children:"0"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g(),{href:"/auth/login",children:(0,d.jsx)(j.$,{variant:"ghost",size:"sm",children:v("auth.login")})}),(0,d.jsx)(g(),{href:"/auth/register",children:(0,d.jsx)(j.$,{size:"sm",children:v("auth.register")})})]}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",className:"md:hidden",onClick:()=>b(!a),children:a?(0,d.jsx)(r.A,{className:"h-4 w-4"}):(0,d.jsx)(s.A,{className:"h-4 w-4"})})]})]}),a&&(0,d.jsx)("div",{className:"md:hidden border-t py-4",children:(0,d.jsxs)("nav",{className:"flex flex-col space-y-4",children:[z.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",onClick:()=>b(!1),children:a.name},a.href)),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,d.jsxs)(j.$,{variant:"ghost",size:"sm",onClick:y,className:"flex items-center space-x-2",children:["dark"===c?(0,d.jsx)(n.A,{className:"h-4 w-4"}):(0,d.jsx)(o.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"dark"===c?"Light":"Dark"})]}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:x,className:"flex items-center space-x-2",children:(0,d.jsx)("span",{children:"en"===t?"عربي":"English"})})]})]})})]})})}},80380:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},88683:(a,b,c)=>{"use strict";c.d(b,{LanguageProvider:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\components\\providers\\language-provider.tsx","LanguageProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\components\\providers\\language-provider.tsx","useLanguage")},98436:(a,b,c)=>{"use strict";c.d(b,{LanguageProvider:()=>i,o:()=>j});var d=c(60687),e=c(43210);let f={EN:{code:"en",name:"English",dir:"ltr"},AR:{code:"ar",name:"العربية",dir:"rtl"}},g=(0,e.createContext)(void 0),h={en:{"nav.home":"Home","nav.shop":"Shop","nav.services":"Services","nav.production-lines":"Production Lines","nav.blog":"Blog","nav.about":"About","nav.contact":"Contact","auth.login":"Login","auth.register":"Register","auth.logout":"Logout","common.loading":"Loading...","common.error":"Error","common.success":"Success","common.cancel":"Cancel","common.save":"Save","common.edit":"Edit","common.delete":"Delete","common.search":"Search","common.filter":"Filter","common.sort":"Sort","common.view-all":"View All","common.read-more":"Read More","common.add-to-cart":"Add to Cart","common.buy-now":"Buy Now","common.out-of-stock":"Out of Stock","common.in-stock":"In Stock","common.price":"Price","common.quantity":"Quantity","common.total":"Total","common.subtotal":"Subtotal","common.shipping":"Shipping","common.tax":"Tax","common.discount":"Discount"},ar:{"nav.home":"الرئيسية","nav.shop":"المتجر","nav.services":"الخدمات","nav.production-lines":"خطوط الإنتاج","nav.blog":"المدونة","nav.about":"من نحن","nav.contact":"اتصل بنا","auth.login":"تسجيل الدخول","auth.register":"إنشاء حساب","auth.logout":"تسجيل الخروج","common.loading":"جاري التحميل...","common.error":"خطأ","common.success":"نجح","common.cancel":"إلغاء","common.save":"حفظ","common.edit":"تعديل","common.delete":"حذف","common.search":"بحث","common.filter":"تصفية","common.sort":"ترتيب","common.view-all":"عرض الكل","common.read-more":"اقرأ المزيد","common.add-to-cart":"أضف للسلة","common.buy-now":"اشتري الآن","common.out-of-stock":"غير متوفر","common.in-stock":"متوفر","common.price":"السعر","common.quantity":"الكمية","common.total":"المجموع","common.subtotal":"المجموع الفرعي","common.shipping":"الشحن","common.tax":"الضريبة","common.discount":"الخصم"}};function i({children:a}){let[b,c]=(0,e.useState)("en"),[i,j]=(0,e.useState)("ltr");return(0,d.jsx)(g.Provider,{value:{language:b,direction:i,setLanguage:a=>{c(a),j(f[a].dir),document.documentElement.lang=a,document.documentElement.dir=f[a].dir,localStorage.setItem("language",a)},t:(a,c)=>{let d=h[b][a]||a;return c&&Object.entries(c).forEach(([a,b])=>{d=d.replace(`{{${a}}}`,b)}),d}},children:(0,d.jsx)("div",{dir:i,className:"rtl"===i?"font-arabic":"font-sans",children:a})})}function j(){let a=(0,e.useContext)(g);if(void 0===a)throw Error("useLanguage must be used within a LanguageProvider");return a}},98621:(a,b,c)=>{"use strict";c.d(b,{ToastProvider:()=>s});var d=c(60687),e=c(29867),f=c(43210),g=c(32687),h=c(24224),i=c(11860),j=c(4780);let k=g.Kq,l=f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.LM,{ref:c,className:(0,j.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...b}));l.displayName=g.LM.displayName;let m=(0,h.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),n=f.forwardRef(({className:a,variant:b,...c},e)=>(0,d.jsx)(g.bL,{ref:e,className:(0,j.cn)(m({variant:b}),a),...c}));n.displayName=g.bL.displayName,f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.rc,{ref:c,className:(0,j.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...b})).displayName=g.rc.displayName;let o=f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.bm,{ref:c,className:(0,j.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...b,children:(0,d.jsx)(i.A,{className:"h-4 w-4"})}));o.displayName=g.bm.displayName;let p=f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.hE,{ref:c,className:(0,j.cn)("text-sm font-semibold",a),...b}));p.displayName=g.hE.displayName;let q=f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.VY,{ref:c,className:(0,j.cn)("text-sm opacity-90",a),...b}));function r(){let{toasts:a}=(0,e.dj)();return(0,d.jsxs)(k,{children:[a.map(function({id:a,title:b,description:c,action:e,...f}){return(0,d.jsxs)(n,{...f,children:[(0,d.jsxs)("div",{className:"grid gap-1",children:[b&&(0,d.jsx)(p,{children:b}),c&&(0,d.jsx)(q,{children:c})]}),e,(0,d.jsx)(o,{})]},a)}),(0,d.jsx)(l,{})]})}function s({children:a}){return(0,d.jsxs)(d.Fragment,{children:[a,(0,d.jsx)(r,{})]})}q.displayName=g.VY.displayName}};