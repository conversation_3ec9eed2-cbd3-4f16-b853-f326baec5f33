import { NextResponse } from 'next/server'

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aidevcommerce.com'
  
  const robotsTxt = `# Robots.txt for AIDEVCOMMERCE
# Generated automatically

User-agent: *
Allow: /

# Disallow admin and private areas
Disallow: /admin/
Disallow: /account/
Disallow: /api/
Disallow: /_next/
Disallow: /auth/

# Disallow search and filter pages with parameters
Disallow: /shop?*
Disallow: /services?*
Disallow: /blog?*

# Disallow temporary and test pages
Disallow: /test/
Disallow: /temp/
Disallow: /staging/

# Allow specific API endpoints that should be crawled
Allow: /api/sitemap
Allow: /api/robots

# Crawl-delay for different bots
User-agent: Googlebot
Crawl-delay: 1

User-agent: Bingbot
Crawl-delay: 2

User-agent: Slurp
Crawl-delay: 2

User-agent: DuckDuckBot
Crawl-delay: 1

User-agent: Baiduspider
Crawl-delay: 3

User-agent: YandexBot
Crawl-delay: 2

# Block aggressive crawlers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MegaIndex
Disallow: /

# Sitemap location
Sitemap: ${baseUrl}/api/sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Host directive (for Yandex)
Host: ${baseUrl.replace('https://', '').replace('http://', '')}
`

  return new NextResponse(robotsTxt, {
    status: 200,
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
    },
  })
}
