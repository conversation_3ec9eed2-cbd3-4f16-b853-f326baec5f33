"use client"

import { useState } from "react"
import Link from "next/link"
import { AdminLayout } from "@/components/layout/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  BookOpen,
  FileText,
  Calendar,
  User,
  MessageCircle,
  TrendingUp,
  Clock,
  Tag,
  Download,
  Upload,
  Star,
  CheckCircle,
  AlertCircle,
  XCircle,
} from "lucide-react"

// Mock blog posts data for admin
const mockBlogPosts = [
  {
    id: "1",
    title: "The Future of AI in E-commerce: Transforming Online Shopping",
    slug: "future-ai-ecommerce-transforming-online-shopping",
    excerpt: "Explore how artificial intelligence is revolutionizing the e-commerce landscape...",
    content: "Full article content here...",
    status: "published",
    featured: true,
    category: "Technology",
    author: {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/author-1.jpg"
    },
    publishedAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-16"),
    views: 1250,
    comments: 23,
    likes: 89,
    readTime: "8 min read",
    tags: ["AI", "E-commerce", "Machine Learning", "Technology"],
    seoTitle: "AI in E-commerce: Future Trends and Transformations",
    seoDescription: "Discover how AI is transforming e-commerce with personalized recommendations...",
    image: "/blog-1.jpg"
  },
  {
    id: "2",
    title: "Sustainable Manufacturing: Green Production Lines for the Future",
    slug: "sustainable-manufacturing-green-production-lines",
    excerpt: "Discover how modern production lines are becoming more environmentally friendly...",
    content: "Full article content here...",
    status: "published",
    featured: false,
    category: "Manufacturing",
    author: {
      id: "2",
      name: "Michael Chen",
      email: "<EMAIL>",
      avatar: "/author-2.jpg"
    },
    publishedAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-13"),
    views: 890,
    comments: 15,
    likes: 67,
    readTime: "6 min read",
    tags: ["Sustainability", "Manufacturing", "Green Technology", "Environment"],
    seoTitle: "Sustainable Manufacturing: Green Production Lines",
    seoDescription: "Learn about eco-friendly manufacturing processes and green production lines...",
    image: "/blog-2.jpg"
  },
  {
    id: "3",
    title: "Digital Transformation in Small Businesses: A Complete Guide",
    slug: "digital-transformation-small-businesses-guide",
    excerpt: "Learn how small businesses can successfully navigate digital transformation...",
    content: "Full article content here...",
    status: "draft",
    featured: true,
    category: "Business",
    author: {
      id: "3",
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      avatar: "/author-3.jpg"
    },
    publishedAt: null,
    updatedAt: new Date("2024-01-20"),
    views: 0,
    comments: 0,
    likes: 0,
    readTime: "10 min read",
    tags: ["Digital Transformation", "Small Business", "Strategy", "Technology"],
    seoTitle: "Digital Transformation Guide for Small Businesses",
    seoDescription: "Complete guide to digital transformation for small businesses...",
    image: "/blog-3.jpg"
  },
  {
    id: "4",
    title: "UX Design Trends 2024: What's Shaping User Experience",
    slug: "ux-design-trends-2024-user-experience",
    excerpt: "Stay ahead of the curve with the latest UX design trends...",
    content: "Full article content here...",
    status: "scheduled",
    featured: false,
    category: "Design",
    author: {
      id: "4",
      name: "Alex Thompson",
      email: "<EMAIL>",
      avatar: "/author-4.jpg"
    },
    publishedAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-18"),
    views: 0,
    comments: 0,
    likes: 0,
    readTime: "7 min read",
    tags: ["UX Design", "Design Trends", "User Experience", "Interface"],
    seoTitle: "UX Design Trends 2024: User Experience Evolution",
    seoDescription: "Discover the latest UX design trends shaping user experience in 2024...",
    image: "/blog-4.jpg"
  },
  {
    id: "5",
    title: "Cloud Infrastructure Best Practices for Scalable Applications",
    slug: "cloud-infrastructure-best-practices-scalable-applications",
    excerpt: "Master the fundamentals of cloud infrastructure design...",
    content: "Full article content here...",
    status: "archived",
    featured: false,
    category: "Technology",
    author: {
      id: "5",
      name: "David Kim",
      email: "<EMAIL>",
      avatar: "/author-5.jpg"
    },
    publishedAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-10"),
    views: 980,
    comments: 19,
    likes: 45,
    readTime: "12 min read",
    tags: ["Cloud Computing", "Infrastructure", "Scalability", "DevOps"],
    seoTitle: "Cloud Infrastructure Best Practices Guide",
    seoDescription: "Learn cloud infrastructure best practices for building scalable applications...",
    image: "/blog-5.jpg"
  }
]

const categories = ["All Categories", "Technology", "Manufacturing", "Business", "Design", "Marketing"]
const statuses = ["All Status", "published", "draft", "scheduled", "archived"]
const authors = ["All Authors", "Sarah Johnson", "Michael Chen", "Emily Rodriguez", "Alex Thompson", "David Kim"]

const getStatusColor = (status: string) => {
  switch (status) {
    case "published":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    case "draft":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    case "scheduled":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    case "archived":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "published":
      return CheckCircle
    case "draft":
      return FileText
    case "scheduled":
      return Clock
    case "archived":
      return XCircle
    default:
      return AlertCircle
  }
}

export default function BlogManagementPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedStatus, setSelectedStatus] = useState("All Status")
  const [selectedAuthor, setSelectedAuthor] = useState("All Authors")
  const [selectedPosts, setSelectedPosts] = useState<string[]>([])
  
  const { t } = useLanguage()
  const { toast } = useToast()

  const filteredPosts = mockBlogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === "All Categories" || post.category === selectedCategory
    const matchesStatus = selectedStatus === "All Status" || post.status === selectedStatus
    const matchesAuthor = selectedAuthor === "All Authors" || post.author.name === selectedAuthor
    
    return matchesSearch && matchesCategory && matchesStatus && matchesAuthor
  })

  const handleSelectPost = (postId: string) => {
    setSelectedPosts(prev => 
      prev.includes(postId)
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    )
  }

  const handleSelectAll = () => {
    setSelectedPosts(
      selectedPosts.length === filteredPosts.length 
        ? [] 
        : filteredPosts.map(p => p.id)
    )
  }

  const handleBulkAction = (action: string) => {
    toast({
      title: "Bulk Action",
      description: `${action} applied to ${selectedPosts.length} posts`,
    })
    setSelectedPosts([])
  }

  const handleDeletePost = (postId: string) => {
    toast({
      title: "Post Deleted",
      description: "Blog post has been successfully deleted",
    })
  }

  const handleUpdateStatus = (postId: string, newStatus: string) => {
    toast({
      title: "Status Updated",
      description: `Post status updated to ${newStatus}`,
    })
  }

  const BlogPostCard = ({ post }: { post: typeof mockBlogPosts[0] }) => {
    const StatusIcon = getStatusIcon(post.status)

    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              <input
                type="checkbox"
                checked={selectedPosts.includes(post.id)}
                onChange={() => handleSelectPost(post.id)}
                className="mt-1"
              />
              
              <div className="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                <BookOpen className="h-8 w-8 text-gray-400" />
              </div>
              
              <div className="flex-1 min-w-0">
                {/* Post Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-lg line-clamp-2">
                        {post.title}
                      </h3>
                      {post.featured && (
                        <Badge variant="secondary" className="text-xs">Featured</Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                      {post.excerpt}
                    </p>
                  </div>
                  
                  <div className="text-right ml-4">
                    <Badge className={`flex items-center space-x-1 ${getStatusColor(post.status)}`}>
                      <StatusIcon className="h-3 w-3" />
                      <span className="capitalize">{post.status}</span>
                    </Badge>
                  </div>
                </div>

                {/* Meta Information */}
                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>{post.author.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Tag className="h-4 w-4" />
                    <span>{post.category}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{post.readTime}</span>
                  </div>
                  {post.publishedAt && (
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(post.publishedAt)}</span>
                    </div>
                  )}
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {post.tags.slice(0, 4).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {post.tags.length > 4 && (
                    <Badge variant="outline" className="text-xs">
                      +{post.tags.length - 4}
                    </Badge>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{post.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageCircle className="h-4 w-4" />
                    <span>{post.comments}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4" />
                    <span>{post.likes}</span>
                  </div>
                </div>

                {/* SEO Info */}
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-sm font-medium mb-1">SEO Title:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">
                    {post.seoTitle}
                  </p>
                  <p className="text-sm font-medium mb-1 mt-2">SEO Description:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                    {post.seoDescription}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Link href={`/blog/${post.slug}`} target="_blank">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                    </Link>
                    <Link href={`/admin/blog/${post.id}/edit`}>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeletePost(post.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <select
                      value={post.status}
                      onChange={(e) => handleUpdateStatus(post.id, e.target.value)}
                      className="px-2 py-1 text-sm border rounded"
                    >
                      <option value="draft">Draft</option>
                      <option value="scheduled">Scheduled</option>
                      <option value="published">Published</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Blog Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage blog posts, categories, and content
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Link href="/admin/blog/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Post
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Posts
                  </p>
                  <p className="text-2xl font-bold">
                    {mockBlogPosts.length}
                  </p>
                </div>
                <BookOpen className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Published
                  </p>
                  <p className="text-2xl font-bold">
                    {mockBlogPosts.filter(p => p.status === 'published').length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Drafts
                  </p>
                  <p className="text-2xl font-bold">
                    {mockBlogPosts.filter(p => p.status === 'draft').length}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Views
                  </p>
                  <p className="text-2xl font-bold">
                    {mockBlogPosts.reduce((sum, post) => sum + post.views, 0).toLocaleString()}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search posts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {statuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedAuthor}
                  onChange={(e) => setSelectedAuthor(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {authors.map((author) => (
                    <option key={author} value={author}>
                      {author}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedPosts.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">
                    {selectedPosts.length} posts selected
                  </span>
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {selectedPosts.length === filteredPosts.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Publish')}>
                    Publish
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Archive')}>
                    Archive
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Delete')} className="text-red-600">
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Posts List */}
        <div className="space-y-4">
          {filteredPosts.map((post) => (
            <BlogPostCard key={post.id} post={post} />
          ))}
        </div>

        {/* Empty State */}
        {filteredPosts.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No posts found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search or filter criteria
              </p>
              <Button onClick={() => {
                setSearchQuery("")
                setSelectedCategory("All Categories")
                setSelectedStatus("All Status")
                setSelectedAuthor("All Authors")
              }}>
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {filteredPosts.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredPosts.length} of {mockBlogPosts.length} posts
            </p>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" className="bg-primary-50 dark:bg-primary-900">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
