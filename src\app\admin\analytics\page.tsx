"use client"

import { useState } from "react"
import { AdminLayout } from "@/components/layout/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Eye,
  MousePointer,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus,
  Target,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
} from "lucide-react"

// Mock analytics data
const analyticsData = {
  overview: {
    revenue: {
      current: 125430.50,
      previous: 98750.25,
      change: 27.0
    },
    orders: {
      current: 1247,
      previous: 1089,
      change: 14.5
    },
    customers: {
      current: 892,
      previous: 756,
      change: 18.0
    },
    products: {
      current: 156,
      previous: 142,
      change: 9.9
    }
  },
  traffic: {
    pageViews: 45230,
    uniqueVisitors: 12450,
    bounceRate: 32.5,
    avgSessionDuration: "3m 24s",
    conversionRate: 2.8
  },
  topProducts: [
    { id: "1", name: "Premium Wireless Headphones", sales: 234, revenue: 46800, growth: 15.2 },
    { id: "2", name: "Smart Fitness Watch", sales: 189, revenue: 56700, growth: 8.7 },
    { id: "3", name: "Wireless Charging Pad", sales: 298, revenue: 14900, growth: -3.2 },
    { id: "4", name: "Ergonomic Office Chair", sales: 156, revenue: 70200, growth: 22.1 },
    { id: "5", name: "Professional Camera Lens", sales: 67, revenue: 60300, growth: 5.4 }
  ],
  salesByCategory: [
    { category: "Electronics", sales: 45230, percentage: 42.3 },
    { category: "Furniture", sales: 28450, percentage: 26.6 },
    { category: "Accessories", sales: 18920, percentage: 17.7 },
    { category: "Photography", sales: 9870, percentage: 9.2 },
    { category: "Gaming", sales: 4530, percentage: 4.2 }
  ],
  deviceBreakdown: [
    { device: "Desktop", users: 6890, percentage: 55.3 },
    { device: "Mobile", users: 4120, percentage: 33.1 },
    { device: "Tablet", users: 1440, percentage: 11.6 }
  ],
  recentActivity: [
    { type: "order", description: "New order #2024-156", time: "2 minutes ago", value: "$299.99" },
    { type: "user", description: "New user registration", time: "5 minutes ago", value: "<EMAIL>" },
    { type: "product", description: "Product viewed 50+ times", time: "12 minutes ago", value: "Smart Watch" },
    { type: "order", description: "Order completed", time: "18 minutes ago", value: "$149.99" },
    { type: "review", description: "New 5-star review", time: "25 minutes ago", value: "Headphones" }
  ]
}

const timeRanges = [
  { label: "Last 7 days", value: "7d" },
  { label: "Last 30 days", value: "30d" },
  { label: "Last 90 days", value: "90d" },
  { label: "Last year", value: "1y" }
]

export default function AnalyticsPage() {
  const [selectedTimeRange, setSelectedTimeRange] = useState("30d")
  const { t } = useLanguage()

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUp className="h-4 w-4 text-green-500" />
    if (change < 0) return <ArrowDown className="h-4 w-4 text-red-500" />
    return <Minus className="h-4 w-4 text-gray-500" />
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return "text-green-600 dark:text-green-400"
    if (change < 0) return "text-red-600 dark:text-red-400"
    return "text-gray-600 dark:text-gray-400"
  }

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case "Desktop": return Monitor
      case "Mobile": return Smartphone
      case "Tablet": return Tablet
      default: return Monitor
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "order": return ShoppingCart
      case "user": return Users
      case "product": return Package
      case "review": return Eye
      default: return Target
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive business insights and performance metrics
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
            >
              {timeRanges.map((range) => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Revenue
                  </p>
                  <p className="text-2xl font-bold">
                    {formatPrice(analyticsData.overview.revenue.current)}
                  </p>
                  <div className={`flex items-center space-x-1 text-sm ${getChangeColor(analyticsData.overview.revenue.change)}`}>
                    {getChangeIcon(analyticsData.overview.revenue.change)}
                    <span>{Math.abs(analyticsData.overview.revenue.change)}%</span>
                    <span className="text-gray-500">vs last period</span>
                  </div>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Orders
                  </p>
                  <p className="text-2xl font-bold">
                    {analyticsData.overview.orders.current.toLocaleString()}
                  </p>
                  <div className={`flex items-center space-x-1 text-sm ${getChangeColor(analyticsData.overview.orders.change)}`}>
                    {getChangeIcon(analyticsData.overview.orders.change)}
                    <span>{Math.abs(analyticsData.overview.orders.change)}%</span>
                    <span className="text-gray-500">vs last period</span>
                  </div>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    New Customers
                  </p>
                  <p className="text-2xl font-bold">
                    {analyticsData.overview.customers.current.toLocaleString()}
                  </p>
                  <div className={`flex items-center space-x-1 text-sm ${getChangeColor(analyticsData.overview.customers.change)}`}>
                    {getChangeIcon(analyticsData.overview.customers.change)}
                    <span>{Math.abs(analyticsData.overview.customers.change)}%</span>
                    <span className="text-gray-500">vs last period</span>
                  </div>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active Products
                  </p>
                  <p className="text-2xl font-bold">
                    {analyticsData.overview.products.current}
                  </p>
                  <div className={`flex items-center space-x-1 text-sm ${getChangeColor(analyticsData.overview.products.change)}`}>
                    {getChangeIcon(analyticsData.overview.products.change)}
                    <span>{Math.abs(analyticsData.overview.products.change)}%</span>
                    <span className="text-gray-500">vs last period</span>
                  </div>
                </div>
                <Package className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Traffic Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Website Traffic
              </CardTitle>
              <CardDescription>
                Visitor analytics and engagement metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">
                    {analyticsData.traffic.pageViews.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Page Views</p>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {analyticsData.traffic.uniqueVisitors.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Unique Visitors</p>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-2xl font-bold text-orange-600">
                    {analyticsData.traffic.bounceRate}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Bounce Rate</p>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">
                    {analyticsData.traffic.conversionRate}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Conversion Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                Device Breakdown
              </CardTitle>
              <CardDescription>
                Traffic distribution by device type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.deviceBreakdown.map((device) => {
                  const DeviceIcon = getDeviceIcon(device.device)
                  return (
                    <div key={device.device} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <DeviceIcon className="h-5 w-5 text-gray-500" />
                        <span className="font-medium">{device.device}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full" 
                            style={{ width: `${device.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {device.percentage}%
                        </span>
                        <span className="text-sm text-gray-500 w-16 text-right">
                          {device.users.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Products and Sales by Category */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Top Products
              </CardTitle>
              <CardDescription>
                Best performing products by sales and revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium line-clamp-1">{product.name}</p>
                        <p className="text-sm text-gray-500">
                          {product.sales} sales • {formatPrice(product.revenue)}
                        </p>
                      </div>
                    </div>
                    <div className={`flex items-center space-x-1 text-sm ${getChangeColor(product.growth)}`}>
                      {getChangeIcon(product.growth)}
                      <span>{Math.abs(product.growth)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Sales by Category
              </CardTitle>
              <CardDescription>
                Revenue distribution across product categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.salesByCategory.map((category) => (
                  <div key={category.category} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium">{category.category}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full" 
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium w-12 text-right">
                        {category.percentage}%
                      </span>
                      <span className="text-sm text-gray-500 w-20 text-right">
                        {formatPrice(category.sales)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest events and activities across your platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.recentActivity.map((activity, index) => {
                const ActivityIcon = getActivityIcon(activity.type)
                return (
                  <div key={index} className="flex items-center justify-between p-3 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20">
                    <div className="flex items-center space-x-3">
                      <ActivityIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      <div>
                        <p className="font-medium">{activity.description}</p>
                        <p className="text-sm text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                    <Badge variant="secondary">
                      {activity.value}
                    </Badge>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
