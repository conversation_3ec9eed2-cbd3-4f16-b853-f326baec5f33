(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[895],{1976:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},2523:(e,a,s)=>{"use strict";s.d(a,{p:()=>l});var r=s(5155),t=s(2115),i=s(9434);let l=t.forwardRef((e,a)=>{let{className:s,type:t,...l}=e;return(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...l})});l.displayName="Input"},2877:(e,a,s)=>{Promise.resolve().then(s.bind(s,3795))},3795:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>w});var r=s(5155),t=s(2115),i=s(6874),l=s.n(i),c=s(285),n=s(2523),d=s(6695),o=s(4817),m=s(7481),x=s(9434),h=s(1976),u=s(8564),p=s(7809),g=s(7924),y=s(9946);let f=(0,y.A)("sliders-horizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]),j=(0,y.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),v=(0,y.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),b=[{id:"1",name:"Premium Wireless Headphones",slug:"premium-wireless-headphones",price:199.99,comparePrice:249.99,rating:4.8,reviewCount:124,image:"/placeholder-product.jpg",category:"Electronics",inStock:!0,featured:!0,description:"High-quality wireless headphones with noise cancellation"},{id:"2",name:"Smart Fitness Watch",slug:"smart-fitness-watch",price:299.99,comparePrice:null,rating:4.6,reviewCount:89,image:"/placeholder-product.jpg",category:"Electronics",inStock:!0,featured:!1,description:"Advanced fitness tracking with heart rate monitoring"},{id:"3",name:"Organic Cotton T-Shirt",slug:"organic-cotton-t-shirt",price:29.99,comparePrice:39.99,rating:4.4,reviewCount:67,image:"/placeholder-product.jpg",category:"Clothing",inStock:!0,featured:!1,description:"Comfortable organic cotton t-shirt in various colors"},{id:"4",name:"Professional Camera Lens",slug:"professional-camera-lens",price:899.99,comparePrice:null,rating:4.9,reviewCount:45,image:"/placeholder-product.jpg",category:"Photography",inStock:!1,featured:!0,description:"Professional grade camera lens for stunning photography"},{id:"5",name:"Ergonomic Office Chair",slug:"ergonomic-office-chair",price:449.99,comparePrice:599.99,rating:4.7,reviewCount:156,image:"/placeholder-product.jpg",category:"Furniture",inStock:!0,featured:!1,description:"Comfortable ergonomic chair for long work sessions"},{id:"6",name:"Wireless Charging Pad",slug:"wireless-charging-pad",price:49.99,comparePrice:null,rating:4.3,reviewCount:203,image:"/placeholder-product.jpg",category:"Electronics",inStock:!0,featured:!1,description:"Fast wireless charging for compatible devices"}],N=["All Categories","Electronics","Clothing","Photography","Furniture","Home & Garden","Sports & Outdoors"],k=[{value:"featured",label:"Featured"},{value:"price-low",label:"Price: Low to High"},{value:"price-high",label:"Price: High to Low"},{value:"rating",label:"Highest Rated"},{value:"newest",label:"Newest"}];function w(){let[e,a]=(0,t.useState)(""),[s,i]=(0,t.useState)("All Categories"),[y,w]=(0,t.useState)("featured"),[C,S]=(0,t.useState)("grid"),[A,P]=(0,t.useState)(!1),[M,z]=(0,t.useState)({min:0,max:1e3}),[R,D]=(0,t.useState)(0),{t:L}=(0,o.o)(),{toast:$}=(0,m.dj)(),E=b.filter(a=>{let r=a.name.toLowerCase().includes(e.toLowerCase()),t="All Categories"===s||a.category===s,i=a.price>=M.min&&a.price<=M.max,l=0===R||a.rating>=R;return r&&t&&i&&l}),F=e=>{let{product:a}=e;return(0,r.jsxs)(d.Zp,{className:"group hover:shadow-lg transition-all duration-300",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-gray-400 text-4xl",children:"\uD83D\uDCE6"})}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2",children:[(0,r.jsx)(c.$,{size:"sm",variant:"secondary",onClick:()=>{$({title:"Added to Wishlist",description:"".concat(a.name," has been added to your wishlist")})},children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(l(),{href:"/product/".concat(a.slug),children:(0,r.jsx)(c.$,{size:"sm",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"absolute top-2 left-2 space-y-1",children:[a.featured&&(0,r.jsx)("span",{className:"bg-primary-500 text-white px-2 py-1 text-xs rounded",children:"Featured"}),a.comparePrice&&(0,r.jsx)("span",{className:"bg-red-500 text-white px-2 py-1 text-xs rounded",children:"Sale"}),!a.inStock&&(0,r.jsx)("span",{className:"bg-gray-500 text-white px-2 py-1 text-xs rounded",children:"Out of Stock"})]})]}),(0,r.jsx)(d.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm line-clamp-2",children:(0,r.jsx)(l(),{href:"/product/".concat(a.slug),className:"hover:text-primary-600 transition-colors",children:a.name})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2",children:a.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsx)(u.A,{className:"h-3 w-3 ".concat(s<Math.floor(a.rating)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[a.rating," (",a.reviewCount,")"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"font-bold text-lg",children:(0,x.$g)(a.price)}),a.comparePrice&&(0,r.jsx)("span",{className:"text-sm text-gray-500 line-through",children:(0,x.$g)(a.comparePrice)})]}),(0,r.jsxs)(c.$,{className:"w-full",size:"sm",onClick:()=>{$({title:"Added to Cart",description:"".concat(a.name," has been added to your cart")})},disabled:!a.inStock,children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),a.inStock?"Add to Cart":"Out of Stock"]})]})})]})};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Shop"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Discover our amazing products"})]}),(0,r.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,r.jsx)(n.p,{placeholder:"Search products...",value:e,onChange:e=>a(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,r.jsx)("select",{value:s,onChange:e=>i(e.target.value),className:"px-3 py-2 border rounded-md bg-background",children:N.map(e=>(0,r.jsx)("option",{value:e,children:e},e))}),(0,r.jsx)("select",{value:y,onChange:e=>w(e.target.value),className:"px-3 py-2 border rounded-md bg-background",children:k.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,r.jsxs)(c.$,{variant:"outline",onClick:()=>P(!A),children:[(0,r.jsx)(f,{className:"h-4 w-4 mr-2"}),"Filters"]}),(0,r.jsxs)("div",{className:"flex border rounded-md",children:[(0,r.jsx)(c.$,{variant:"grid"===C?"default":"ghost",size:"sm",onClick:()=>S("grid"),className:"rounded-r-none",children:(0,r.jsx)(j,{className:"h-4 w-4"})}),(0,r.jsx)(c.$,{variant:"list"===C?"default":"ghost",size:"sm",onClick:()=>S("list"),className:"rounded-l-none",children:(0,r.jsx)(v,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"ml-auto text-sm text-gray-500",children:[E.length," products found"]})]}),A&&(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Price Range"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.p,{type:"number",placeholder:"Min",value:M.min,onChange:e=>z(a=>({...a,min:Number(e.target.value)}))}),(0,r.jsx)("span",{children:"-"}),(0,r.jsx)(n.p,{type:"number",placeholder:"Max",value:M.max,onChange:e=>z(a=>({...a,max:Number(e.target.value)}))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Minimum Rating"}),(0,r.jsxs)("select",{value:R,onChange:e=>D(Number(e.target.value)),className:"w-full px-3 py-2 border rounded-md bg-background",children:[(0,r.jsx)("option",{value:0,children:"Any Rating"}),(0,r.jsx)("option",{value:4,children:"4+ Stars"}),(0,r.jsx)("option",{value:4.5,children:"4.5+ Stars"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Availability"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"mr-2",defaultChecked:!0}),"In Stock"]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"mr-2"}),"Out of Stock"]})]})]})]})})})]}),(0,r.jsx)("div",{className:"grid gap-6 ".concat("grid"===C?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4":"grid-cols-1"),children:E.map(e=>(0,r.jsx)(F,{product:e},e.id))}),0===E.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"No products found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Try adjusting your search or filter criteria"}),(0,r.jsx)(c.$,{onClick:()=>{a(""),i("All Categories"),z({min:0,max:1e3}),D(0)},children:"Clear Filters"})]}),E.length>0&&(0,r.jsx)("div",{className:"text-center mt-12",children:(0,r.jsx)(c.$,{variant:"outline",size:"lg",children:"Load More Products"})})]})})}},6695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>l,aR:()=>c,wL:()=>m});var r=s(5155),t=s(2115),i=s(9434);let l=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...t})});l.displayName="Card";let c=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...t})});c.displayName="CardHeader";let n=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",s),...t})});n.displayName="CardTitle";let d=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",s),...t})});d.displayName="CardDescription";let o=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",s),...t})});o.displayName="CardContent";let m=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",s),...t})});m.displayName="CardFooter"},7809:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},7924:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8564:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])}},e=>{e.O(0,[455,874,197,441,964,358],()=>e(e.s=2877)),_N_E=e.O()}]);