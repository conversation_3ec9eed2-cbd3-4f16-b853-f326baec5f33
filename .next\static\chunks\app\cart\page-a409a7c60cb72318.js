(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{2843:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(5155),r=t(2115),i=t(6874),l=t.n(i),n=t(285),c=t(6695),d=t(2921),x=t(4817),m=t(7481),o=t(9434),h=t(6151),u=t(5169),p=t(2525),j=t(7712),g=t(4616),N=t(1976),f=t(9799),v=t(5525),y=t(3904),b=t(3332),w=t(2138);function k(){let{items:e,updateQuantity:s,removeItem:t,clearCart:i,subtotal:k,tax:C,shipping:A,total:$}=(0,d.x)(),{t:q}=(0,x.o)(),{toast:S}=(0,m.dj)(),[I,R]=(0,r.useState)(!1),O=async()=>{R(!0),confirm("Are you sure you want to clear your cart?")&&(i(),S({title:"Cart Cleared",description:"All items have been removed from your cart"})),R(!1)};return 0===e.length?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,a.jsx)(h.A,{className:"h-24 w-24 text-gray-400 mx-auto mb-6"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:"Looks like you haven't added any items to your cart yet. Start shopping to fill it up!"}),(0,a.jsx)(l(),{href:"/shop",children:(0,a.jsxs)(n.$,{size:"lg",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Continue Shopping"]})})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Shopping Cart"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:[e.length," ",1===e.length?"item":"items"," in your cart"]})]}),(0,a.jsxs)(l(),{href:"/shop",className:"flex items-center text-primary-600 hover:text-primary-700",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Continue Shopping"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:O,disabled:I,className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Clear Cart"]})}),e.map(e=>{var r,i;return(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"h-24 w-24 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("div",{className:"text-3xl",children:"\uD83D\uDCE6"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)(l(),{href:"/product/".concat(e.slug),className:"font-semibold text-lg hover:text-primary-600 transition-colors line-clamp-2",children:e.name}),e.variant&&(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:["Variant: ",e.variant.name]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,a.jsx)("span",{className:"font-bold text-lg",children:(0,o.$g)((null==(r=e.variant)?void 0:r.price)||e.price)}),!e.inStock&&(0,a.jsx)("span",{className:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-xs",children:"Out of Stock"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center border rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>s(e.id,e.quantity-1),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",disabled:e.quantity<=1,children:(0,a.jsx)(j.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"px-4 py-2 border-x min-w-[60px] text-center",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>s(e.id,e.quantity+1),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",disabled:e.quantity>=e.maxQuantity,children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:()=>{t(e.id),S({title:"Moved to Wishlist",description:"Item has been moved to your wishlist"})},children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:()=>t(e.id),className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Remove"]})]})]}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Item Total:"}),(0,a.jsx)("span",{className:"font-semibold",children:(0,o.$g)(((null==(i=e.variant)?void 0:i.price)||e.price)*e.quantity)})]})})]})]})})},e.id)})]}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(c.Zp,{className:"sticky top-8",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Order Summary"}),(0,a.jsx)(c.BT,{children:"Review your order before checkout"})]}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsx)("span",{children:(0,o.$g)(k)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Tax"}),(0,a.jsx)("span",{children:(0,o.$g)(C)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{children:0===A?(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"Free"}):(0,o.$g)(A)})]}),(0,a.jsx)("div",{className:"border-t pt-3",children:(0,a.jsxs)("div",{className:"flex justify-between font-semibold text-lg",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsx)("span",{children:(0,o.$g)($)})]})})]}),A>0&&(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,a.jsx)("div",{className:"flex items-center justify-between text-sm mb-2",children:(0,a.jsxs)("span",{className:"text-blue-700 dark:text-blue-300",children:["Add ",(0,o.$g)(100-k)," for free shipping"]})}),(0,a.jsx)("div",{className:"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(k/100*100,100),"%")}})})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-600",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Free shipping on orders over $100"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Secure checkout guaranteed"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-purple-600",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"30-day return policy"})]})]}),(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",placeholder:"Promo code",className:"flex-1 px-3 py-2 border rounded-md text-sm"}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"Apply"]})]})}),(0,a.jsx)(l(),{href:"/checkout",className:"block",children:(0,a.jsxs)(n.$,{className:"w-full",size:"lg",children:["Proceed to Checkout",(0,a.jsx)(w.A,{className:"h-5 w-5 ml-2"})]})}),(0,a.jsx)(l(),{href:"/shop",className:"block",children:(0,a.jsx)(n.$,{variant:"outline",className:"w-full",children:"Continue Shopping"})})]})]})})]})]})})}},2921:(e,s,t)=>{"use strict";t.d(s,{x:()=>i});var a=t(5453),r=t(6786);let i=(0,a.v)()((0,r.Zr)((e,s)=>({items:[],isOpen:!1,addItem:t=>{let a=s().items,r=a.findIndex(e=>{var s,a;return e.productId===t.productId&&(null==(s=e.variant)?void 0:s.id)===(null==(a=t.variant)?void 0:a.id)});if(r>-1){let s=a[r],i=s.quantity+(t.quantity||1),l=Math.min(t.maxQuantity,s.maxQuantity);e({items:a.map((e,s)=>s===r?{...e,quantity:Math.min(i,l)}:e)})}else{var i;e({items:[...a,{...t,id:"".concat(t.productId,"-").concat((null==(i=t.variant)?void 0:i.id)||"default","-").concat(Date.now()),quantity:t.quantity||1}]})}},removeItem:t=>{e({items:s().items.filter(e=>e.id!==t)})},updateQuantity:(t,a)=>{if(a<=0)return void s().removeItem(t);e({items:s().items.map(e=>e.id===t?{...e,quantity:Math.min(a,e.maxQuantity)}:e)})},clearCart:()=>{e({items:[]})},toggleCart:()=>{e({isOpen:!s().isOpen})},openCart:()=>{e({isOpen:!0})},closeCart:()=>{e({isOpen:!1})},get totalItems(){return s().items.reduce((e,s)=>e+s.quantity,0)},get subtotal(){return s().items.reduce((e,s)=>{var t;return e+((null==(t=s.variant)?void 0:t.price)||s.price)*s.quantity},0)},get tax(){return .08*s().subtotal},get shipping(){return s().subtotal>=100?0:10},get total(){return s().subtotal+s().tax+s().shipping}}),{name:"cart-storage",partialize:e=>({items:e.items})}))},6036:(e,s,t)=>{Promise.resolve().then(t.bind(t,2843))},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>x,ZB:()=>c,Zp:()=>l,aR:()=>n,wL:()=>m});var a=t(5155),r=t(2115),i=t(9434);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});l.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...r})});n.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",t),...r})});x.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"}},e=>{e.O(0,[3274,8644,9197,8441,5964,7358],()=>e(e.s=6036)),_N_E=e.O()}]);