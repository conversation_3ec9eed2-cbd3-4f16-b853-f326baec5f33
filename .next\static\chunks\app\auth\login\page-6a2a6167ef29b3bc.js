(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(5155),r=t(2115),l=t(9434);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4177:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(5155),r=t(2115),l=t(6874),i=t.n(l),c=t(5695),d=t(2177),o=t(221),n=t(8309),m=t(285),u=t(2523),x=t(5057),h=t(6695),p=t(7481),f=t(4817),g=t(5169),y=t(8883),j=t(2919),v=t(8749),w=t(2657);let N=n.Ik({email:n.Yj().email("Invalid email address"),password:n.Yj().min(8,"Password must be at least 8 characters")});function b(){let[e,s]=(0,r.useState)(!1),[t,l]=(0,r.useState)(!1),n=(0,c.useRouter)(),{toast:b}=(0,p.dj)(),{t:k}=(0,f.o)(),{register:A,handleSubmit:C,formState:{errors:R}}=(0,d.mN)({resolver:(0,o.u)(N)}),F=async e=>{l(!0);try{console.log("Login attempt:",e),b({title:k("common.success"),description:"Login functionality will be implemented soon"}),await new Promise(e=>setTimeout(e,1e3)),n.push("/")}catch(e){b({title:k("common.error"),description:"An error occurred during login",variant:"destructive"})}finally{l(!1)}},M=async e=>{l(!0);try{console.log("Social login attempt:",e),b({title:k("common.success"),description:"".concat(e," login will be implemented soon")})}catch(s){b({title:k("common.error"),description:"Failed to login with ".concat(e),variant:"destructive"})}finally{l(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(i(),{href:"/",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),(0,a.jsxs)(h.Zp,{className:"w-full",children:[(0,a.jsxs)(h.aR,{className:"space-y-1",children:[(0,a.jsx)(h.ZB,{className:"text-2xl text-center",children:k("auth.login")}),(0,a.jsx)(h.BT,{className:"text-center",children:"Enter your email and password to access your account"})]}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("form",{onSubmit:C(F),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"email",children:"Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...A("email")})]}),R.email&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:R.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"password",type:e?"text":"password",placeholder:"Enter your password",className:"pl-10 pr-10",...A("password")}),(0,a.jsx)("button",{type:"button",onClick:()=>s(!e),className:"absolute right-3 top-3 text-muted-foreground hover:text-foreground",children:e?(0,a.jsx)(v.A,{className:"h-4 w-4"}):(0,a.jsx)(w.A,{className:"h-4 w-4"})})]}),R.password&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:R.password.message})]}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)(i(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})}),(0,a.jsx)(m.$,{type:"submit",className:"w-full",disabled:t,children:t?"Signing in...":k("auth.login")})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("span",{className:"w-full border-t"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,a.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)(m.$,{variant:"outline",onClick:()=>M("google"),disabled:t,children:[(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,a.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,a.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,a.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google"]}),(0,a.jsxs)(m.$,{variant:"outline",onClick:()=>M("facebook"),disabled:t,children:[(0,a.jsx)("svg",{className:"mr-2 h-4 w-4",fill:"#1877F2",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Facebook"]})]})]}),(0,a.jsx)(h.wL,{children:(0,a.jsxs)("p",{className:"text-center text-sm text-muted-foreground w-full",children:["Don't have an account?"," ",(0,a.jsx)(i(),{href:"/auth/register",className:"text-primary hover:underline",children:k("auth.register")})]})})]})]})})}},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(5155),r=t(2115),l=t(968),i=t(2085),c=t(9434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,c.cn)(d(),t),...r})});o.displayName=l.b.displayName},5169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5332:(e,s,t)=>{Promise.resolve().then(t.bind(t,4177))},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"notFound")&&t.d(s,{notFound:function(){return a.notFound}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>n,ZB:()=>d,Zp:()=>i,aR:()=>c,wL:()=>m});var a=t(5155),r=t(2115),l=t(9434);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});d.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});n.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},8749:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{e.O(0,[274,804,197,441,964,358],()=>e(e.s=5332)),_N_E=e.O()}]);