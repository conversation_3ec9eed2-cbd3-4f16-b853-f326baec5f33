"use client"

import { HeroSec<PERSON> } from "@/components/home/<USER>"
import { FeaturesSection } from "@/components/home/<USER>"
import { FeaturedProductsSection } from "@/components/home/<USER>"
import { AIRecommendations } from "@/components/ai/ai-recommendations"
import { AIChatSupport } from "@/components/ai/ai-chat-support"

export default function Home() {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      <FeaturedProductsSection />

      {/* AI-Powered Recommendations */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <AIRecommendations
            title="Recommended Just for You"
            subtitle="AI-powered product recommendations based on your preferences and browsing history"
            limit={6}
            showReason={true}
            variant="default"
          />
        </div>
      </section>

      {/* AI Chat Support */}
      <AIChatSupport
        initialMessage="Hi! I'm your AI shopping assistant. I can help you find products, track orders, or answer any questions you have!"
        position="bottom-right"
      />
    </div>
  )
}
