"use client"

import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { ShoppingBag, Wrench, Factory, BookOpen, Sun, Moon } from "lucide-react"
import { useTheme } from "next-themes"

export default function Home() {
  const { language, setLanguage, t } = useLanguage()
  const { toast } = useToast()
  const { theme, setTheme } = useTheme()

  const handleLanguageToggle = () => {
    setLanguage(language === 'en' ? 'ar' : 'en')
    toast({
      title: t('common.success'),
      description: `Language changed to ${language === 'en' ? 'العربية' : 'English'}`,
    })
  }

  const handleThemeToggle = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
    toast({
      title: t('common.success'),
      description: `Theme changed to ${theme === 'dark' ? 'light' : 'dark'} mode`,
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold text-gradient-primary">
              AIDEVCOMMERCE
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleLanguageToggle}
              className="font-medium"
            >
              {language === 'en' ? 'العربية' : 'English'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleThemeToggle}
              className="p-2"
            >
              {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-12">
        <div className="text-center space-y-8">
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold text-gradient-primary animate-fade-in">
              {t('nav.home')} - AIDEVCOMMERCE
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto animate-fade-in animate-delay-200">
              Advanced E-commerce Platform with AI Integration
            </p>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-fade-in animate-delay-300">
              Discover our products, services, production lines, and insights through our comprehensive platform.
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            <div className="card-hover bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-fade-in animate-delay-500">
              <ShoppingBag className="h-12 w-12 text-primary-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">{t('nav.shop')}</h3>
              <p className="text-muted-foreground">
                Browse our extensive product catalog with AI-powered recommendations
              </p>
            </div>

            <div className="card-hover bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-fade-in animate-delay-700">
              <Wrench className="h-12 w-12 text-secondary-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">{t('nav.services')}</h3>
              <p className="text-muted-foreground">
                Professional services tailored to your business needs
              </p>
            </div>

            <div className="card-hover bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-fade-in animate-delay-900">
              <Factory className="h-12 w-12 text-accent-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">{t('nav.production-lines')}</h3>
              <p className="text-muted-foreground">
                State-of-the-art production lines for industrial solutions
              </p>
            </div>

            <div className="card-hover bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-fade-in animate-delay-1100">
              <BookOpen className="h-12 w-12 text-primary-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">{t('nav.blog')}</h3>
              <p className="text-muted-foreground">
                Stay updated with industry insights and company news
              </p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12 animate-fade-in animate-delay-1300">
            <Button size="lg" className="btn-animate">
              {t('nav.shop')} {t('common.view-all')}
            </Button>
            <Button variant="outline" size="lg" className="btn-animate">
              {t('nav.about')} {t('common.read-more')}
            </Button>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 mt-12 border-t border-border/50">
        <div className="text-center text-muted-foreground">
          <p>&copy; 2024 AIDEVCOMMERCE. All rights reserved.</p>
          <p className="mt-2">Built with Next.js 15, TypeScript, and Tailwind CSS</p>
        </div>
      </footer>
    </div>
  )
}
