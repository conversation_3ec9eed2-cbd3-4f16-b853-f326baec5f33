"use client"

import { useState } from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  ArrowLeft,
  Star,
  Factory,
  Zap,
  Settings,
  Gauge,
  CheckCircle,
  Download,
  Share2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Users,
  Award,
  Shield,
  TrendingUp,
  FileText,
  Video,
  Image as ImageIcon,
} from "lucide-react"

// Mock production line data (in a real app, this would be fetched based on slug)
const mockProductionLine = {
  id: "1",
  name: "Automated Assembly Line Pro",
  slug: "automated-assembly-line-pro",
  description: "High-speed automated assembly line with advanced robotics and quality control systems for maximum efficiency. This state-of-the-art production line integrates cutting-edge technology with proven manufacturing processes to deliver unparalleled performance and reliability.",
  shortDescription: "High-speed automated assembly with robotics",
  category: "Assembly Lines",
  industry: "Automotive",
  capacity: "500 units/hour",
  power: "150 kW",
  footprint: "50m x 20m",
  price: 2500000,
  leadTime: "12-16 weeks",
  rating: 4.9,
  installationCount: 45,
  image: "/production-line-1.jpg",
  featured: true,
  popular: true,
  certifications: ["ISO 9001", "CE", "UL", "OSHA"],
  specifications: {
    "Production Capacity": "500 units/hour",
    "Power Consumption": "150 kW",
    "Floor Space": "50m x 20m x 8m",
    "Automation Level": "Fully Automated",
    "Quality Control": "Integrated Vision Systems",
    "Maintenance": "Predictive Maintenance",
    "Warranty": "2 Years Full Coverage",
    "Training": "Included",
    "Installation": "Turnkey Solution",
    "Support": "24/7 Remote Monitoring"
  },
  features: [
    "Advanced Robotics Integration",
    "Real-time Quality Monitoring",
    "Predictive Maintenance System",
    "Energy Efficient Design",
    "Modular Configuration",
    "Remote Monitoring Capability",
    "Safety Compliance Systems",
    "Data Analytics Dashboard",
    "Flexible Production Scheduling",
    "Automated Material Handling"
  ],
  applications: [
    "Automotive Manufacturing",
    "Electronics Assembly",
    "Consumer Goods",
    "Medical Devices",
    "Aerospace Components",
    "Industrial Equipment"
  ],
  benefits: [
    "Increased Production Efficiency",
    "Reduced Labor Costs",
    "Improved Product Quality",
    "Enhanced Safety Standards",
    "Lower Maintenance Requirements",
    "Real-time Production Monitoring",
    "Scalable Configuration",
    "Quick ROI Achievement"
  ],
  technicalSpecs: {
    "Mechanical": {
      "Frame Construction": "Heavy-duty steel frame",
      "Conveyor System": "Variable speed belt conveyor",
      "Positioning Accuracy": "±0.1mm",
      "Cycle Time": "7.2 seconds per unit"
    },
    "Electrical": {
      "Power Supply": "400V, 3-phase, 50/60Hz",
      "Control System": "Siemens PLC with HMI",
      "Safety Systems": "Category 3 safety circuits",
      "Communication": "Ethernet/IP, Profinet"
    },
    "Software": {
      "Operating System": "Windows-based HMI",
      "Data Logging": "SQL Server database",
      "Reporting": "Real-time dashboards",
      "Integration": "ERP/MES connectivity"
    }
  },
  gallery: [
    { type: "image", title: "Main Assembly Station", url: "/gallery-1.jpg" },
    { type: "image", title: "Quality Control System", url: "/gallery-2.jpg" },
    { type: "image", title: "Control Panel", url: "/gallery-3.jpg" },
    { type: "video", title: "Production Line in Action", url: "/video-1.mp4" },
    { type: "image", title: "Safety Systems", url: "/gallery-4.jpg" },
    { type: "image", title: "Maintenance Access", url: "/gallery-5.jpg" }
  ]
}

const mockCaseStudies = [
  {
    id: "1",
    company: "AutoTech Manufacturing",
    industry: "Automotive",
    challenge: "Needed to increase production capacity by 300% while maintaining quality standards",
    solution: "Implemented Automated Assembly Line Pro with custom robotics integration",
    results: "Achieved 400% capacity increase with 99.8% quality rate and 40% reduction in labor costs",
    timeline: "14 weeks implementation"
  },
  {
    id: "2",
    company: "ElectroComponents Ltd",
    industry: "Electronics",
    challenge: "Required flexible production line for multiple product variants",
    solution: "Deployed modular configuration with quick-changeover capabilities",
    results: "Reduced changeover time from 4 hours to 15 minutes, increased OEE by 35%",
    timeline: "12 weeks implementation"
  }
]

interface ProductionLinePageProps {
  params: {
    slug: string
  }
}

export default function ProductionLinePage({ params }: ProductionLinePageProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedImage, setSelectedImage] = useState(0)
  const { t } = useLanguage()
  const { toast } = useToast()

  // In a real app, you would fetch the production line based on the slug
  const productionLine = mockProductionLine
  
  if (!productionLine) {
    notFound()
  }

  const handleInquiry = () => {
    toast({
      title: "Inquiry Sent",
      description: "Our sales team will contact you within 24 hours",
    })
  }

  const handleDownloadBrochure = () => {
    toast({
      title: "Download Started",
      description: "Technical brochure is being downloaded",
    })
  }

  const handleRequestQuote = () => {
    toast({
      title: "Quote Request Sent",
      description: "We'll prepare a detailed quote and send it to you within 48 hours",
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: productionLine.name,
        text: productionLine.description,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link Copied",
        description: "Production line link copied to clipboard",
      })
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Assembly Lines": return Factory
      case "Manufacturing Cells": return Settings
      case "Packaging Lines": return Zap
      case "Machining Centers": return Gauge
      default: return Factory
    }
  }

  const CategoryIcon = getCategoryIcon(productionLine.category)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-8">
          <Link href="/" className="hover:text-foreground">Home</Link>
          <span>/</span>
          <Link href="/production-lines" className="hover:text-foreground">Production Lines</Link>
          <span>/</span>
          <span className="text-foreground">{productionLine.name}</span>
        </nav>

        {/* Back Button */}
        <Link href="/production-lines" className="inline-flex items-center text-sm text-gray-500 hover:text-foreground mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Production Lines
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Production Line Header */}
            <div className="space-y-6">
              {/* Main Image */}
              <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-xl flex items-center justify-center">
                <CategoryIcon className="h-32 w-32 text-gray-400" />
              </div>

              {/* Gallery Thumbnails */}
              <div className="flex space-x-2 overflow-x-auto">
                {productionLine.gallery.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`flex-shrink-0 w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-lg border-2 flex items-center justify-center ${
                      selectedImage === index 
                        ? "border-primary-500" 
                        : "border-gray-200 dark:border-gray-700"
                    }`}
                  >
                    {item.type === "video" ? (
                      <Video className="h-6 w-6 text-gray-400" />
                    ) : (
                      <ImageIcon className="h-6 w-6 text-gray-400" />
                    )}
                  </button>
                ))}
              </div>

              {/* Production Line Info */}
              <div>
                <div className="flex items-center space-x-4 mb-4">
                  <Badge variant="outline">{productionLine.category}</Badge>
                  <Badge variant="secondary">{productionLine.industry}</Badge>
                  {productionLine.featured && <Badge className="bg-primary-500">Featured</Badge>}
                  {productionLine.popular && <Badge className="bg-orange-500">Popular</Badge>}
                </div>
                
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  {productionLine.name}
                </h1>

                <div className="flex items-center space-x-6 mb-6">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${
                            i < Math.floor(productionLine.rating)
                              ? "text-yellow-400 fill-current"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="font-medium">{productionLine.rating}</span>
                    <span className="text-gray-500">({productionLine.installationCount} installations)</span>
                  </div>
                </div>

                <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed">
                  {productionLine.description}
                </p>
              </div>
            </div>

            {/* Production Line Details Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="specifications">Specs</TabsTrigger>
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="applications">Applications</TabsTrigger>
                <TabsTrigger value="case-studies">Case Studies</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Key Specifications */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Key Specifications</CardTitle>
                      <CardDescription>
                        Essential technical specifications
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {Object.entries(productionLine.specifications).slice(0, 6).map(([key, value]) => (
                          <div key={key} className="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                            <span className="font-medium">{key}</span>
                            <span className="text-gray-600 dark:text-gray-400">{value}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Benefits */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Key Benefits</CardTitle>
                      <CardDescription>
                        Advantages of this production line
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {productionLine.benefits.slice(0, 6).map((benefit, index) => (
                          <li key={index} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                            <span className="text-sm">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="specifications" className="mt-8">
                <div className="space-y-6">
                  {Object.entries(productionLine.technicalSpecs).map(([category, specs]) => (
                    <Card key={category}>
                      <CardHeader>
                        <CardTitle>{category} Specifications</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {Object.entries(specs).map(([key, value]) => (
                            <div key={key} className="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                              <span className="font-medium">{key}</span>
                              <span className="text-gray-600 dark:text-gray-400">{value}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="features" className="mt-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Advanced Features</CardTitle>
                    <CardDescription>
                      Comprehensive feature set for optimal performance
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {productionLine.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                          <CheckCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="applications" className="mt-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Industry Applications</CardTitle>
                    <CardDescription>
                      Suitable industries and use cases
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {productionLine.applications.map((application, index) => (
                        <div key={index} className="p-4 border rounded-lg text-center">
                          <Factory className="h-8 w-8 text-primary-600 dark:text-primary-400 mx-auto mb-2" />
                          <h4 className="font-semibold">{application}</h4>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="case-studies" className="mt-8">
                <div className="space-y-6">
                  {mockCaseStudies.map((study) => (
                    <Card key={study.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle>{study.company}</CardTitle>
                          <Badge variant="outline">{study.industry}</Badge>
                        </div>
                        <CardDescription>
                          Implementation Timeline: {study.timeline}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-2">Challenge</h4>
                          <p className="text-gray-600 dark:text-gray-400">{study.challenge}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Solution</h4>
                          <p className="text-gray-600 dark:text-gray-400">{study.solution}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Results</h4>
                          <p className="text-green-600 dark:text-green-400">{study.results}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Inquiry Card */}
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Get Information</CardTitle>
                <CardDescription>
                  Request detailed information and pricing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Price */}
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                    {formatPrice(productionLine.price)}
                  </div>
                  <div className="text-sm text-gray-500">starting price</div>
                  <div className="text-sm text-gray-500 mt-1">
                    Lead time: {productionLine.leadTime}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    size="lg"
                    onClick={handleRequestQuote}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Request Quote
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleInquiry}
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Schedule Call
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleDownloadBrochure}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Brochure
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleShare}
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                </div>

                {/* Contact Info */}
                <div className="pt-6 border-t space-y-3">
                  <div className="flex items-center space-x-3 text-sm">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>+****************</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>Global Installation</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Certifications */}
            <Card>
              <CardHeader>
                <CardTitle>Certifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {productionLine.certifications.map((cert, index) => (
                    <Badge key={index} variant="secondary">
                      <Award className="h-3 w-3 mr-1" />
                      {cert}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Installations</span>
                  <span className="font-semibold">{productionLine.installationCount}+</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Rating</span>
                  <span className="font-semibold">{productionLine.rating}/5.0</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Capacity</span>
                  <span className="font-semibold">{productionLine.capacity}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Power</span>
                  <span className="font-semibold">{productionLine.power}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
