import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getUserByEmail } from '@/lib/auth'
import { generateId } from '@/lib/utils'
import { prisma } from '@/lib/prisma'

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { email } = forgotPasswordSchema.parse(body)
    
    // Check if user exists
    const user = await getUserByEmail(email)
    if (!user) {
      // Don't reveal if user exists or not for security
      return NextResponse.json(
        { 
          success: true, 
          message: 'If an account with that email exists, we have sent a password reset link.' 
        },
        { status: 200 }
      )
    }
    
    // Generate reset token
    const resetToken = generateId()
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now
    
    // Store reset token in database
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: resetToken,
        expires: resetTokenExpiry,
      }
    })
    
    // TODO: Send email with reset link
    // await sendPasswordResetEmail(email, resetToken)
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'If an account with that email exists, we have sent a password reset link.' 
      },
      { status: 200 }
    )
    
  } catch (error) {
    console.error('Forgot password error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          errors: error.flatten().fieldErrors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
