"use client"

import { useState } from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  ArrowLeft,
  Calendar,
  User,
  Eye,
  MessageCircle,
  Share2,
  BookOpen,
  Clock,
  Tag,
  Heart,
  Facebook,
  Twitter,
  Linkedin,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Reply,
} from "lucide-react"

// Mock blog post data (in a real app, this would be fetched based on slug)
const mockBlogPost = {
  id: "1",
  title: "The Future of AI in E-commerce: Transforming Online Shopping",
  slug: "future-ai-ecommerce-transforming-online-shopping",
  excerpt: "Explore how artificial intelligence is revolutionizing the e-commerce landscape, from personalized recommendations to automated customer service.",
  content: `
    <h2>Introduction</h2>
    <p>Artificial Intelligence (AI) is no longer a futuristic concept—it's actively reshaping the e-commerce landscape today. From personalized product recommendations to intelligent chatbots, AI technologies are enhancing every aspect of the online shopping experience.</p>
    
    <h2>Personalized Shopping Experiences</h2>
    <p>One of the most significant impacts of AI in e-commerce is the ability to create highly personalized shopping experiences. Machine learning algorithms analyze customer behavior, purchase history, and browsing patterns to deliver tailored product recommendations that increase conversion rates and customer satisfaction.</p>
    
    <h3>Key Benefits:</h3>
    <ul>
      <li>Increased conversion rates through relevant product suggestions</li>
      <li>Enhanced customer satisfaction with personalized experiences</li>
      <li>Improved customer retention and loyalty</li>
      <li>Higher average order values through cross-selling and upselling</li>
    </ul>
    
    <h2>Automated Customer Service</h2>
    <p>AI-powered chatbots and virtual assistants are revolutionizing customer service in e-commerce. These intelligent systems can handle routine inquiries, provide product information, and even assist with order processing, all while learning from each interaction to improve their responses.</p>
    
    <h2>Inventory Management and Demand Forecasting</h2>
    <p>AI algorithms excel at analyzing historical sales data, seasonal trends, and market conditions to predict future demand accurately. This capability helps e-commerce businesses optimize their inventory levels, reduce stockouts, and minimize carrying costs.</p>
    
    <h2>The Road Ahead</h2>
    <p>As AI technology continues to evolve, we can expect even more innovative applications in e-commerce. From augmented reality shopping experiences to voice commerce, the future holds exciting possibilities for both businesses and consumers.</p>
    
    <p>The key to success will be implementing AI solutions that genuinely enhance the customer experience while maintaining the human touch that builds trust and loyalty.</p>
  `,
  category: "Technology",
  author: {
    name: "Sarah Johnson",
    avatar: "/author-1.jpg",
    bio: "AI Research Specialist with over 8 years of experience in machine learning and e-commerce technology. Sarah has helped numerous companies implement AI solutions to improve their online presence.",
    social: {
      twitter: "@sarahjohnson",
      linkedin: "sarah-johnson-ai"
    }
  },
  publishedAt: new Date("2024-01-15"),
  updatedAt: new Date("2024-01-16"),
  readTime: "8 min read",
  views: 1250,
  comments: 23,
  likes: 89,
  featured: true,
  trending: true,
  image: "/blog-1.jpg",
  tags: ["AI", "E-commerce", "Machine Learning", "Technology", "Digital Transformation"],
  relatedPosts: [
    {
      id: "2",
      title: "Digital Transformation in Small Businesses: A Complete Guide",
      slug: "digital-transformation-small-businesses-guide",
      image: "/blog-3.jpg",
      publishedAt: new Date("2024-01-10"),
      readTime: "10 min read"
    },
    {
      id: "3",
      title: "The Rise of Voice Commerce: Shopping with Voice Assistants",
      slug: "rise-voice-commerce-shopping-voice-assistants",
      image: "/blog-6.jpg",
      publishedAt: new Date("2024-01-03"),
      readTime: "5 min read"
    }
  ]
}

const mockComments = [
  {
    id: "1",
    author: "Mike Chen",
    avatar: "/commenter-1.jpg",
    content: "Great article! We've implemented AI recommendations in our e-commerce platform and seen a 35% increase in conversion rates. The personalization aspect is truly game-changing.",
    publishedAt: new Date("2024-01-16T10:30:00"),
    likes: 12,
    replies: [
      {
        id: "1-1",
        author: "Sarah Johnson",
        avatar: "/author-1.jpg",
        content: "Thanks Mike! That's an impressive improvement. Would love to hear more about your implementation approach.",
        publishedAt: new Date("2024-01-16T11:15:00"),
        likes: 5,
        isAuthor: true
      }
    ]
  },
  {
    id: "2",
    author: "Lisa Wang",
    avatar: "/commenter-2.jpg",
    content: "The section on inventory management really resonates with our challenges. We're looking into AI solutions for demand forecasting. Any specific tools you'd recommend?",
    publishedAt: new Date("2024-01-16T14:20:00"),
    likes: 8,
    replies: []
  }
]

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const [liked, setLiked] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)
  const [newComment, setNewComment] = useState("")
  
  const { t } = useLanguage()
  const { toast } = useToast()

  // In a real app, you would fetch the blog post based on the slug
  const post = mockBlogPost
  
  if (!post) {
    notFound()
  }

  const handleLike = () => {
    setLiked(!liked)
    toast({
      title: liked ? "Removed from favorites" : "Added to favorites",
      description: liked ? "Post removed from your favorites" : "Post added to your favorites",
    })
  }

  const handleShare = (platform?: string) => {
    const url = window.location.href
    const text = `Check out this article: ${post.title}`
    
    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`)
        break
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`)
        break
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`)
        break
      default:
        if (navigator.share) {
          navigator.share({ title: post.title, text: post.excerpt, url })
        } else {
          navigator.clipboard.writeText(url)
          toast({
            title: "Link Copied",
            description: "Article link copied to clipboard",
          })
        }
    }
  }

  const handleSubmitComment = () => {
    if (!newComment.trim()) return
    
    toast({
      title: "Comment Posted",
      description: "Your comment has been posted successfully",
    })
    setNewComment("")
    setShowCommentForm(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-8">
          <Link href="/" className="hover:text-foreground">Home</Link>
          <span>/</span>
          <Link href="/blog" className="hover:text-foreground">Blog</Link>
          <span>/</span>
          <span className="text-foreground">{post.category}</span>
        </nav>

        {/* Back Button */}
        <Link href="/blog" className="inline-flex items-center text-sm text-gray-500 hover:text-foreground mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Blog
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <article className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
              {/* Hero Image */}
              <div className="aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900 dark:to-secondary-900 flex items-center justify-center">
                <BookOpen className="h-24 w-24 text-primary-600 dark:text-primary-400" />
              </div>

              <div className="p-8">
                {/* Article Header */}
                <div className="mb-8">
                  {/* Meta Info */}
                  <div className="flex items-center space-x-4 mb-4">
                    <Badge variant="secondary">{post.category}</Badge>
                    {post.featured && <Badge className="bg-primary-500">Featured</Badge>}
                    {post.trending && <Badge className="bg-orange-500">Trending</Badge>}
                  </div>

                  {/* Title */}
                  <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
                    {post.title}
                  </h1>

                  {/* Author and Meta */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-gray-400" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">
                          {post.author.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {post.author.bio.split('.')[0]}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(post.publishedAt)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{post.readTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{post.views}</span>
                      </div>
                    </div>
                  </div>

                  {/* Social Actions */}
                  <div className="flex items-center justify-between py-4 border-y border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLike}
                        className={liked ? "text-red-500" : ""}
                      >
                        <Heart className={`h-4 w-4 mr-2 ${liked ? 'fill-current' : ''}`} />
                        {post.likes + (liked ? 1 : 0)}
                      </Button>
                      
                      <Button variant="ghost" size="sm">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        {post.comments}
                      </Button>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" onClick={() => handleShare('twitter')}>
                        <Twitter className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleShare('facebook')}>
                        <Facebook className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleShare('linkedin')}>
                        <Linkedin className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleShare()}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Article Content */}
                <div 
                  className="prose prose-lg max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />

                {/* Tags */}
                <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="cursor-pointer hover:bg-primary-50 dark:hover:bg-primary-900">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Author Bio */}
                <div className="mt-8 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        About {post.author.name}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400 mb-3">
                        {post.author.bio}
                      </p>
                      <div className="flex items-center space-x-4">
                        {post.author.social.twitter && (
                          <a href={`https://twitter.com/${post.author.social.twitter.replace('@', '')}`} className="text-blue-500 hover:text-blue-600">
                            <Twitter className="h-4 w-4" />
                          </a>
                        )}
                        {post.author.social.linkedin && (
                          <a href={`https://linkedin.com/in/${post.author.social.linkedin}`} className="text-blue-700 hover:text-blue-800">
                            <Linkedin className="h-4 w-4" />
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </article>

            {/* Comments Section */}
            <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold">Comments ({mockComments.length})</h3>
                <Button onClick={() => setShowCommentForm(!showCommentForm)}>
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Add Comment
                </Button>
              </div>

              {/* Comment Form */}
              {showCommentForm && (
                <div className="mb-8 p-4 border rounded-lg">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Share your thoughts..."
                    className="w-full p-3 border rounded-lg resize-none h-24"
                  />
                  <div className="flex justify-end space-x-2 mt-3">
                    <Button variant="outline" onClick={() => setShowCommentForm(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSubmitComment}>
                      Post Comment
                    </Button>
                  </div>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-6">
                {mockComments.map((comment) => (
                  <div key={comment.id} className="border-b border-gray-200 dark:border-gray-700 pb-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-semibold">{comment.author}</span>
                          {comment.isAuthor && (
                            <Badge variant="secondary" className="text-xs">Author</Badge>
                          )}
                          <span className="text-sm text-gray-500">
                            {formatDate(comment.publishedAt)}
                          </span>
                        </div>
                        <p className="text-gray-700 dark:text-gray-300 mb-3">
                          {comment.content}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <button className="flex items-center space-x-1 text-gray-500 hover:text-primary-600">
                            <ThumbsUp className="h-4 w-4" />
                            <span>{comment.likes}</span>
                          </button>
                          <button className="flex items-center space-x-1 text-gray-500 hover:text-primary-600">
                            <Reply className="h-4 w-4" />
                            <span>Reply</span>
                          </button>
                        </div>

                        {/* Replies */}
                        {comment.replies && comment.replies.length > 0 && (
                          <div className="mt-4 ml-6 space-y-4">
                            {comment.replies.map((reply) => (
                              <div key={reply.id} className="flex items-start space-x-3">
                                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                                  <User className="h-4 w-4 text-gray-400" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <span className="font-semibold text-sm">{reply.author}</span>
                                    {reply.isAuthor && (
                                      <Badge variant="secondary" className="text-xs">Author</Badge>
                                    )}
                                    <span className="text-xs text-gray-500">
                                      {formatDate(reply.publishedAt)}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                                    {reply.content}
                                  </p>
                                  <button className="flex items-center space-x-1 text-xs text-gray-500 hover:text-primary-600">
                                    <ThumbsUp className="h-3 w-3" />
                                    <span>{reply.likes}</span>
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Share */}
            <Card>
              <CardHeader>
                <CardTitle>Share Article</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-2">
                  <Button variant="outline" size="sm" onClick={() => handleShare('twitter')}>
                    <Twitter className="h-4 w-4 mr-2" />
                    Twitter
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleShare('facebook')}>
                    <Facebook className="h-4 w-4 mr-2" />
                    Facebook
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleShare('linkedin')}>
                    <Linkedin className="h-4 w-4 mr-2" />
                    LinkedIn
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleShare()}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Related Posts */}
            <Card>
              <CardHeader>
                <CardTitle>Related Articles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {post.relatedPosts.map((relatedPost) => (
                    <div key={relatedPost.id} className="flex space-x-3">
                      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center flex-shrink-0">
                        <BookOpen className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <Link 
                          href={`/blog/${relatedPost.slug}`}
                          className="text-sm font-medium hover:text-primary-600 transition-colors line-clamp-2"
                        >
                          {relatedPost.title}
                        </Link>
                        <div className="flex items-center space-x-2 mt-1 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(relatedPost.publishedAt)}</span>
                          <Clock className="h-3 w-3" />
                          <span>{relatedPost.readTime}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter */}
            <Card>
              <CardHeader>
                <CardTitle>Stay Updated</CardTitle>
                <CardDescription>
                  Get the latest articles delivered to your inbox
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="w-full px-3 py-2 border rounded-lg text-sm"
                  />
                  <Button className="w-full" size="sm">
                    Subscribe
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
