(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[949],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},482:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>M});var t=a(5155),r=a(2115),d=a(2177),i=a(221),l=a(8309),c=a(4997),n=a(285),o=a(2523),m=a(5057),h=a(6695),p=a(7481),x=a(4817),y=a(4616),u=a(9946);let f=(0,u.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),g=(0,u.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var j=a(8564);let N=(0,u.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var v=a(2525);let k=(0,u.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var b=a(4516);let A=l.Ik({type:l.k5(["shipping","billing"]),firstName:l.Yj().min(2,"First name must be at least 2 characters"),lastName:l.Yj().min(2,"Last name must be at least 2 characters"),company:l.Yj().optional(),address1:l.Yj().min(5,"Address must be at least 5 characters"),address2:l.Yj().optional(),city:l.Yj().min(2,"City must be at least 2 characters"),state:l.Yj().min(2,"State must be at least 2 characters"),postalCode:l.Yj().min(5,"Postal code must be at least 5 characters"),country:l.Yj().min(2,"Country must be at least 2 characters"),phone:l.Yj().optional(),isDefault:l.zM().default(!1)}),w=[{id:"1",type:"shipping",firstName:"John",lastName:"Doe",company:"",address1:"123 Main Street",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"United States",phone:"+****************",isDefault:!0},{id:"2",type:"billing",firstName:"John",lastName:"Doe",company:"Acme Corp",address1:"456 Business Ave",address2:"Suite 200",city:"New York",state:"NY",postalCode:"10002",country:"United States",phone:"+****************",isDefault:!1}];function M(){let[e,s]=(0,r.useState)(w),[a,l]=(0,r.useState)(!1),[u,M]=(0,r.useState)(null),[C,F]=(0,r.useState)(!1),{toast:S}=(0,p.dj)(),{t:D}=(0,x.o)(),{register:J,handleSubmit:z,formState:{errors:E},reset:P,setValue:Y}=(0,d.mN)({resolver:(0,i.u)(A)}),R=async e=>{F(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),u)s(s=>s.map(s=>s.id===u?{...s,...e}:s)),S({title:D("common.success"),description:"Address updated successfully"}),M(null);else{let a={id:Date.now().toString(),...e};s(e=>[...e,a]),S({title:D("common.success"),description:"Address added successfully"}),l(!1)}P()}catch(e){S({title:D("common.error"),description:"Failed to save address",variant:"destructive"})}finally{F(!1)}},O=async e=>{if(confirm("Are you sure you want to delete this address?")){F(!0);try{await new Promise(e=>setTimeout(e,500)),s(s=>s.filter(s=>s.id!==e)),S({title:D("common.success"),description:"Address deleted successfully"})}catch(e){S({title:D("common.error"),description:"Failed to delete address",variant:"destructive"})}finally{F(!1)}}},B=async e=>{F(!0);try{await new Promise(e=>setTimeout(e,500)),s(s=>s.map(s=>({...s,isDefault:s.id===e}))),S({title:D("common.success"),description:"Default address updated"})}catch(e){S({title:D("common.error"),description:"Failed to update default address",variant:"destructive"})}finally{F(!1)}};return(0,t.jsx)(c.O,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Addresses"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage your shipping and billing addresses"})]}),!a&&(0,t.jsxs)(n.$,{onClick:()=>l(!0),children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Add Address"]})]}),a&&(0,t.jsxs)(h.Zp,{className:"mb-6",children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:u?"Edit Address":"Add New Address"}),(0,t.jsx)(h.BT,{children:u?"Update your address information":"Add a new shipping or billing address"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("form",{onSubmit:z(R),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{children:"Address Type"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"radio",value:"shipping",...J("type"),className:"text-primary-600"}),(0,t.jsx)(f,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Shipping"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"radio",value:"billing",...J("type"),className:"text-primary-600"}),(0,t.jsx)(g,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Billing"})]})]}),E.type&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.type.message})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"firstName",children:"First Name"}),(0,t.jsx)(o.p,{id:"firstName",...J("firstName"),placeholder:"Enter first name"}),E.firstName&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.firstName.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"lastName",children:"Last Name"}),(0,t.jsx)(o.p,{id:"lastName",...J("lastName"),placeholder:"Enter last name"}),E.lastName&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.lastName.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"company",children:"Company (Optional)"}),(0,t.jsx)(o.p,{id:"company",...J("company"),placeholder:"Enter company name"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"address1",children:"Address Line 1"}),(0,t.jsx)(o.p,{id:"address1",...J("address1"),placeholder:"Enter street address"}),E.address1&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.address1.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),(0,t.jsx)(o.p,{id:"address2",...J("address2"),placeholder:"Apartment, suite, etc."})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"city",children:"City"}),(0,t.jsx)(o.p,{id:"city",...J("city"),placeholder:"Enter city"}),E.city&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.city.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"state",children:"State/Province"}),(0,t.jsx)(o.p,{id:"state",...J("state"),placeholder:"Enter state"}),E.state&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.state.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"postalCode",children:"Postal Code"}),(0,t.jsx)(o.p,{id:"postalCode",...J("postalCode"),placeholder:"Enter postal code"}),E.postalCode&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.postalCode.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"country",children:"Country"}),(0,t.jsx)(o.p,{id:"country",...J("country"),placeholder:"Enter country"}),E.country&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:E.country.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"phone",children:"Phone (Optional)"}),(0,t.jsx)(o.p,{id:"phone",type:"tel",...J("phone"),placeholder:"Enter phone number"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"isDefault",...J("isDefault"),className:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"}),(0,t.jsx)(m.J,{htmlFor:"isDefault",children:"Set as default address"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>{l(!1),M(null),P()},disabled:C,children:"Cancel"}),(0,t.jsx)(n.$,{type:"submit",disabled:C,children:C?"Saving...":u?"Update Address":"Add Address"})]})]})})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.map(e=>(0,t.jsxs)(h.Zp,{className:"relative",children:[e.isDefault&&(0,t.jsx)("div",{className:"absolute top-4 right-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-full text-xs font-medium",children:[(0,t.jsx)(j.A,{className:"h-3 w-3 fill-current"}),(0,t.jsx)("span",{children:"Default"})]})}),(0,t.jsx)(h.aR,{children:(0,t.jsxs)(h.ZB,{className:"flex items-center text-lg",children:["shipping"===e.type?(0,t.jsx)(f,{className:"h-5 w-5 mr-2"}):(0,t.jsx)(g,{className:"h-5 w-5 mr-2"}),"shipping"===e.type?"Shipping":"Billing"," Address"]})}),(0,t.jsxs)(h.Wu,{children:[(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{className:"font-medium",children:[e.firstName," ",e.lastName]}),e.company&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.company}),(0,t.jsx)("p",{children:e.address1}),e.address2&&(0,t.jsx)("p",{children:e.address2}),(0,t.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),(0,t.jsx)("p",{children:e.country}),e.phone&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.phone})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-4 border-t",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(n.$,{size:"sm",variant:"outline",onClick:()=>{M(e.id),l(!0),Object.entries(e).forEach(e=>{let[s,a]=e;"id"!==s&&Y(s,a)})},children:[(0,t.jsx)(N,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,t.jsxs)(n.$,{size:"sm",variant:"outline",onClick:()=>O(e.id),className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]}),!e.isDefault&&(0,t.jsxs)(n.$,{size:"sm",variant:"ghost",onClick:()=>B(e.id),className:"text-primary-600 hover:text-primary-700",children:[(0,t.jsx)(k,{className:"h-4 w-4 mr-1"}),"Set Default"]})]})]})]},e.id))}),0===e.length&&!a&&(0,t.jsx)(h.Zp,{children:(0,t.jsxs)(h.Wu,{className:"text-center py-12",children:[(0,t.jsx)(b.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No addresses yet"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Add your first address to get started with orders"}),(0,t.jsxs)(n.$,{onClick:()=>l(!0),children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Add Your First Address"]})]})})]})})}},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1586:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1976:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var t=a(5155),r=a(2115),d=a(9434);let i=r.forwardRef((e,s)=>{let{className:a,type:r,...i}=e;return(0,t.jsx)("input",{type:r,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"},2525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},3861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4835:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},4997:(e,s,a)=>{"use strict";a.d(s,{O:()=>j});var t=a(5155),r=a(6874),d=a.n(r),i=a(5695),l=a(9434),c=a(4817),n=a(1007),o=a(4516),m=a(7108),h=a(1976),p=a(3861),x=a(1586),y=a(5525),u=a(381),f=a(9947),g=a(4835);function j(e){let{children:s}=e,a=(0,i.usePathname)(),{t:r}=(0,c.o)(),j=[{name:"Profile",href:"/account/profile",icon:n.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:o.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:m.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:h.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:p.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:x.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:y.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:u.A,description:"Account preferences and settings"}],N=[{name:"Help Center",href:"/help",icon:f.A},{name:"Contact Support",href:"/contact",icon:f.A}];return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,t.jsx)(n.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,t.jsxs)("nav",{className:"p-2",children:[(0,t.jsx)("div",{className:"space-y-1",children:j.map(e=>{let s=a===e.href;return(0,t.jsxs)(d(),{href:e.href,className:(0,l.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",s?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,t.jsx)(e.icon,{className:(0,l.cn)("mr-3 h-5 w-5",s?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{children:e.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:e.description})]})]},e.href)})}),(0,t.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"space-y-1",children:[N.map(e=>(0,t.jsxs)(d(),{href:e.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,t.jsx)(e.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),e.name]},e.href)),(0,t.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,t.jsx)(g.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:s})})]})})})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var t=a(5155),r=a(2115),d=a(968),i=a(2085),l=a(9434);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(d.b,{ref:s,className:(0,l.cn)(c(),a),...r})});n.displayName=d.b.displayName},5525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"notFound")&&a.d(s,{notFound:function(){return t.notFound}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>n,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>l,wL:()=>m});var t=a(5155),r=a(2115),d=a(9434);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",a),...r})});n.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("p-6 pt-0",a),...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("flex items-center p-6 pt-0",a),...r})});m.displayName="CardFooter"},6990:(e,s,a)=>{Promise.resolve().then(a.bind(a,482))},7108:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},8564:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9947:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}},e=>{e.O(0,[274,804,197,441,964,358],()=>e(e.s=6990)),_N_E=e.O()}]);