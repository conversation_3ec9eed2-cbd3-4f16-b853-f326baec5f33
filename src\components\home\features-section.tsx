"use client"

import { useLanguage } from "@/components/providers/language-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ShoppingBag,
  Wrench,
  Factory,
  BookOpen,
  Shield,
  Truck,
  Headphones,
  Zap,
  Globe,
  Award,
  Users,
  TrendingUp,
} from "lucide-react"

const features = [
  {
    icon: ShoppingBag,
    title: "Smart E-commerce",
    description: "AI-powered product recommendations and seamless shopping experience with advanced search and filtering.",
    badge: "Popular",
    color: "from-blue-500 to-cyan-500",
    stats: "10K+ Products"
  },
  {
    icon: Wrench,
    title: "Professional Services",
    description: "Expert consultation, implementation, and support services tailored to your business needs.",
    badge: "Premium",
    color: "from-purple-500 to-pink-500",
    stats: "500+ Projects"
  },
  {
    icon: Factory,
    title: "Production Lines",
    description: "State-of-the-art manufacturing equipment and industrial solutions for modern production.",
    badge: "Industrial",
    color: "from-orange-500 to-red-500",
    stats: "200+ Installations"
  },
  {
    icon: BookOpen,
    title: "Knowledge Hub",
    description: "Comprehensive blog with industry insights, tutorials, and latest trends in technology.",
    badge: "Educational",
    color: "from-green-500 to-emerald-500",
    stats: "1K+ Articles"
  },
  {
    icon: Shield,
    title: "Secure & Reliable",
    description: "Enterprise-grade security with 99.9% uptime guarantee and data protection compliance.",
    badge: "Trusted",
    color: "from-indigo-500 to-blue-500",
    stats: "99.9% Uptime"
  },
  {
    icon: Globe,
    title: "Global Reach",
    description: "Worldwide shipping and multilingual support with localized experiences for different regions.",
    badge: "International",
    color: "from-teal-500 to-green-500",
    stats: "50+ Countries"
  },
]

const benefits = [
  {
    icon: Truck,
    title: "Fast Delivery",
    description: "Express shipping options with real-time tracking"
  },
  {
    icon: Headphones,
    title: "24/7 Support",
    description: "Round-the-clock customer service and technical support"
  },
  {
    icon: Award,
    title: "Quality Assured",
    description: "Premium products with comprehensive warranty coverage"
  },
  {
    icon: TrendingUp,
    title: "Growth Focused",
    description: "Solutions designed to scale with your business needs"
  },
]

export function FeaturesSection() {
  const { t } = useLanguage()

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            <Zap className="h-4 w-4 mr-2" />
            Features
          </Badge>
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Everything You Need
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Comprehensive solutions designed to meet all your business requirements with cutting-edge technology and exceptional service.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 overflow-hidden">
              <CardHeader className="relative">
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
                
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${feature.color} text-white shadow-lg`}>
                      <feature.icon className="h-6 w-6" />
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  
                  <CardTitle className="text-xl mb-2 group-hover:text-primary-600 transition-colors">
                    {feature.title}
                  </CardTitle>
                  <div className="text-sm text-primary-600 dark:text-primary-400 font-medium mb-2">
                    {feature.stats}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="relative z-10">
                <CardDescription className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Why Choose AIDEVCOMMERCE?
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              We're committed to delivering exceptional value through innovation, quality, and customer-centric approach.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center group">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white dark:bg-gray-800 rounded-2xl shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300">
                  <benefit.icon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                </div>
                <h4 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                  {benefit.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
          {[
            { value: "50K+", label: "Happy Customers", icon: Users },
            { value: "100K+", label: "Products Sold", icon: ShoppingBag },
            { value: "500+", label: "Projects Completed", icon: Award },
            { value: "99.9%", label: "Uptime Guarantee", icon: Shield },
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl mb-3 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors">
                <stat.icon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
