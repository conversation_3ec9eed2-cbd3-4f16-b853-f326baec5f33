(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{0:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{default:()=>bl});var e={};async function f(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}c.r(e),c.d(e,{config:()=>bh,middleware:()=>bg});let g=null;async function h(){if("phase-production-build"===process.env.NEXT_PHASE)return;g||(g=f());let a=await g;if(null==a?void 0:a.register)try{await a.register()}catch(a){throw a.message=`An error occurred while loading instrumentation hook: ${a.message}`,a}}async function i(...a){let b=await f();try{var c;await (null==b||null==(c=b.onRequestError)?void 0:c.call(b,...a))}catch(a){console.error("Error in instrumentation.onRequestError:",a)}}let j=null;function k(){return j||(j=h()),j}function l(a){return`The edge runtime does not support Node.js '${a}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==c.g.process&&(process.env=c.g.process.env,c.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(a){let b=new Proxy(function(){},{get(b,c){if("then"===c)return{};throw Object.defineProperty(Error(l(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(l(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(c,d,e){if("function"==typeof e[0])return e[0](b);throw Object.defineProperty(Error(l(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>b})},enumerable:!1,configurable:!1}),k();class m extends Error{constructor({page:a}){super(`The middleware "${a}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class o extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let p="_N_T_",q={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function r(a){var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}function s(a){let b={},c=[];if(a)for(let[d,e]of a.entries())"set-cookie"===d.toLowerCase()?(c.push(...r(e)),b[d]=1===c.length?c[0]:c):b[d]=e;return b}function t(a){try{return String(new URL(String(a)))}catch(b){throw Object.defineProperty(Error(`URL is malformed "${String(a)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:b}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...q,GROUP:{builtinReact:[q.reactServerComponents,q.actionBrowser],serverOnly:[q.reactServerComponents,q.actionBrowser,q.instrument,q.middleware],neutralTarget:[q.apiNode,q.apiEdge],clientOnly:[q.serverSideRendering,q.appPagesBrowser],bundled:[q.reactServerComponents,q.actionBrowser,q.serverSideRendering,q.appPagesBrowser,q.shared,q.instrument,q.middleware],appPages:[q.reactServerComponents,q.serverSideRendering,q.appPagesBrowser,q.actionBrowser]}});let u=Symbol("response"),v=Symbol("passThrough"),w=Symbol("waitUntil");class x{constructor(a,b){this[v]=!1,this[w]=b?{kind:"external",function:b}:{kind:"internal",promises:[]}}respondWith(a){this[u]||(this[u]=Promise.resolve(a))}passThroughOnException(){this[v]=!0}waitUntil(a){if("external"===this[w].kind)return(0,this[w].function)(a);this[w].promises.push(a)}}class y extends x{constructor(a){var b;super(a.request,null==(b=a.context)?void 0:b.waitUntil),this.sourcePage=a.page}get request(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function z(a){return a.replace(/\/$/,"")||"/"}function A(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}function B(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=A(a);return""+b+c+d+e}function C(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=A(a);return""+c+b+d+e}function D(a,b){if("string"!=typeof a)return!1;let{pathname:c}=A(a);return c===b||c.startsWith(b+"/")}let E=new WeakMap;function F(a,b){let c;if(!b)return{pathname:a};let d=E.get(b);d||(d=b.map(a=>a.toLowerCase()),E.set(b,d));let e=a.split("/",2);if(!e[1])return{pathname:a};let f=e[1].toLowerCase(),g=d.indexOf(f);return g<0?{pathname:a}:(c=b[g],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}let G=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function H(a,b){return new URL(String(a).replace(G,"localhost"),b&&String(b).replace(G,"localhost"))}let I=Symbol("NextURLInternal");class J{constructor(a,b,c){let d,e;"object"==typeof b&&"pathname"in b||"string"==typeof b?(d=b,e=c||{}):e=c||b||{},this[I]={url:H(a,d??e.base),options:e,basePath:""},this.analyze()}analyze(){var a,b,c,d,e;let f=function(a,b){var c,d;let{basePath:e,i18n:f,trailingSlash:g}=null!=(c=b.nextConfig)?c:{},h={pathname:a,trailingSlash:"/"!==a?a.endsWith("/"):g};e&&D(h.pathname,e)&&(h.pathname=function(a,b){if(!D(a,b))return a;let c=a.slice(b.length);return c.startsWith("/")?c:"/"+c}(h.pathname,e),h.basePath=e);let i=h.pathname;if(h.pathname.startsWith("/_next/data/")&&h.pathname.endsWith(".json")){let a=h.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");h.buildId=a[0],i="index"!==a[1]?"/"+a.slice(1).join("/"):"/",!0===b.parseData&&(h.pathname=i)}if(f){let a=b.i18nProvider?b.i18nProvider.analyze(h.pathname):F(h.pathname,f.locales);h.locale=a.detectedLocale,h.pathname=null!=(d=a.pathname)?d:h.pathname,!a.detectedLocale&&h.buildId&&(a=b.i18nProvider?b.i18nProvider.analyze(i):F(i,f.locales)).detectedLocale&&(h.locale=a.detectedLocale)}return h}(this[I].url.pathname,{nextConfig:this[I].options.nextConfig,parseData:!0,i18nProvider:this[I].options.i18nProvider}),g=function(a,b){let c;if((null==b?void 0:b.host)&&!Array.isArray(b.host))c=b.host.toString().split(":",1)[0];else{if(!a.hostname)return;c=a.hostname}return c.toLowerCase()}(this[I].url,this[I].options.headers);this[I].domainLocale=this[I].options.i18nProvider?this[I].options.i18nProvider.detectDomainLocale(g):function(a,b,c){if(a)for(let f of(c&&(c=c.toLowerCase()),a)){var d,e;if(b===(null==(d=f.domain)?void 0:d.split(":",1)[0].toLowerCase())||c===f.defaultLocale.toLowerCase()||(null==(e=f.locales)?void 0:e.some(a=>a.toLowerCase()===c)))return f}}(null==(b=this[I].options.nextConfig)||null==(a=b.i18n)?void 0:a.domains,g);let h=(null==(c=this[I].domainLocale)?void 0:c.defaultLocale)||(null==(e=this[I].options.nextConfig)||null==(d=e.i18n)?void 0:d.defaultLocale);this[I].url.pathname=f.pathname,this[I].defaultLocale=h,this[I].basePath=f.basePath??"",this[I].buildId=f.buildId,this[I].locale=f.locale??h,this[I].trailingSlash=f.trailingSlash}formatPathname(){var a;let b;return b=function(a,b,c,d){if(!b||b===c)return a;let e=a.toLowerCase();return!d&&(D(e,"/api")||D(e,"/"+b.toLowerCase()))?a:B(a,"/"+b)}((a={basePath:this[I].basePath,buildId:this[I].buildId,defaultLocale:this[I].options.forceLocale?void 0:this[I].defaultLocale,locale:this[I].locale,pathname:this[I].url.pathname,trailingSlash:this[I].trailingSlash}).pathname,a.locale,a.buildId?void 0:a.defaultLocale,a.ignorePrefix),(a.buildId||!a.trailingSlash)&&(b=z(b)),a.buildId&&(b=C(B(b,"/_next/data/"+a.buildId),"/"===a.pathname?"index.json":".json")),b=B(b,a.basePath),!a.buildId&&a.trailingSlash?b.endsWith("/")?b:C(b,"/"):z(b)}formatSearch(){return this[I].url.search}get buildId(){return this[I].buildId}set buildId(a){this[I].buildId=a}get locale(){return this[I].locale??""}set locale(a){var b,c;if(!this[I].locale||!(null==(c=this[I].options.nextConfig)||null==(b=c.i18n)?void 0:b.locales.includes(a)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${a}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[I].locale=a}get defaultLocale(){return this[I].defaultLocale}get domainLocale(){return this[I].domainLocale}get searchParams(){return this[I].url.searchParams}get host(){return this[I].url.host}set host(a){this[I].url.host=a}get hostname(){return this[I].url.hostname}set hostname(a){this[I].url.hostname=a}get port(){return this[I].url.port}set port(a){this[I].url.port=a}get protocol(){return this[I].url.protocol}set protocol(a){this[I].url.protocol=a}get href(){let a=this.formatPathname(),b=this.formatSearch();return`${this.protocol}//${this.host}${a}${b}${this.hash}`}set href(a){this[I].url=H(a),this.analyze()}get origin(){return this[I].url.origin}get pathname(){return this[I].url.pathname}set pathname(a){this[I].url.pathname=a}get hash(){return this[I].url.hash}set hash(a){this[I].url.hash=a}get search(){return this[I].url.search}set search(a){this[I].url.search=a}get password(){return this[I].url.password}set password(a){this[I].url.password=a}get username(){return this[I].url.username}set username(a){this[I].url.username=a}get basePath(){return this[I].basePath}set basePath(a){this[I].basePath=a.startsWith("/")?a:`/${a}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new J(String(this),this[I].options)}}var K=c(724);let L=Symbol("internal request");class M extends Request{constructor(a,b={}){let c="string"!=typeof a&&"url"in a?a.url:String(a);t(c),a instanceof Request?super(a,b):super(c,b);let d=new J(c,{headers:s(this.headers),nextConfig:b.nextConfig});this[L]={cookies:new K.RequestCookies(this.headers),nextUrl:d,url:d.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[L].cookies}get nextUrl(){return this[L].nextUrl}get page(){throw new n}get ua(){throw new o}get url(){return this[L].url}}class N{static get(a,b,c){let d=Reflect.get(a,b,c);return"function"==typeof d?d.bind(a):d}static set(a,b,c,d){return Reflect.set(a,b,c,d)}static has(a,b){return Reflect.has(a,b)}static deleteProperty(a,b){return Reflect.deleteProperty(a,b)}}let O=Symbol("internal response"),P=new Set([301,302,303,307,308]);function Q(a,b){var c;if(null==a||null==(c=a.request)?void 0:c.headers){if(!(a.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let c=[];for(let[d,e]of a.request.headers)b.set("x-middleware-request-"+d,e),c.push(d);b.set("x-middleware-override-headers",c.join(","))}}class R extends Response{constructor(a,b={}){super(a,b);let c=this.headers,d=new Proxy(new K.ResponseCookies(c),{get(a,d,e){switch(d){case"delete":case"set":return(...e)=>{let f=Reflect.apply(a[d],a,e),g=new Headers(c);return f instanceof K.ResponseCookies&&c.set("x-middleware-set-cookie",f.getAll().map(a=>(0,K.stringifyCookie)(a)).join(",")),Q(b,g),f};default:return N.get(a,d,e)}}});this[O]={cookies:d,url:b.url?new J(b.url,{headers:s(c),nextConfig:b.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[O].cookies}static json(a,b){let c=Response.json(a,b);return new R(c.body,c)}static redirect(a,b){let c="number"==typeof b?b:(null==b?void 0:b.status)??307;if(!P.has(c))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let d="object"==typeof b?b:{},e=new Headers(null==d?void 0:d.headers);return e.set("Location",t(a)),new R(null,{...d,headers:e,status:c})}static rewrite(a,b){let c=new Headers(null==b?void 0:b.headers);return c.set("x-middleware-rewrite",t(a)),Q(b,c),new R(null,{...b,headers:c})}static next(a){let b=new Headers(null==a?void 0:a.headers);return b.set("x-middleware-next","1"),Q(a,b),new R(null,{...a,headers:b})}}function S(a,b){let c="string"==typeof b?new URL(b):b,d=new URL(a,b),e=d.origin===c.origin;return{url:e?d.toString().slice(c.origin.length):d.toString(),isRelative:e}}let T="Next-Router-Prefetch",U=["RSC","Next-Router-State-Tree",T,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"];class V extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new V}}class W extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,c,d){if("symbol"==typeof c)return N.get(b,c,d);let e=c.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);if(void 0!==f)return N.get(b,f,d)},set(b,c,d,e){if("symbol"==typeof c)return N.set(b,c,d,e);let f=c.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);return N.set(b,g??c,d,e)},has(b,c){if("symbol"==typeof c)return N.has(b,c);let d=c.toLowerCase(),e=Object.keys(a).find(a=>a.toLowerCase()===d);return void 0!==e&&N.has(b,e)},deleteProperty(b,c){if("symbol"==typeof c)return N.deleteProperty(b,c);let d=c.toLowerCase(),e=Object.keys(a).find(a=>a.toLowerCase()===d);return void 0===e||N.deleteProperty(b,e)}})}static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"append":case"delete":case"set":return V.callable;default:return N.get(a,b,c)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new W(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}let X=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class Y{disable(){throw X}getStore(){}run(){throw X}exit(){throw X}enterWith(){throw X}static bind(a){return a}}let Z="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function $(){return Z?new Z:new Y}let _=$(),aa=$();class ab extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ab}}class ac{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return ab.callable;default:return N.get(a,b,c)}}})}}let ad=Symbol.for("next.mutated.cookies");class ae{static wrap(a,b){let c=new K.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let d=[],e=new Set,f=()=>{let a=_.getStore();if(a&&(a.pathWasRevalidated=!0),d=c.getAll().filter(a=>e.has(a.name)),b){let a=[];for(let b of d){let c=new K.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},g=new Proxy(c,{get(a,b,c){switch(b){case ad:return d;case"delete":return function(...b){e.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),g}finally{f()}};case"set":return function(...b){e.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),g}finally{f()}};default:return N.get(a,b,c)}}});return g}}function af(a){if("action"!==function(a){let b=aa.getStore();switch(!b&&function(a){throw Object.defineProperty(Error(`\`${a}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(a),b.type){case"request":default:return b;case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(a).phase)throw new ab}var ag=function(a){return a.handleRequest="BaseServer.handleRequest",a.run="BaseServer.run",a.pipe="BaseServer.pipe",a.getStaticHTML="BaseServer.getStaticHTML",a.render="BaseServer.render",a.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",a.renderToResponse="BaseServer.renderToResponse",a.renderToHTML="BaseServer.renderToHTML",a.renderError="BaseServer.renderError",a.renderErrorToResponse="BaseServer.renderErrorToResponse",a.renderErrorToHTML="BaseServer.renderErrorToHTML",a.render404="BaseServer.render404",a}(ag||{}),ah=function(a){return a.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",a.loadComponents="LoadComponents.loadComponents",a}(ah||{}),ai=function(a){return a.getRequestHandler="NextServer.getRequestHandler",a.getServer="NextServer.getServer",a.getServerRequestHandler="NextServer.getServerRequestHandler",a.createServer="createServer.createServer",a}(ai||{}),aj=function(a){return a.compression="NextNodeServer.compression",a.getBuildId="NextNodeServer.getBuildId",a.createComponentTree="NextNodeServer.createComponentTree",a.clientComponentLoading="NextNodeServer.clientComponentLoading",a.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",a.generateStaticRoutes="NextNodeServer.generateStaticRoutes",a.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",a.generatePublicRoutes="NextNodeServer.generatePublicRoutes",a.generateImageRoutes="NextNodeServer.generateImageRoutes.route",a.sendRenderResult="NextNodeServer.sendRenderResult",a.proxyRequest="NextNodeServer.proxyRequest",a.runApi="NextNodeServer.runApi",a.render="NextNodeServer.render",a.renderHTML="NextNodeServer.renderHTML",a.imageOptimizer="NextNodeServer.imageOptimizer",a.getPagePath="NextNodeServer.getPagePath",a.getRoutesManifest="NextNodeServer.getRoutesManifest",a.findPageComponents="NextNodeServer.findPageComponents",a.getFontManifest="NextNodeServer.getFontManifest",a.getServerComponentManifest="NextNodeServer.getServerComponentManifest",a.getRequestHandler="NextNodeServer.getRequestHandler",a.renderToHTML="NextNodeServer.renderToHTML",a.renderError="NextNodeServer.renderError",a.renderErrorToHTML="NextNodeServer.renderErrorToHTML",a.render404="NextNodeServer.render404",a.startResponse="NextNodeServer.startResponse",a.route="route",a.onProxyReq="onProxyReq",a.apiResolver="apiResolver",a.internalFetch="internalFetch",a}(aj||{}),ak=function(a){return a.startServer="startServer.startServer",a}(ak||{}),al=function(a){return a.getServerSideProps="Render.getServerSideProps",a.getStaticProps="Render.getStaticProps",a.renderToString="Render.renderToString",a.renderDocument="Render.renderDocument",a.createBodyResult="Render.createBodyResult",a}(al||{}),am=function(a){return a.renderToString="AppRender.renderToString",a.renderToReadableStream="AppRender.renderToReadableStream",a.getBodyResult="AppRender.getBodyResult",a.fetch="AppRender.fetch",a}(am||{}),an=function(a){return a.executeRoute="Router.executeRoute",a}(an||{}),ao=function(a){return a.runHandler="Node.runHandler",a}(ao||{}),ap=function(a){return a.runHandler="AppRouteRouteHandlers.runHandler",a}(ap||{}),aq=function(a){return a.generateMetadata="ResolveMetadata.generateMetadata",a.generateViewport="ResolveMetadata.generateViewport",a}(aq||{}),ar=function(a){return a.execute="Middleware.execute",a}(ar||{});let as=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],at=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function au(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}let{context:av,propagation:aw,trace:ax,SpanStatusCode:ay,SpanKind:az,ROOT_CONTEXT:aA}=d=c(956);class aB extends Error{constructor(a,b){super(),this.bubble=a,this.result=b}}let aC=(a,b)=>{(function(a){return"object"==typeof a&&null!==a&&a instanceof aB})(b)&&b.bubble?a.setAttribute("next.bubble",!0):(b&&a.recordException(b),a.setStatus({code:ay.ERROR,message:null==b?void 0:b.message})),a.end()},aD=new Map,aE=d.createContextKey("next.rootSpanId"),aF=0,aG={set(a,b,c){a.push({key:b,value:c})}};class aH{getTracerInstance(){return ax.getTracer("next.js","0.0.1")}getContext(){return av}getTracePropagationData(){let a=av.active(),b=[];return aw.inject(a,b,aG),b}getActiveScopeSpan(){return ax.getSpan(null==av?void 0:av.active())}withPropagatedContext(a,b,c){let d=av.active();if(ax.getSpanContext(d))return b();let e=aw.extract(d,a,c);return av.with(e,b)}trace(...a){var b;let[c,d,e]=a,{fn:f,options:g}="function"==typeof d?{fn:d,options:{}}:{fn:e,options:{...d}},h=g.spanName??c;if(!as.includes(c)&&"1"!==process.env.NEXT_OTEL_VERBOSE||g.hideSpan)return f();let i=this.getSpanContext((null==g?void 0:g.parentSpan)??this.getActiveScopeSpan()),j=!1;i?(null==(b=ax.getSpanContext(i))?void 0:b.isRemote)&&(j=!0):(i=(null==av?void 0:av.active())??aA,j=!0);let k=aF++;return g.attributes={"next.span_name":h,"next.span_type":c,...g.attributes},av.with(i.setValue(aE,k),()=>this.getTracerInstance().startActiveSpan(h,g,a=>{let b="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,d=()=>{aD.delete(k),b&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&at.includes(c||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(c.split(".").pop()||"").replace(/[A-Z]/g,a=>"-"+a.toLowerCase())}`,{start:b,end:performance.now()})};j&&aD.set(k,new Map(Object.entries(g.attributes??{})));try{if(f.length>1)return f(a,b=>aC(a,b));let b=f(a);if(au(b))return b.then(b=>(a.end(),b)).catch(b=>{throw aC(a,b),b}).finally(d);return a.end(),d(),b}catch(b){throw aC(a,b),d(),b}}))}wrap(...a){let b=this,[c,d,e]=3===a.length?a:[a[0],{},a[1]];return as.includes(c)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let a=d;"function"==typeof a&&"function"==typeof e&&(a=a.apply(this,arguments));let f=arguments.length-1,g=arguments[f];if("function"!=typeof g)return b.trace(c,a,()=>e.apply(this,arguments));{let d=b.getContext().bind(av.active(),g);return b.trace(c,a,(a,b)=>(arguments[f]=function(a){return null==b||b(a),d.apply(this,arguments)},e.apply(this,arguments)))}}:e}startSpan(...a){let[b,c]=a,d=this.getSpanContext((null==c?void 0:c.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(b,c,d)}getSpanContext(a){return a?ax.setSpan(av.active(),a):void 0}getRootSpanAttributes(){let a=av.active().getValue(aE);return aD.get(a)}setRootSpanAttribute(a,b){let c=av.active().getValue(aE),d=aD.get(c);d&&d.set(a,b)}}let aI=(()=>{let a=new aH;return()=>a})(),aJ="__prerender_bypass";Symbol("__next_preview_data"),Symbol(aJ);class aK{constructor(a,b,c,d){var e;let f=a&&function(a,b){let c=W.from(a.headers);return{isOnDemandRevalidate:c.get("x-prerender-revalidate")===b.previewModeId,revalidateOnlyGenerated:c.has("x-prerender-revalidate-if-generated")}}(b,a).isOnDemandRevalidate,g=null==(e=c.get(aJ))?void 0:e.value;this._isEnabled=!!(!f&&g&&a&&g===a.previewModeId),this._previewModeId=null==a?void 0:a.previewModeId,this._mutableCookies=d}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:aJ,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:aJ,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function aL(a,b){if("x-middleware-set-cookie"in a.headers&&"string"==typeof a.headers["x-middleware-set-cookie"]){let c=a.headers["x-middleware-set-cookie"],d=new Headers;for(let a of r(c))d.append("set-cookie",a);for(let a of new K.ResponseCookies(d).getAll())b.set(a)}}var aM=c(802),aN=c.n(aM);class aO extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}class aP{constructor(a,b){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=a,this.calculateSize=b||(()=>1)}set(a,b){if(!a||!b)return;let c=this.calculateSize(b);if(c>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(a)&&(this.totalSize-=this.sizes.get(a)||0),this.cache.set(a,b),this.sizes.set(a,c),this.totalSize+=c,this.touch(a)}has(a){return!!a&&(this.touch(a),!!this.cache.get(a))}get(a){if(!a)return;let b=this.cache.get(a);if(void 0!==b)return this.touch(a),b}touch(a){let b=this.cache.get(a);void 0!==b&&(this.cache.delete(a),this.cache.set(a,b),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let a=this.cache.keys().next().value;if(void 0!==a){let b=this.sizes.get(a)||0;this.totalSize-=b,this.cache.delete(a),this.sizes.delete(a)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(a){this.cache.has(a)&&(this.totalSize-=this.sizes.get(a)||0,this.cache.delete(a),this.sizes.delete(a))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}c(356).Buffer,new aP(0x3200000,a=>a.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE&&((a,...b)=>{console.log(`use-cache: ${a}`,...b)}),Symbol.for("@next/cache-handlers");let aQ=Symbol.for("@next/cache-handlers-map"),aR=Symbol.for("@next/cache-handlers-set"),aS=globalThis;function aT(){if(aS[aQ])return aS[aQ].entries()}async function aU(a,b){if(!a)return b();let c=aV(a);try{return await b()}finally{let b=function(a,b){let c=new Set(a.pendingRevalidatedTags),d=new Set(a.pendingRevalidateWrites);return{pendingRevalidatedTags:b.pendingRevalidatedTags.filter(a=>!c.has(a)),pendingRevalidates:Object.fromEntries(Object.entries(b.pendingRevalidates).filter(([b])=>!(b in a.pendingRevalidates))),pendingRevalidateWrites:b.pendingRevalidateWrites.filter(a=>!d.has(a))}}(c,aV(a));await aX(a,b)}}function aV(a){return{pendingRevalidatedTags:a.pendingRevalidatedTags?[...a.pendingRevalidatedTags]:[],pendingRevalidates:{...a.pendingRevalidates},pendingRevalidateWrites:a.pendingRevalidateWrites?[...a.pendingRevalidateWrites]:[]}}async function aW(a,b){if(0===a.length)return;let c=[];b&&c.push(b.revalidateTag(a));let d=function(){if(aS[aR])return aS[aR].values()}();if(d)for(let b of d)c.push(b.expireTags(...a));await Promise.all(c)}async function aX(a,b){let c=(null==b?void 0:b.pendingRevalidatedTags)??a.pendingRevalidatedTags??[],d=(null==b?void 0:b.pendingRevalidates)??a.pendingRevalidates??{},e=(null==b?void 0:b.pendingRevalidateWrites)??a.pendingRevalidateWrites??[];return Promise.all([aW(c,a.incrementalCache),...Object.values(d),...e])}let aY=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class aZ{disable(){throw aY}getStore(){}run(){throw aY}exit(){throw aY}enterWith(){throw aY}static bind(a){return a}}let a$="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,a_=a$?new a$:new aZ;class a0{constructor({waitUntil:a,onClose:b,onTaskError:c}){this.workUnitStores=new Set,this.waitUntil=a,this.onClose=b,this.onTaskError=c,this.callbackQueue=new(aN()),this.callbackQueue.pause()}after(a){if(au(a))this.waitUntil||a1(),this.waitUntil(a.catch(a=>this.reportTaskError("promise",a)));else if("function"==typeof a)this.addCallback(a);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(a){var b;this.waitUntil||a1();let c=aa.getStore();c&&this.workUnitStores.add(c);let d=a_.getStore(),e=d?d.rootTaskSpawnPhase:null==c?void 0:c.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let f=(b=async()=>{try{await a_.run({rootTaskSpawnPhase:e},()=>a())}catch(a){this.reportTaskError("function",a)}},a$?a$.bind(b):aZ.bind(b));this.callbackQueue.add(f)}async runCallbacksOnClose(){return await new Promise(a=>this.onClose(a)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let a of this.workUnitStores)a.phase="after";let a=_.getStore();if(!a)throw Object.defineProperty(new aO("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return aU(a,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(a,b){if(console.error("promise"===a?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",b),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,b)}catch(a){console.error(Object.defineProperty(new aO("`onTaskError` threw while handling an error thrown from an `after` task",{cause:a}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function a1(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function a2(a){let b,c={then:(d,e)=>(b||(b=a()),b.then(a=>{c.value=a}).catch(()=>{}),b.then(d,e))};return c}class a3{onClose(a){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",a),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function a4(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID||"",previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let a5=Symbol.for("@next/request-context");async function a6(a,b,c){let d=[],e=c&&c.size>0;for(let b of(a=>{let b=["/layout"];if(a.startsWith("/")){let c=a.split("/");for(let a=1;a<c.length+1;a++){let d=c.slice(0,a).join("/");d&&(d.endsWith("/page")||d.endsWith("/route")||(d=`${d}${!d.endsWith("/")?"/":""}layout`),b.push(d))}}return b})(a))b=`${p}${b}`,d.push(b);if(b.pathname&&!e){let a=`${p}${b.pathname}`;d.push(a)}return{tags:d,expirationsByCacheKind:function(a){let b=new Map,c=aT();if(c)for(let[d,e]of c)"getExpiration"in e&&b.set(d,a2(async()=>e.getExpiration(...a)));return b}(d)}}class a7 extends M{constructor(a){super(a.input,a.init),this.sourcePage=a.page}get request(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let a8={keys:a=>Array.from(a.keys()),get:(a,b)=>a.get(b)??void 0},a9=(a,b)=>aI().withPropagatedContext(a.headers,b,a8),ba=!1;async function bb(a){var b;let d,e;if(!ba&&(ba=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:a,wrapRequestHandler:b}=c(905);a(),a9=b(a9)}await k();let f=void 0!==globalThis.__BUILD_MANIFEST;a.request.url=a.request.url.replace(/\.rsc($|\?)/,"$1");let g=a.bypassNextUrl?new URL(a.request.url):new J(a.request.url,{headers:a.request.headers,nextConfig:a.request.nextConfig});for(let a of[...g.searchParams.keys()]){let b=g.searchParams.getAll(a),c=function(a){for(let b of["nxtP","nxtI"])if(a!==b&&a.startsWith(b))return a.substring(b.length);return null}(a);if(c){for(let a of(g.searchParams.delete(c),b))g.searchParams.append(c,a);g.searchParams.delete(a)}}let h=process.env.__NEXT_BUILD_ID||"";"buildId"in g&&(h=g.buildId||"",g.buildId="");let i=function(a){let b=new Headers;for(let[c,d]of Object.entries(a))for(let a of Array.isArray(d)?d:[d])void 0!==a&&("number"==typeof a&&(a=a.toString()),b.append(c,a));return b}(a.request.headers),j=i.has("x-nextjs-data"),l="1"===i.get("RSC");j&&"/index"===g.pathname&&(g.pathname="/");let m=new Map;if(!f)for(let a of U){let b=a.toLowerCase(),c=i.get(b);null!==c&&(m.set(b,c),i.delete(b))}let n=new a7({page:a.page,input:(function(a){let b="string"==typeof a,c=b?new URL(a):a;return c.searchParams.delete("_rsc"),b?c.toString():c})(g).toString(),init:{body:a.request.body,headers:i,method:a.request.method,nextConfig:a.request.nextConfig,signal:a.request.signal}});j&&Object.defineProperty(n,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&a.IncrementalCache&&(globalThis.__incrementalCache=new a.IncrementalCache({CurCacheHandler:a.incrementalCacheHandler,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:a.request.headers,getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:a4()})}));let o=a.request.waitUntil??(null==(b=function(){let a=globalThis[a5];return null==a?void 0:a.get()}())?void 0:b.waitUntil),p=new y({request:n,page:a.page,context:o?{waitUntil:o}:void 0});if((d=await a9(n,()=>{if("/middleware"===a.page||"/src/middleware"===a.page){let b=p.waitUntil.bind(p),c=new a3;return aI().trace(ar.execute,{spanName:`middleware ${n.method} ${n.nextUrl.pathname}`,attributes:{"http.target":n.nextUrl.pathname,"http.method":n.method}},async()=>{try{var d,f,g,i,j,k;let l=a4(),m=await a6("/",n.nextUrl,null),o=(j=n.nextUrl,k=a=>{e=a},function(a,b,c,d,e,f,g,h,i,j,k){function l(a){c&&c.setHeader("Set-Cookie",a)}let m={};return{type:"request",phase:a,implicitTags:f,url:{pathname:d.pathname,search:d.search??""},rootParams:e,get headers(){return m.headers||(m.headers=function(a){let b=W.from(a);for(let a of U)b.delete(a.toLowerCase());return W.seal(b)}(b.headers)),m.headers},get cookies(){if(!m.cookies){let a=new K.RequestCookies(W.from(b.headers));aL(b,a),m.cookies=ac.seal(a)}return m.cookies},set cookies(value){m.cookies=value},get mutableCookies(){if(!m.mutableCookies){let a=function(a,b){let c=new K.RequestCookies(W.from(a));return ae.wrap(c,b)}(b.headers,g||(c?l:void 0));aL(b,a),m.mutableCookies=a}return m.mutableCookies},get userspaceMutableCookies(){return m.userspaceMutableCookies||(m.userspaceMutableCookies=function(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return af("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return af("cookies().set"),a.set(...c),b};default:return N.get(a,c,d)}}});return b}(this.mutableCookies)),m.userspaceMutableCookies},get draftMode(){return m.draftMode||(m.draftMode=new aK(i,b,this.cookies,this.mutableCookies)),m.draftMode},renderResumeDataCache:h??null,isHmrRefresh:j,serverComponentsHmrCache:k||globalThis.__serverComponentsHmrCache}}("action",n,void 0,j,{},m,k,void 0,l,!1,void 0)),q=function({page:a,fallbackRouteParams:b,renderOpts:c,requestEndedState:d,isPrefetchRequest:e,buildId:f,previouslyRevalidatedTags:g}){var h;let i={isStaticGeneration:!c.shouldWaitOnAllReady&&!c.supportsDynamicResponse&&!c.isDraftMode&&!c.isPossibleServerAction,page:a,fallbackRouteParams:b,route:(h=a.split("/").reduce((a,b,c,d)=>b?"("===b[0]&&b.endsWith(")")||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b:a,"")).startsWith("/")?h:"/"+h,incrementalCache:c.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:c.cacheLifeProfiles,isRevalidate:c.isRevalidate,isBuildTimePrerendering:c.nextExport,hasReadableErrorStacks:c.hasReadableErrorStacks,fetchCache:c.fetchCache,isOnDemandRevalidate:c.isOnDemandRevalidate,isDraftMode:c.isDraftMode,requestEndedState:d,isPrefetchRequest:e,buildId:f,reactLoadableManifest:(null==c?void 0:c.reactLoadableManifest)||{},assetPrefix:(null==c?void 0:c.assetPrefix)||"",afterContext:function(a){let{waitUntil:b,onClose:c,onAfterTaskError:d}=a;return new a0({waitUntil:b,onClose:c,onTaskError:d})}(c),dynamicIOEnabled:c.experimental.dynamicIO,dev:c.dev??!1,previouslyRevalidatedTags:g,refreshTagsByCacheKind:function(){let a=new Map,b=aT();if(b)for(let[c,d]of b)"refreshTags"in d&&a.set(c,a2(async()=>d.refreshTags()));return a}(),runInCleanSnapshot:a$?a$.snapshot():function(a,...b){return a(...b)}};return c.store=i,i}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(f=a.request.nextConfig)||null==(d=f.experimental)?void 0:d.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(i=a.request.nextConfig)||null==(g=i.experimental)?void 0:g.authInterrupts)},supportsDynamicResponse:!0,waitUntil:b,onClose:c.onClose.bind(c),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:n.headers.has(T),buildId:h??"",previouslyRevalidatedTags:[]});return await _.run(q,()=>aa.run(o,a.handler,n,p))}finally{setTimeout(()=>{c.dispatchClose()},0)}})}return a.handler(n,p)}))&&!(d instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});d&&e&&d.headers.set("set-cookie",e);let q=null==d?void 0:d.headers.get("x-middleware-rewrite");if(d&&q&&(l||!f)){let b=new J(q,{forceLocale:!0,headers:a.request.headers,nextConfig:a.request.nextConfig});f||b.host!==n.nextUrl.host||(b.buildId=h||b.buildId,d.headers.set("x-middleware-rewrite",String(b)));let{url:c,isRelative:e}=S(b.toString(),g.toString());!f&&j&&d.headers.set("x-nextjs-rewrite",c),l&&e&&(g.pathname!==b.pathname&&d.headers.set("x-nextjs-rewritten-path",b.pathname),g.search!==b.search&&d.headers.set("x-nextjs-rewritten-query",b.search.slice(1)))}let r=null==d?void 0:d.headers.get("Location");if(d&&r&&!f){let b=new J(r,{forceLocale:!1,headers:a.request.headers,nextConfig:a.request.nextConfig});d=new Response(d.body,d),b.host===g.host&&(b.buildId=h||b.buildId,d.headers.set("Location",b.toString())),j&&(d.headers.delete("Location"),d.headers.set("x-nextjs-redirect",S(b.toString(),g.toString()).url))}let s=d||R.next(),t=s.headers.get("x-middleware-override-headers"),u=[];if(t){for(let[a,b]of m)s.headers.set(`x-middleware-request-${a}`,b),u.push(a);u.length>0&&s.headers.set("x-middleware-override-headers",t+","+u.join(","))}return{response:s,waitUntil:("internal"===p[w].kind?Promise.all(p[w].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:n.fetchMetrics}}c(280),"undefined"==typeof URLPattern||URLPattern;var bc=c(815);if(new WeakMap,bc.unstable_postpone,!1===function(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}("Route %%% needs to bail out of prerendering at this point because it used ^^^. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error"))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;var bd=c(991);let be=new Map,bf={rateLimit:{windowMs:9e5,maxRequests:100,apiMaxRequests:50,authMaxRequests:5},blockedIPs:new Set([]),suspiciousPatterns:[/\b(union|select|insert|delete|drop|create|alter|exec|script)\b/i,/<script[^>]*>.*?<\/script>/gi,/javascript:/gi,/on\w+\s*=/gi],protectedRoutes:["/admin","/account","/api/admin","/api/user"],adminRoutes:["/admin","/api/admin"]};async function bg(a){let{pathname:b}=a.nextUrl,c=function(a){let b=a.headers.get("x-forwarded-for"),c=a.headers.get("x-real-ip");return b?b.split(",")[0].trim():c||a.ip||"unknown"}(a);if(.01>Math.random()&&function(){let a=Date.now();for(let[b,c]of be.entries())a>c.resetTime&&be.delete(b)}(),bf.blockedIPs.has(c))return new R("Access Denied",{status:403});if(!function(a){let b=a.url,c=a.headers.get("user-agent")||"";for(let a of bf.suspiciousPatterns)if(a.test(b)||a.test(c))return!1;return!0}(a))return console.warn(`Suspicious request detected from ${c}: ${a.url}`),new R("Bad Request",{status:400});let d=bf.rateLimit.maxRequests;if(b.startsWith("/api/auth")?d=bf.rateLimit.authMaxRequests:b.startsWith("/api")&&(d=bf.rateLimit.apiMaxRequests),!function(a,b,c){let d=Date.now(),e=`${a}:${Math.floor(d/c)}`,f=be.get(e);return f?!(f.count>=b)&&(f.count++,!0):(be.set(e,{count:1,resetTime:d+c}),!0)}(c,d,bf.rateLimit.windowMs))return console.warn(`Rate limit exceeded for ${c}`),new R("Too Many Requests",{status:429,headers:{"Retry-After":"900"}});if(bf.protectedRoutes.some(a=>b.startsWith(a))){let c=await (0,bd.getToken)({req:a,secret:process.env.NEXTAUTH_SECRET});if(!c){let b=new URL("/auth/login",a.url);return b.searchParams.set("callbackUrl",a.url),R.redirect(b)}if(bf.adminRoutes.some(a=>b.startsWith(a))){let a=c.role;if("admin"!==a&&"moderator"!==a)return new R("Forbidden",{status:403})}}let e=R.next();return(e.headers.set("X-DNS-Prefetch-Control","on"),e.headers.set("Strict-Transport-Security","max-age=63072000; includeSubDomains; preload"),e.headers.set("X-XSS-Protection","1; mode=block"),e.headers.set("X-Frame-Options","DENY"),e.headers.set("X-Content-Type-Options","nosniff"),e.headers.set("Referrer-Policy","origin-when-cross-origin"),e.headers.set("Permissions-Policy","camera=(), microphone=(), geolocation=(), payment=()"),e.headers.set("Content-Security-Policy","default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https: wss:; media-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests"),(b.startsWith("/_next/static/")||b.startsWith("/images/")||b.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/))&&e.headers.set("Cache-Control","public, max-age=31536000, immutable"),b.startsWith("/api/")&&(e.headers.set("Access-Control-Allow-Origin",process.env.ALLOWED_ORIGINS||"*"),e.headers.set("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),e.headers.set("Access-Control-Allow-Headers","Content-Type, Authorization"),e.headers.set("Access-Control-Max-Age","86400")),"OPTIONS"===a.method)?new R(null,{status:200}):e}let bh={matcher:["/((?!_next/static|_next/image|favicon.ico|public/).*)"]};Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401});let bi={...e},bj=bi.middleware||bi.default,bk="/src/middleware";if("function"!=typeof bj)throw Object.defineProperty(Error(`The Middleware "${bk}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function bl(a){return bb({...a,page:bk,handler:async(...a)=>{try{return await bj(...a)}catch(e){let b=a[0],c=new URL(b.url),d=c.pathname+c.search;throw await i(e,{path:d,method:b.method,headers:Object.fromEntries(b.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),e}}})}},35:(a,b)=>{"use strict";Symbol.for("react.transitional.element"),Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator;Object.prototype.hasOwnProperty,Object.assign},131:(a,b)=>{"use strict";function c(a,b,c){d(a,b),b.set(a,c)}function d(a,b){if(b.has(a))throw TypeError("Cannot initialize the same private elements twice on an object")}function e(a,b){return a.get(g(a,b))}function f(a,b,c){return a.set(g(a,b),c),c}function g(a,b,c){if("function"==typeof a?a===b:a.has(b))return arguments.length<3?b:c;throw TypeError("Private element is not present on this object")}Object.defineProperty(b,"__esModule",{value:!0}),b.SessionStore=void 0,b.defaultCookies=function(a){let b=a?"__Secure-":"";return{sessionToken:{name:`${b}next-auth.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},callbackUrl:{name:`${b}next-auth.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},csrfToken:{name:`${a?"__Host-":""}next-auth.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},pkceCodeVerifier:{name:`${b}next-auth.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},state:{name:`${b}next-auth.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},nonce:{name:`${b}next-auth.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}}}};var h=new WeakMap,i=new WeakMap,j=new WeakMap,k=new WeakSet;class l{constructor(a,b,g){!function(a,b){d(a,b),b.add(a)}(this,k),c(this,h,{}),c(this,i,void 0),c(this,j,void 0),f(j,this,g),f(i,this,a);let{cookies:l}=b,{name:m}=a;if("function"==typeof(null==l?void 0:l.getAll))for(let{name:a,value:b}of l.getAll())a.startsWith(m)&&(e(h,this)[a]=b);else if(l instanceof Map)for(let a of l.keys())a.startsWith(m)&&(e(h,this)[a]=l.get(a));else for(let a in l)a.startsWith(m)&&(e(h,this)[a]=l[a])}get value(){return Object.keys(e(h,this)).sort((a,b)=>{var c,d;return parseInt(null!=(c=a.split(".").pop())?c:"0")-parseInt(null!=(d=b.split(".").pop())?d:"0")}).map(a=>e(h,this)[a]).join("")}chunk(a,b){let c=g(k,this,n).call(this);for(let d of g(k,this,m).call(this,{name:e(i,this).name,value:a,options:{...e(i,this).options,...b}}))c[d.name]=d;return Object.values(c)}clean(){return Object.values(g(k,this,n).call(this))}}function m(a){let b=Math.ceil(a.value.length/3933);if(1===b)return e(h,this)[a.name]=a.value,[a];let c=[];for(let d=0;d<b;d++){let b=`${a.name}.${d}`,f=a.value.substr(3933*d,3933);c.push({...a,name:b,value:f}),e(h,this)[b]=f}return e(j,this).debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:163,valueSize:a.value.length,chunks:c.map(a=>a.value.length+163)}),c}function n(){let a={};for(let c in e(h,this)){var b;null==(b=e(h,this))||delete b[c],a[c]={name:c,value:"",options:{...e(i,this).options,maxAge:0}}}return a}b.SessionStore=l},201:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getTestReqInfo:function(){return g},withRequest:function(){return f}});let d=new(c(521)).AsyncLocalStorage;function e(a,b){let c=b.header(a,"next-test-proxy-port");if(!c)return;let d=b.url(a);return{url:d,proxyPort:Number(c),testData:b.header(a,"next-test-data")||""}}function f(a,b,c){let f=e(a,b);return f?d.run(f,c):c()}function g(a,b){let c=d.getStore();return c||(a&&b?e(a,b):void 0)}},280:(a,b,c)=>{var d;(()=>{var e={226:function(e,f){!function(g,h){"use strict";var i="function",j="undefined",k="object",l="string",m="major",n="model",o="name",p="type",q="vendor",r="version",s="architecture",t="console",u="mobile",v="tablet",w="smarttv",x="wearable",y="embedded",z="Amazon",A="Apple",B="ASUS",C="BlackBerry",D="Browser",E="Chrome",F="Firefox",G="Google",H="Huawei",I="Microsoft",J="Motorola",K="Opera",L="Samsung",M="Sharp",N="Sony",O="Xiaomi",P="Zebra",Q="Facebook",R="Chromium OS",S="Mac OS",T=function(a,b){var c={};for(var d in a)b[d]&&b[d].length%2==0?c[d]=b[d].concat(a[d]):c[d]=a[d];return c},U=function(a){for(var b={},c=0;c<a.length;c++)b[a[c].toUpperCase()]=a[c];return b},V=function(a,b){return typeof a===l&&-1!==W(b).indexOf(W(a))},W=function(a){return a.toLowerCase()},X=function(a,b){if(typeof a===l)return a=a.replace(/^\s\s*/,""),typeof b===j?a:a.substring(0,350)},Y=function(a,b){for(var c,d,e,f,g,j,l=0;l<b.length&&!g;){var m=b[l],n=b[l+1];for(c=d=0;c<m.length&&!g&&m[c];)if(g=m[c++].exec(a))for(e=0;e<n.length;e++)j=g[++d],typeof(f=n[e])===k&&f.length>0?2===f.length?typeof f[1]==i?this[f[0]]=f[1].call(this,j):this[f[0]]=f[1]:3===f.length?typeof f[1]!==i||f[1].exec&&f[1].test?this[f[0]]=j?j.replace(f[1],f[2]):void 0:this[f[0]]=j?f[1].call(this,j,f[2]):void 0:4===f.length&&(this[f[0]]=j?f[3].call(this,j.replace(f[1],f[2])):h):this[f]=j||h;l+=2}},Z=function(a,b){for(var c in b)if(typeof b[c]===k&&b[c].length>0){for(var d=0;d<b[c].length;d++)if(V(b[c][d],a))return"?"===c?h:c}else if(V(b[c],a))return"?"===c?h:c;return a},$={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},_={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r,[o,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r,[o,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[o,r],[/opios[\/ ]+([\w\.]+)/i],[r,[o,K+" Mini"]],[/\bopr\/([\w\.]+)/i],[r,[o,K]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[o,r],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[r,[o,"UC"+D]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[r,[o,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r,[o,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r,[o,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[r,[o,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[r,[o,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o,/(.+)/,"$1 Secure "+D],r],[/\bfocus\/([\w\.]+)/i],[r,[o,F+" Focus"]],[/\bopt\/([\w\.]+)/i],[r,[o,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[r,[o,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r,[o,"Dolphin"]],[/coast\/([\w\.]+)/i],[r,[o,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[r,[o,"MIUI "+D]],[/fxios\/([-\w\.]+)/i],[r,[o,F]],[/\bqihu|(qi?ho?o?|360)browser/i],[[o,"360 "+D]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[o,/(.+)/,"$1 "+D],r],[/(comodo_dragon)\/([\w\.]+)/i],[[o,/_/g," "],r],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[o,r],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[o],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[o,Q],r],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[o,r],[/\bgsa\/([\w\.]+) .*safari\//i],[r,[o,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[r,[o,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[r,[o,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[o,E+" WebView"],r],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[r,[o,"Android "+D]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[o,r],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[r,[o,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[r,o],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[o,[r,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[o,r],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[o,"Netscape"],r],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[r,[o,F+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[o,r],[/(cobalt)\/([\w\.]+)/i],[o,[r,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[s,"amd64"]],[/(ia32(?=;))/i],[[s,W]],[/((?:i[346]|x)86)[;\)]/i],[[s,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[s,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[s,"armhf"]],[/windows (ce|mobile); ppc;/i],[[s,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[s,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[s,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[s,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[n,[q,L],[p,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[n,[q,L],[p,u]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[n,[q,A],[p,u]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[n,[q,A],[p,v]],[/(macintosh);/i],[n,[q,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[n,[q,M],[p,u]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[n,[q,H],[p,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[n,[q,H],[p,u]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,u]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[n,[q,"OPPO"],[p,u]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[n,[q,"Vivo"],[p,u]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[n,[q,"Realme"],[p,u]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[n,[q,J],[p,u]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[n,[q,J],[p,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[n,[q,"LG"],[p,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[n,[q,"LG"],[p,u]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[n,[q,"Lenovo"],[p,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[n,/_/g," "],[q,"Nokia"],[p,u]],[/(pixel c)\b/i],[n,[q,G],[p,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[n,[q,G],[p,u]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[n,[q,N],[p,u]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[n,"Xperia Tablet"],[q,N],[p,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[n,[q,"OnePlus"],[p,u]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[n,[q,z],[p,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[n,/(.+)/g,"Fire Phone $1"],[q,z],[p,u]],[/(playbook);[-\w\),; ]+(rim)/i],[n,q,[p,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[n,[q,C],[p,u]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[n,[q,B],[p,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[n,[q,B],[p,u]],[/(nexus 9)/i],[n,[q,"HTC"],[p,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[q,[n,/_/g," "],[p,u]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[n,[q,"Acer"],[p,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[n,[q,"Meizu"],[p,u]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[q,n,[p,u]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[q,n,[p,v]],[/(surface duo)/i],[n,[q,I],[p,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[n,[q,"Fairphone"],[p,u]],[/(u304aa)/i],[n,[q,"AT&T"],[p,u]],[/\bsie-(\w*)/i],[n,[q,"Siemens"],[p,u]],[/\b(rct\w+) b/i],[n,[q,"RCA"],[p,v]],[/\b(venue[\d ]{2,7}) b/i],[n,[q,"Dell"],[p,v]],[/\b(q(?:mv|ta)\w+) b/i],[n,[q,"Verizon"],[p,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[n,[q,"Barnes & Noble"],[p,v]],[/\b(tm\d{3}\w+) b/i],[n,[q,"NuVision"],[p,v]],[/\b(k88) b/i],[n,[q,"ZTE"],[p,v]],[/\b(nx\d{3}j) b/i],[n,[q,"ZTE"],[p,u]],[/\b(gen\d{3}) b.+49h/i],[n,[q,"Swiss"],[p,u]],[/\b(zur\d{3}) b/i],[n,[q,"Swiss"],[p,v]],[/\b((zeki)?tb.*\b) b/i],[n,[q,"Zeki"],[p,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[q,"Dragon Touch"],n,[p,v]],[/\b(ns-?\w{0,9}) b/i],[n,[q,"Insignia"],[p,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[n,[q,"NextBook"],[p,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[q,"Voice"],n,[p,u]],[/\b(lvtel\-)?(v1[12]) b/i],[[q,"LvTel"],n,[p,u]],[/\b(ph-1) /i],[n,[q,"Essential"],[p,u]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[n,[q,"Envizen"],[p,v]],[/\b(trio[-\w\. ]+) b/i],[n,[q,"MachSpeed"],[p,v]],[/\btu_(1491) b/i],[n,[q,"Rotor"],[p,v]],[/(shield[\w ]+) b/i],[n,[q,"Nvidia"],[p,v]],[/(sprint) (\w+)/i],[q,n,[p,u]],[/(kin\.[onetw]{3})/i],[[n,/\./g," "],[q,I],[p,u]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[n,[q,P],[p,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[n,[q,P],[p,u]],[/smart-tv.+(samsung)/i],[q,[p,w]],[/hbbtv.+maple;(\d+)/i],[[n,/^/,"SmartTV"],[q,L],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[q,"LG"],[p,w]],[/(apple) ?tv/i],[q,[n,A+" TV"],[p,w]],[/crkey/i],[[n,E+"cast"],[q,G],[p,w]],[/droid.+aft(\w)( bui|\))/i],[n,[q,z],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[n,[q,M],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[n,[q,N],[p,w]],[/(mitv-\w{5}) bui/i],[n,[q,O],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[q,n,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[q,X],[n,X],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[q,n,[p,t]],[/droid.+; (shield) bui/i],[n,[q,"Nvidia"],[p,t]],[/(playstation [345portablevi]+)/i],[n,[q,N],[p,t]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[n,[q,I],[p,t]],[/((pebble))app/i],[q,n,[p,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[n,[q,A],[p,x]],[/droid.+; (glass) \d/i],[n,[q,G],[p,x]],[/droid.+; (wt63?0{2,3})\)/i],[n,[q,P],[p,x]],[/(quest( 2| pro)?)/i],[n,[q,Q],[p,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[q,[p,y]],[/(aeobc)\b/i],[n,[q,z],[p,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[n,[p,u]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[n,[p,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,u]],[/(android[-\w\. ]{0,9});.+buil/i],[n,[q,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[r,[o,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r,[o,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[o,r],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r,o]],os:[[/microsoft (windows) (vista|xp)/i],[o,r],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[o,[r,Z,$]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,"Windows"],[r,Z,$]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[r,/_/g,"."],[o,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[o,S],[r,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[r,o],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[o,r],[/\(bb(10);/i],[r,[o,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[r,[o,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[r,[o,F+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r,[o,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[r,[o,"watchOS"]],[/crkey\/([\d\.]+)/i],[r,[o,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[o,R],r],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[o,r],[/(sunos) ?([\w\.\d]*)/i],[[o,"Solaris"],r],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[o,r]]},aa=function(a,b){if(typeof a===k&&(b=a,a=h),!(this instanceof aa))return new aa(a,b).getResult();var c=typeof g!==j&&g.navigator?g.navigator:h,d=a||(c&&c.userAgent?c.userAgent:""),e=c&&c.userAgentData?c.userAgentData:h,f=b?T(_,b):_,t=c&&c.userAgent==d;return this.getBrowser=function(){var a,b={};return b[o]=h,b[r]=h,Y.call(b,d,f.browser),b[m]=typeof(a=b[r])===l?a.replace(/[^\d\.]/g,"").split(".")[0]:h,t&&c&&c.brave&&typeof c.brave.isBrave==i&&(b[o]="Brave"),b},this.getCPU=function(){var a={};return a[s]=h,Y.call(a,d,f.cpu),a},this.getDevice=function(){var a={};return a[q]=h,a[n]=h,a[p]=h,Y.call(a,d,f.device),t&&!a[p]&&e&&e.mobile&&(a[p]=u),t&&"Macintosh"==a[n]&&c&&typeof c.standalone!==j&&c.maxTouchPoints&&c.maxTouchPoints>2&&(a[n]="iPad",a[p]=v),a},this.getEngine=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.engine),a},this.getOS=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.os),t&&!a[o]&&e&&"Unknown"!=e.platform&&(a[o]=e.platform.replace(/chrome os/i,R).replace(/macos/i,S)),a},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return d},this.setUA=function(a){return d=typeof a===l&&a.length>350?X(a,350):a,this},this.setUA(d),this};aa.VERSION="1.0.35",aa.BROWSER=U([o,r,m]),aa.CPU=U([s]),aa.DEVICE=U([n,q,p,t,u,w,v,x,y]),aa.ENGINE=aa.OS=U([o,r]),typeof f!==j?(e.exports&&(f=e.exports=aa),f.UAParser=aa):c.amdO?void 0===(d=(function(){return aa}).call(b,c,b,a))||(a.exports=d):typeof g!==j&&(g.UAParser=aa);var ab=typeof g!==j&&(g.jQuery||g.Zepto);if(ab&&!ab.ua){var ac=new aa;ab.ua=ac.getResult(),ab.ua.get=function(){return ac.getUA()},ab.ua.set=function(a){ac.setUA(a);var b=ac.getResult();for(var c in b)ab.ua[c]=b[c]}}}("object"==typeof window?window:this)}},f={};function g(a){var b=f[a];if(void 0!==b)return b.exports;var c=f[a]={exports:{}},d=!0;try{e[a].call(c.exports,c,c.exports,g),d=!1}finally{d&&delete f[a]}return c.exports}g.ab="//",a.exports=g(226)})()},356:a=>{"use strict";a.exports=require("node:buffer")},521:a=>{"use strict";a.exports=require("node:async_hooks")},537:(a,b,c)=>{"use strict";c.r(b),c.d(b,{CompactEncrypt:()=>bc,CompactSign:()=>bf,EmbeddedJWK:()=>bo,EncryptJWT:()=>bk,FlattenedEncrypt:()=>aY,FlattenedSign:()=>be,GeneralEncrypt:()=>a$,GeneralSign:()=>bh,SignJWT:()=>bj,UnsecuredJWT:()=>bx,base64url:()=>e,calculateJwkThumbprint:()=>bm,calculateJwkThumbprintUri:()=>bn,compactDecrypt:()=>aQ,compactVerify:()=>a3,createLocalJWKSet:()=>bt,createRemoteJWKSet:()=>bw,cryptoRuntime:()=>bH,decodeJwt:()=>bB,decodeProtectedHeader:()=>bA,errors:()=>d,exportJWK:()=>aV,exportPKCS8:()=>aU,exportSPKI:()=>aT,flattenedDecrypt:()=>aP,flattenedVerify:()=>a2,generalDecrypt:()=>aR,generalVerify:()=>a4,generateKeyPair:()=>bF,generateSecret:()=>bG,importJWK:()=>aF,importPKCS8:()=>aE,importSPKI:()=>aC,importX509:()=>aD,jwtDecrypt:()=>bb,jwtVerify:()=>ba});var d={};c.r(d),c.d(d,{JOSEAlgNotAllowed:()=>w,JOSEError:()=>t,JOSENotSupported:()=>x,JWEDecompressionFailed:()=>z,JWEDecryptionFailed:()=>y,JWEInvalid:()=>A,JWKInvalid:()=>D,JWKSInvalid:()=>E,JWKSMultipleMatchingKeys:()=>G,JWKSNoMatchingKey:()=>F,JWKSTimeout:()=>H,JWSInvalid:()=>B,JWSSignatureVerificationFailed:()=>I,JWTClaimValidationFailed:()=>u,JWTExpired:()=>v,JWTInvalid:()=>C});var e={};c.r(e),c.d(e,{decode:()=>bz,encode:()=>by});let f=crypto,g=async(a,b)=>{let c=`SHA-${a.slice(-3)}`;return new Uint8Array(await f.subtle.digest(c,b))},h=new TextEncoder,i=new TextDecoder;function j(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;return a.forEach(a=>{b.set(a,c),c+=a.length}),b}function k(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function l(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return k(c,b,0),k(c,a%0x100000000,4),c}function m(a){let b=new Uint8Array(4);return k(b,a),b}function n(a){return j(m(a.length),a)}async function o(a,b,c){let d=Math.ceil((b>>3)/32),e=new Uint8Array(32*d);for(let b=0;b<d;b++){let d=new Uint8Array(4+a.length+c.length);d.set(m(b+1)),d.set(a,4),d.set(c,4+a.length),e.set(await g("sha256",d),32*b)}return e.slice(0,b>>3)}let p=a=>{let b=a;"string"==typeof b&&(b=h.encode(b));let c=[];for(let a=0;a<b.length;a+=32768)c.push(String.fromCharCode.apply(null,b.subarray(a,a+32768)));return btoa(c.join(""))},q=a=>p(a).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),r=a=>{let b=atob(a),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c},s=a=>{let b=a;b instanceof Uint8Array&&(b=i.decode(b)),b=b.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return r(b)}catch(a){throw TypeError("The input to be decoded is not correctly encoded.")}};class t extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(a){var b;super(a),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null==(b=Error.captureStackTrace)||b.call(Error,this,this.constructor)}}class u extends t{static get code(){return"ERR_JWT_CLAIM_VALIDATION_FAILED"}constructor(a,b="unspecified",c="unspecified"){super(a),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=b,this.reason=c}}class v extends t{static get code(){return"ERR_JWT_EXPIRED"}constructor(a,b="unspecified",c="unspecified"){super(a),this.code="ERR_JWT_EXPIRED",this.claim=b,this.reason=c}}class w extends t{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}static get code(){return"ERR_JOSE_ALG_NOT_ALLOWED"}}class x extends t{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}class y extends t{constructor(){super(...arguments),this.code="ERR_JWE_DECRYPTION_FAILED",this.message="decryption operation failed"}static get code(){return"ERR_JWE_DECRYPTION_FAILED"}}class z extends t{constructor(){super(...arguments),this.code="ERR_JWE_DECOMPRESSION_FAILED",this.message="decompression operation failed"}static get code(){return"ERR_JWE_DECOMPRESSION_FAILED"}}class A extends t{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}static get code(){return"ERR_JWE_INVALID"}}class B extends t{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}static get code(){return"ERR_JWS_INVALID"}}class C extends t{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}static get code(){return"ERR_JWT_INVALID"}}class D extends t{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}static get code(){return"ERR_JWK_INVALID"}}class E extends t{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}class F extends t{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}class G extends t{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator;class H extends t{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}class I extends t{constructor(){super(...arguments),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED",this.message="signature verification failed"}static get code(){return"ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}let J=f.getRandomValues.bind(f);function K(a){switch(a){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new x(`Unsupported JWE Algorithm: ${a}`)}}let L=a=>J(new Uint8Array(K(a)>>3)),M=(a,b)=>{if(b.length<<3!==K(a))throw new A("Invalid Initialization Vector length")},N=(a,b)=>{let c=a.byteLength<<3;if(c!==b)throw new A(`Invalid Content Encryption Key length. Expected ${b} bits, got ${c} bits`)};function O(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function P(a,b){return a.name===b}function Q(a){return parseInt(a.name.slice(4),10)}function R(a,b){if(b.length&&!b.some(b=>a.usages.includes(b))){let a="CryptoKey does not support this operation, its usages must include ";if(b.length>2){let c=b.pop();a+=`one of ${b.join(", ")}, or ${c}.`}else 2===b.length?a+=`one of ${b[0]} or ${b[1]}.`:a+=`${b[0]}.`;throw TypeError(a)}}function S(a,b,...c){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!P(a.algorithm,"AES-GCM"))throw O("AES-GCM");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw O(c,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!P(a.algorithm,"AES-KW"))throw O("AES-KW");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw O(c,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw O("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!P(a.algorithm,"PBKDF2"))throw O("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!P(a.algorithm,"RSA-OAEP"))throw O("RSA-OAEP");let c=parseInt(b.slice(9),10)||1;if(Q(a.algorithm.hash)!==c)throw O(`SHA-${c}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}R(a,c)}function T(a,b,...c){if(c.length>2){let b=c.pop();a+=`one of type ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of type ${c[0]} or ${c[1]}.`:a+=`of type ${c[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor&&b.constructor.name&&(a+=` Received an instance of ${b.constructor.name}`),a}let U=(a,...b)=>T("Key must be ",a,...b);function V(a,b,...c){return T(`Key for the ${a} algorithm must be `,b,...c)}let W=["CryptoKey"];async function X(a,b,c,d,e,g){let h,i;if(!(b instanceof Uint8Array))throw TypeError(U(b,"Uint8Array"));let k=parseInt(a.slice(1,4),10),m=await f.subtle.importKey("raw",b.subarray(k>>3),"AES-CBC",!1,["decrypt"]),n=await f.subtle.importKey("raw",b.subarray(0,k>>3),{hash:`SHA-${k<<1}`,name:"HMAC"},!1,["sign"]),o=j(g,d,c,l(g.length<<3)),p=new Uint8Array((await f.subtle.sign("HMAC",n,o)).slice(0,k>>3));try{h=((a,b)=>{if(!(a instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(b instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(a.length!==b.length)throw TypeError("Input buffers must have the same length");let c=a.length,d=0,e=-1;for(;++e<c;)d|=a[e]^b[e];return 0===d})(e,p)}catch(a){}if(!h)throw new y;try{i=new Uint8Array(await f.subtle.decrypt({iv:d,name:"AES-CBC"},m,c))}catch(a){}if(!i)throw new y;return i}async function Y(a,b,c,d,e,g){let h;b instanceof Uint8Array?h=await f.subtle.importKey("raw",b,"AES-GCM",!1,["decrypt"]):(S(b,a,"decrypt"),h=b);try{return new Uint8Array(await f.subtle.decrypt({additionalData:g,iv:d,name:"AES-GCM",tagLength:128},h,j(c,e)))}catch(a){throw new y}}let Z=async(a,b,c,d,e,f)=>{if(!(b instanceof CryptoKey)&&!(b instanceof Uint8Array))throw TypeError(U(b,...W,"Uint8Array"));switch(M(a,d),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return b instanceof Uint8Array&&N(b,parseInt(a.slice(-3),10)),X(a,b,c,d,e,f);case"A128GCM":case"A192GCM":case"A256GCM":return b instanceof Uint8Array&&N(b,parseInt(a.slice(1,4),10)),Y(a,b,c,d,e,f);default:throw new x("Unsupported JWE Content Encryption Algorithm")}},$=async()=>{throw new x('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `inflateRaw` decrypt option to provide Inflate Raw implementation.')},_=async()=>{throw new x('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `deflateRaw` encrypt option to provide Deflate Raw implementation.')},aa=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0};function ab(a){if("object"!=typeof a||null===a||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}let ac=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]];function ad(a,b){if(a.algorithm.length!==parseInt(b.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${b}`)}function ae(a,b,c){if(a instanceof CryptoKey)return S(a,b,c),a;if(a instanceof Uint8Array)return f.subtle.importKey("raw",a,"AES-KW",!0,[c]);throw TypeError(U(a,...W,"Uint8Array"))}let af=async(a,b,c)=>{let d=await ae(b,a,"wrapKey");ad(d,a);let e=await f.subtle.importKey("raw",c,...ac);return new Uint8Array(await f.subtle.wrapKey("raw",e,d,"AES-KW"))},ag=async(a,b,c)=>{let d=await ae(b,a,"unwrapKey");ad(d,a);let e=await f.subtle.unwrapKey("raw",c,d,"AES-KW",...ac);return new Uint8Array(await f.subtle.exportKey("raw",e))};async function ah(a,b,c,d,e=new Uint8Array(0),g=new Uint8Array(0)){let i;if(!(a instanceof CryptoKey))throw TypeError(U(a,...W));if(S(a,"ECDH"),!(b instanceof CryptoKey))throw TypeError(U(b,...W));S(b,"ECDH","deriveBits");let k=j(n(h.encode(c)),n(e),n(g),m(d));return i="X25519"===a.algorithm.name?256:"X448"===a.algorithm.name?448:Math.ceil(parseInt(a.algorithm.namedCurve.substr(-3),10)/8)<<3,o(new Uint8Array(await f.subtle.deriveBits({name:a.algorithm.name,public:a},b,i)),d,k)}async function ai(a){if(!(a instanceof CryptoKey))throw TypeError(U(a,...W));return f.subtle.generateKey(a.algorithm,!0,["deriveBits"])}function aj(a){if(!(a instanceof CryptoKey))throw TypeError(U(a,...W));return["P-256","P-384","P-521"].includes(a.algorithm.namedCurve)||"X25519"===a.algorithm.name||"X448"===a.algorithm.name}async function ak(a,b,c,d){if(!(a instanceof Uint8Array)||a.length<8)throw new A("PBES2 Salt Input must be 8 or more octets");let e=j(h.encode(b),new Uint8Array([0]),a),g=parseInt(b.slice(13,16),10),i={hash:`SHA-${b.slice(8,11)}`,iterations:c,name:"PBKDF2",salt:e},k=await function(a,b){if(a instanceof Uint8Array)return f.subtle.importKey("raw",a,"PBKDF2",!1,["deriveBits"]);if(a instanceof CryptoKey)return S(a,b,"deriveBits","deriveKey"),a;throw TypeError(U(a,...W,"Uint8Array"))}(d,b);if(k.usages.includes("deriveBits"))return new Uint8Array(await f.subtle.deriveBits(i,k,g));if(k.usages.includes("deriveKey"))return f.subtle.deriveKey(i,k,{length:g,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let al=async(a,b,c,d=2048,e=J(new Uint8Array(16)))=>{let f=await ak(e,a,d,b);return{encryptedKey:await af(a.slice(-6),f,c),p2c:d,p2s:q(e)}},am=async(a,b,c,d,e)=>{let f=await ak(e,a,d,b);return ag(a.slice(-6),f,c)};function an(a){switch(a){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new x(`alg ${a} is not supported either by JOSE or your javascript runtime`)}}let ao=(a,b)=>{if(a.startsWith("RS")||a.startsWith("PS")){let{modulusLength:c}=b.algorithm;if("number"!=typeof c||c<2048)throw TypeError(`${a} requires key modulusLength to be 2048 bits or larger`)}},ap=async(a,b,c)=>{if(!(b instanceof CryptoKey))throw TypeError(U(b,...W));if(S(b,a,"encrypt","wrapKey"),ao(a,b),b.usages.includes("encrypt"))return new Uint8Array(await f.subtle.encrypt(an(a),b,c));if(b.usages.includes("wrapKey")){let d=await f.subtle.importKey("raw",c,...ac);return new Uint8Array(await f.subtle.wrapKey("raw",d,b,an(a)))}throw TypeError('RSA-OAEP key "usages" must include "encrypt" or "wrapKey" for this operation')},aq=async(a,b,c)=>{if(!(b instanceof CryptoKey))throw TypeError(U(b,...W));if(S(b,a,"decrypt","unwrapKey"),ao(a,b),b.usages.includes("decrypt"))return new Uint8Array(await f.subtle.decrypt(an(a),b,c));if(b.usages.includes("unwrapKey")){let d=await f.subtle.unwrapKey("raw",c,b,an(a),...ac);return new Uint8Array(await f.subtle.exportKey("raw",d))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function ar(a){switch(a){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new x(`Unsupported JWE Algorithm: ${a}`)}}let as=a=>J(new Uint8Array(ar(a)>>3)),at=(a,b)=>{let c=(a.match(/.{1,64}/g)||[]).join("\n");return`-----BEGIN ${b}-----
${c}
-----END ${b}-----`},au=async(a,b,c)=>{if(!(c instanceof CryptoKey))throw TypeError(U(c,...W));if(!c.extractable)throw TypeError("CryptoKey is not extractable");if(c.type!==a)throw TypeError(`key is not a ${a} key`);return at(p(new Uint8Array(await f.subtle.exportKey(b,c))),`${a.toUpperCase()} KEY`)},av=(a,b,c=0)=>{0===c&&(b.unshift(b.length),b.unshift(6));let d=a.indexOf(b[0],c);if(-1===d)return!1;let e=a.subarray(d,d+b.length);return e.length===b.length&&(e.every((a,c)=>a===b[c])||av(a,b,d+1))},aw=a=>{switch(!0){case av(a,[42,134,72,206,61,3,1,7]):return"P-256";case av(a,[43,129,4,0,34]):return"P-384";case av(a,[43,129,4,0,35]):return"P-521";case av(a,[43,101,110]):return"X25519";case av(a,[43,101,111]):return"X448";case av(a,[43,101,112]):return"Ed25519";case av(a,[43,101,113]):return"Ed448";default:throw new x("Invalid or unsupported EC Key Curve or OKP Key Sub Type")}},ax=async(a,b,c,d,e)=>{var g;let h,i,j=new Uint8Array(atob(c.replace(a,"")).split("").map(a=>a.charCodeAt(0))),k="spki"===b;switch(d){case"PS256":case"PS384":case"PS512":h={name:"RSA-PSS",hash:`SHA-${d.slice(-3)}`},i=k?["verify"]:["sign"];break;case"RS256":case"RS384":case"RS512":h={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${d.slice(-3)}`},i=k?["verify"]:["sign"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":h={name:"RSA-OAEP",hash:`SHA-${parseInt(d.slice(-3),10)||1}`},i=k?["encrypt","wrapKey"]:["decrypt","unwrapKey"];break;case"ES256":h={name:"ECDSA",namedCurve:"P-256"},i=k?["verify"]:["sign"];break;case"ES384":h={name:"ECDSA",namedCurve:"P-384"},i=k?["verify"]:["sign"];break;case"ES512":h={name:"ECDSA",namedCurve:"P-521"},i=k?["verify"]:["sign"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a=aw(j);h=a.startsWith("P-")?{name:"ECDH",namedCurve:a}:{name:a},i=k?[]:["deriveBits"];break}case"EdDSA":h={name:aw(j)},i=k?["verify"]:["sign"];break;default:throw new x('Invalid or unsupported "alg" (Algorithm) value')}return f.subtle.importKey(b,j,h,null!=(g=null==e?void 0:e.extractable)&&g,i)},ay=(a,b,c)=>ax(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\s)/g,"spki",a,b,c);function az(a){let b=[],c=0;for(;c<a.length;){let d=aA(a.subarray(c));b.push(d),c+=d.byteLength}return b}function aA(a){let b=0,c=31&a[0];if(b++,31===c){for(c=0;a[b]>=128;)c=128*c+a[b]-128,b++;c=128*c+a[b]-128,b++}let d=0;if(a[b]<128)d=a[b],b++;else if(128===d){for(d=0;0!==a[b+d]||0!==a[b+d+1];){if(d>a.byteLength)throw TypeError("invalid indefinite form length");d++}let c=b+d+2;return{byteLength:c,contents:a.subarray(b,b+d),raw:a.subarray(0,c)}}else{let c=127&a[b];b++,d=0;for(let e=0;e<c;e++)d=256*d+a[b],b++}let e=b+d;return{byteLength:e,contents:a.subarray(b,e),raw:a.subarray(0,e)}}let aB=async a=>{var b,c;if(!a.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:d,keyUsages:e}=function(a){let b,c;switch(a.kty){case"oct":switch(a.alg){case"HS256":case"HS384":case"HS512":b={name:"HMAC",hash:`SHA-${a.alg.slice(-3)}`},c=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":throw new x(`${a.alg} keys cannot be imported as CryptoKey instances`);case"A128GCM":case"A192GCM":case"A256GCM":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":b={name:"AES-GCM"},c=["encrypt","decrypt"];break;case"A128KW":case"A192KW":case"A256KW":b={name:"AES-KW"},c=["wrapKey","unwrapKey"];break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":b={name:"PBKDF2"},c=["deriveBits"];break;default:throw new x('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(a.alg){case"PS256":case"PS384":case"PS512":b={name:"RSA-PSS",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":b={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":b={name:"RSA-OAEP",hash:`SHA-${parseInt(a.alg.slice(-3),10)||1}`},c=a.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new x('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(a.alg){case"ES256":b={name:"ECDSA",namedCurve:"P-256"},c=a.d?["sign"]:["verify"];break;case"ES384":b={name:"ECDSA",namedCurve:"P-384"},c=a.d?["sign"]:["verify"];break;case"ES512":b={name:"ECDSA",namedCurve:"P-521"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:"ECDH",namedCurve:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new x('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(a.alg){case"EdDSA":b={name:a.crv},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new x('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new x('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:b,keyUsages:c}}(a),g=[d,null!=(b=a.ext)&&b,null!=(c=a.key_ops)?c:e];if("PBKDF2"===d.name)return f.subtle.importKey("raw",s(a.k),...g);let h={...a};return delete h.alg,delete h.use,f.subtle.importKey("jwk",h,...g)};async function aC(a,b,c){if("string"!=typeof a||0!==a.indexOf("-----BEGIN PUBLIC KEY-----"))throw TypeError('"spki" must be SPKI formatted string');return ay(a,b,c)}async function aD(a,b,c){let d;if("string"!=typeof a||0!==a.indexOf("-----BEGIN CERTIFICATE-----"))throw TypeError('"x509" must be X.509 formatted string');try{d=at(function(a){let b=az(az(aA(a).contents)[0].contents);return p(b[160===b[0].raw[0]?6:5].raw)}(r(a.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\s)/g,""))),"PUBLIC KEY")}catch(a){throw TypeError("Failed to parse the X.509 certificate",{cause:a})}return ay(d,b,c)}async function aE(a,b,c){if("string"!=typeof a||0!==a.indexOf("-----BEGIN PRIVATE KEY-----"))throw TypeError('"pkcs8" must be PKCS#8 formatted string');return ax(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\s)/g,"pkcs8",a,b,c)}async function aF(a,b,c){var d;if(!ab(a))throw TypeError("JWK must be an object");switch(b||(b=a.alg),a.kty){case"oct":if("string"!=typeof a.k||!a.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=c||(c=!0!==a.ext),c)return aB({...a,alg:b,ext:null!=(d=a.ext)&&d});return s(a.k);case"RSA":if(void 0!==a.oth)throw new x('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return aB({...a,alg:b});default:throw new x('Unsupported "kty" (Key Type) Parameter value')}}let aG=(a,b,c)=>{a.startsWith("HS")||"dir"===a||a.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(a)?((a,b)=>{if(!(b instanceof Uint8Array)){if(!(b instanceof CryptoKey))throw TypeError(V(a,b,...W,"Uint8Array"));if("secret"!==b.type)throw TypeError(`${W.join(" or ")} instances for symmetric algorithms must be of type "secret"`)}})(a,b):((a,b,c)=>{if(!(b instanceof CryptoKey))throw TypeError(V(a,b,...W));if("secret"===b.type)throw TypeError(`${W.join(" or ")} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===c&&"public"===b.type)throw TypeError(`${W.join(" or ")} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===c&&"public"===b.type)throw TypeError(`${W.join(" or ")} instances for asymmetric algorithm decryption must be of type "private"`);if(b.algorithm&&"verify"===c&&"private"===b.type)throw TypeError(`${W.join(" or ")} instances for asymmetric algorithm verifying must be of type "public"`);if(b.algorithm&&"encrypt"===c&&"private"===b.type)throw TypeError(`${W.join(" or ")} instances for asymmetric algorithm encryption must be of type "public"`)})(a,b,c)};async function aH(a,b,c,d,e){if(!(c instanceof Uint8Array))throw TypeError(U(c,"Uint8Array"));let g=parseInt(a.slice(1,4),10),h=await f.subtle.importKey("raw",c.subarray(g>>3),"AES-CBC",!1,["encrypt"]),i=await f.subtle.importKey("raw",c.subarray(0,g>>3),{hash:`SHA-${g<<1}`,name:"HMAC"},!1,["sign"]),k=new Uint8Array(await f.subtle.encrypt({iv:d,name:"AES-CBC"},h,b)),m=j(e,d,k,l(e.length<<3));return{ciphertext:k,tag:new Uint8Array((await f.subtle.sign("HMAC",i,m)).slice(0,g>>3))}}async function aI(a,b,c,d,e){let g;c instanceof Uint8Array?g=await f.subtle.importKey("raw",c,"AES-GCM",!1,["encrypt"]):(S(c,a,"encrypt"),g=c);let h=new Uint8Array(await f.subtle.encrypt({additionalData:e,iv:d,name:"AES-GCM",tagLength:128},g,b)),i=h.slice(-16);return{ciphertext:h.slice(0,-16),tag:i}}let aJ=async(a,b,c,d,e)=>{if(!(c instanceof CryptoKey)&&!(c instanceof Uint8Array))throw TypeError(U(c,...W,"Uint8Array"));switch(M(a,d),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return c instanceof Uint8Array&&N(c,parseInt(a.slice(-3),10)),aH(a,b,c,d,e);case"A128GCM":case"A192GCM":case"A256GCM":return c instanceof Uint8Array&&N(c,parseInt(a.slice(1,4),10)),aI(a,b,c,d,e);default:throw new x("Unsupported JWE Content Encryption Algorithm")}};async function aK(a,b,c,d){let e=a.slice(0,7);d||(d=L(e));let{ciphertext:f,tag:g}=await aJ(e,c,b,d,new Uint8Array(0));return{encryptedKey:f,iv:q(d),tag:q(g)}}async function aL(a,b,c,d,e){return Z(a.slice(0,7),b,c,d,e,new Uint8Array(0))}async function aM(a,b,c,d,e){switch(aG(a,b,"decrypt"),a){case"dir":if(void 0!==c)throw new A("Encountered unexpected JWE Encrypted Key");return b;case"ECDH-ES":if(void 0!==c)throw new A("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e,f;if(!ab(d.epk))throw new A('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!aj(b))throw new x("ECDH with the provided key is not allowed or not supported by your javascript runtime");let g=await aF(d.epk,a);if(void 0!==d.apu){if("string"!=typeof d.apu)throw new A('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{e=s(d.apu)}catch(a){throw new A("Failed to base64url decode the apu")}}if(void 0!==d.apv){if("string"!=typeof d.apv)throw new A('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{f=s(d.apv)}catch(a){throw new A("Failed to base64url decode the apv")}}let h=await ah(g,b,"ECDH-ES"===a?d.enc:a,"ECDH-ES"===a?ar(d.enc):parseInt(a.slice(-5,-2),10),e,f);if("ECDH-ES"===a)return h;if(void 0===c)throw new A("JWE Encrypted Key missing");return ag(a.slice(-6),h,c)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===c)throw new A("JWE Encrypted Key missing");return aq(a,b,c);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let f;if(void 0===c)throw new A("JWE Encrypted Key missing");if("number"!=typeof d.p2c)throw new A('JOSE Header "p2c" (PBES2 Count) missing or invalid');let g=(null==e?void 0:e.maxPBES2Count)||1e4;if(d.p2c>g)throw new A('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof d.p2s)throw new A('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{f=s(d.p2s)}catch(a){throw new A("Failed to base64url decode the p2s")}return am(a,b,c,d.p2c,f)}case"A128KW":case"A192KW":case"A256KW":if(void 0===c)throw new A("JWE Encrypted Key missing");return ag(a,b,c);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let e,f;if(void 0===c)throw new A("JWE Encrypted Key missing");if("string"!=typeof d.iv)throw new A('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof d.tag)throw new A('JOSE Header "tag" (Authentication Tag) missing or invalid');try{e=s(d.iv)}catch(a){throw new A("Failed to base64url decode the iv")}try{f=s(d.tag)}catch(a){throw new A("Failed to base64url decode the tag")}return aL(a,b,c,e,f)}default:throw new x('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let aN=function(a,b,c,d,e){let f;if(void 0!==e.crit&&void 0===d.crit)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!d||void 0===d.crit)return new Set;if(!Array.isArray(d.crit)||0===d.crit.length||d.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let g of(f=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,d.crit)){if(!f.has(g))throw new x(`Extension Header Parameter "${g}" is not recognized`);if(void 0===e[g])throw new a(`Extension Header Parameter "${g}" is missing`);if(f.get(g)&&void 0===d[g])throw new a(`Extension Header Parameter "${g}" MUST be integrity protected`)}return new Set(d.crit)},aO=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)};async function aP(a,b,c){var d;let e,f,g,k,l,m,n;if(!ab(a))throw new A("Flattened JWE must be an object");if(void 0===a.protected&&void 0===a.header&&void 0===a.unprotected)throw new A("JOSE Header missing");if("string"!=typeof a.iv)throw new A("JWE Initialization Vector missing or incorrect type");if("string"!=typeof a.ciphertext)throw new A("JWE Ciphertext missing or incorrect type");if("string"!=typeof a.tag)throw new A("JWE Authentication Tag missing or incorrect type");if(void 0!==a.protected&&"string"!=typeof a.protected)throw new A("JWE Protected Header incorrect type");if(void 0!==a.encrypted_key&&"string"!=typeof a.encrypted_key)throw new A("JWE Encrypted Key incorrect type");if(void 0!==a.aad&&"string"!=typeof a.aad)throw new A("JWE AAD incorrect type");if(void 0!==a.header&&!ab(a.header))throw new A("JWE Shared Unprotected Header incorrect type");if(void 0!==a.unprotected&&!ab(a.unprotected))throw new A("JWE Per-Recipient Unprotected Header incorrect type");if(a.protected)try{let b=s(a.protected);e=JSON.parse(i.decode(b))}catch(a){throw new A("JWE Protected Header is invalid")}if(!aa(e,a.header,a.unprotected))throw new A("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let o={...e,...a.header,...a.unprotected};if(aN(A,new Map,null==c?void 0:c.crit,e,o),void 0!==o.zip){if(!e||!e.zip)throw new A('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==o.zip)throw new x('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:p,enc:q}=o;if("string"!=typeof p||!p)throw new A("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof q||!q)throw new A("missing JWE Encryption Algorithm (enc) in JWE Header");let r=c&&aO("keyManagementAlgorithms",c.keyManagementAlgorithms),t=c&&aO("contentEncryptionAlgorithms",c.contentEncryptionAlgorithms);if(r&&!r.has(p))throw new w('"alg" (Algorithm) Header Parameter not allowed');if(t&&!t.has(q))throw new w('"enc" (Encryption Algorithm) Header Parameter not allowed');if(void 0!==a.encrypted_key)try{f=s(a.encrypted_key)}catch(a){throw new A("Failed to base64url decode the encrypted_key")}let u=!1;"function"==typeof b&&(b=await b(e,a),u=!0);try{g=await aM(p,b,f,o,c)}catch(a){if(a instanceof TypeError||a instanceof A||a instanceof x)throw a;g=as(q)}try{k=s(a.iv)}catch(a){throw new A("Failed to base64url decode the iv")}try{l=s(a.tag)}catch(a){throw new A("Failed to base64url decode the tag")}let v=h.encode(null!=(d=a.protected)?d:"");m=void 0!==a.aad?j(v,h.encode("."),h.encode(a.aad)):v;try{n=s(a.ciphertext)}catch(a){throw new A("Failed to base64url decode the ciphertext")}let y=await Z(q,g,n,k,l,m);"DEF"===o.zip&&(y=await ((null==c?void 0:c.inflateRaw)||$)(y));let z={plaintext:y};if(void 0!==a.protected&&(z.protectedHeader=e),void 0!==a.aad)try{z.additionalAuthenticatedData=s(a.aad)}catch(a){throw new A("Failed to base64url decode the aad")}return(void 0!==a.unprotected&&(z.sharedUnprotectedHeader=a.unprotected),void 0!==a.header&&(z.unprotectedHeader=a.header),u)?{...z,key:b}:z}async function aQ(a,b,c){if(a instanceof Uint8Array&&(a=i.decode(a)),"string"!=typeof a)throw new A("Compact JWE must be a string or Uint8Array");let{0:d,1:e,2:f,3:g,4:h,length:j}=a.split(".");if(5!==j)throw new A("Invalid Compact JWE");let k=await aP({ciphertext:g,iv:f||void 0,protected:d||void 0,tag:h||void 0,encrypted_key:e||void 0},b,c),l={plaintext:k.plaintext,protectedHeader:k.protectedHeader};return"function"==typeof b?{...l,key:k.key}:l}async function aR(a,b,c){if(!ab(a))throw new A("General JWE must be an object");if(!Array.isArray(a.recipients)||!a.recipients.every(ab))throw new A("JWE Recipients missing or incorrect type");if(!a.recipients.length)throw new A("JWE Recipients has no members");for(let d of a.recipients)try{return await aP({aad:a.aad,ciphertext:a.ciphertext,encrypted_key:d.encrypted_key,header:d.header,iv:a.iv,protected:a.protected,tag:a.tag,unprotected:a.unprotected},b,c)}catch(a){}throw new y}let aS=async a=>{if(a instanceof Uint8Array)return{kty:"oct",k:q(a)};if(!(a instanceof CryptoKey))throw TypeError(U(a,...W,"Uint8Array"));if(!a.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:b,key_ops:c,alg:d,use:e,...g}=await f.subtle.exportKey("jwk",a);return g};async function aT(a){return au("public","spki",a)}async function aU(a){return au("private","pkcs8",a)}async function aV(a){return aS(a)}async function aW(a,b,c,d,e={}){let f,g,h;switch(aG(a,c,"encrypt"),a){case"dir":h=c;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!aj(c))throw new x("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:i,apv:j}=e,{epk:k}=e;k||(k=(await ai(c)).privateKey);let{x:l,y:m,crv:n,kty:o}=await aV(k),p=await ah(c,k,"ECDH-ES"===a?b:a,"ECDH-ES"===a?ar(b):parseInt(a.slice(-5,-2),10),i,j);if(g={epk:{x:l,crv:n,kty:o}},"EC"===o&&(g.epk.y=m),i&&(g.apu=q(i)),j&&(g.apv=q(j)),"ECDH-ES"===a){h=p;break}h=d||as(b);let r=a.slice(-6);f=await af(r,p,h);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":h=d||as(b),f=await ap(a,c,h);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{h=d||as(b);let{p2c:i,p2s:j}=e;({encryptedKey:f,...g}=await al(a,c,h,i,j));break}case"A128KW":case"A192KW":case"A256KW":h=d||as(b),f=await af(a,c,h);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{h=d||as(b);let{iv:i}=e;({encryptedKey:f,...g}=await aK(a,c,h,i));break}default:throw new x('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:h,encryptedKey:f,parameters:g}}let aX=Symbol();class aY{constructor(a){if(!(a instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=a}setKeyManagementParameters(a){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=a,this}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setSharedUnprotectedHeader(a){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=a,this}setUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}setAdditionalAuthenticatedData(a){return this._aad=a,this}setContentEncryptionKey(a){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=a,this}setInitializationVector(a){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=a,this}async encrypt(a,b){let c,d,e,f,g,k,l;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new A("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!aa(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new A("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let m={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(aN(A,new Map,null==b?void 0:b.crit,this._protectedHeader,m),void 0!==m.zip){if(!this._protectedHeader||!this._protectedHeader.zip)throw new A('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==m.zip)throw new x('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:n,enc:o}=m;if("string"!=typeof n||!n)throw new A('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof o||!o)throw new A('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if("dir"===n){if(this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Encryption")}else if("ECDH-ES"===n&&this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Key Agreement");{let e;({cek:d,encryptedKey:c,parameters:e}=await aW(n,o,a,this._cek,this._keyManagementParameters)),e&&(b&&aX in b?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...e}:this.setUnprotectedHeader(e):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...e}:this.setProtectedHeader(e))}if(this._iv||(this._iv=L(o)),f=this._protectedHeader?h.encode(q(JSON.stringify(this._protectedHeader))):h.encode(""),this._aad?(g=q(this._aad),e=j(f,h.encode("."),h.encode(g))):e=f,"DEF"===m.zip){let a=await ((null==b?void 0:b.deflateRaw)||_)(this._plaintext);({ciphertext:k,tag:l}=await aJ(o,a,d,this._iv,e))}else({ciphertext:k,tag:l}=await aJ(o,this._plaintext,d,this._iv,e));let p={ciphertext:q(k),iv:q(this._iv),tag:q(l)};return c&&(p.encrypted_key=q(c)),g&&(p.aad=g),this._protectedHeader&&(p.protected=i.decode(f)),this._sharedUnprotectedHeader&&(p.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(p.header=this._unprotectedHeader),p}}class aZ{constructor(a,b,c){this.parent=a,this.key=b,this.options=c}setUnprotectedHeader(a){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=a,this}addRecipient(...a){return this.parent.addRecipient(...a)}encrypt(...a){return this.parent.encrypt(...a)}done(){return this.parent}}class a${constructor(a){this._recipients=[],this._plaintext=a}addRecipient(a,b){let c=new aZ(this,a,{crit:null==b?void 0:b.crit});return this._recipients.push(c),c}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setSharedUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}setAdditionalAuthenticatedData(a){return this._aad=a,this}async encrypt(a){var b,c,d;let e;if(!this._recipients.length)throw new A("at least one recipient must be added");if(a={deflateRaw:null==a?void 0:a.deflateRaw},1===this._recipients.length){let[b]=this._recipients,c=await new aY(this._plaintext).setAdditionalAuthenticatedData(this._aad).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(b.unprotectedHeader).encrypt(b.key,{...b.options,...a}),d={ciphertext:c.ciphertext,iv:c.iv,recipients:[{}],tag:c.tag};return c.aad&&(d.aad=c.aad),c.protected&&(d.protected=c.protected),c.unprotected&&(d.unprotected=c.unprotected),c.encrypted_key&&(d.recipients[0].encrypted_key=c.encrypted_key),c.header&&(d.recipients[0].header=c.header),d}for(let a=0;a<this._recipients.length;a++){let b=this._recipients[a];if(!aa(this._protectedHeader,this._unprotectedHeader,b.unprotectedHeader))throw new A("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let c={...this._protectedHeader,...this._unprotectedHeader,...b.unprotectedHeader},{alg:d}=c;if("string"!=typeof d||!d)throw new A('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("dir"===d||"ECDH-ES"===d)throw new A('"dir" and "ECDH-ES" alg may only be used with a single recipient');if("string"!=typeof c.enc||!c.enc)throw new A('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(e){if(e!==c.enc)throw new A('JWE "enc" (Encryption Algorithm) Header Parameter must be the same for all recipients')}else e=c.enc;if(aN(A,new Map,b.options.crit,this._protectedHeader,c),void 0!==c.zip&&(!this._protectedHeader||!this._protectedHeader.zip))throw new A('JWE "zip" (Compression Algorithm) Header MUST be integrity protected')}let f=as(e),g={ciphertext:"",iv:"",recipients:[],tag:""};for(let h=0;h<this._recipients.length;h++){let i=this._recipients[h],j={};g.recipients.push(j);let k=({...this._protectedHeader,...this._unprotectedHeader,...i.unprotectedHeader}).alg.startsWith("PBES2")?2048+h:void 0;if(0===h){let b=await new aY(this._plaintext).setAdditionalAuthenticatedData(this._aad).setContentEncryptionKey(f).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(i.unprotectedHeader).setKeyManagementParameters({p2c:k}).encrypt(i.key,{...i.options,...a,[aX]:!0});g.ciphertext=b.ciphertext,g.iv=b.iv,g.tag=b.tag,b.aad&&(g.aad=b.aad),b.protected&&(g.protected=b.protected),b.unprotected&&(g.unprotected=b.unprotected),j.encrypted_key=b.encrypted_key,b.header&&(j.header=b.header);continue}let{encryptedKey:l,parameters:m}=await aW((null==(b=i.unprotectedHeader)?void 0:b.alg)||(null==(c=this._protectedHeader)?void 0:c.alg)||(null==(d=this._unprotectedHeader)?void 0:d.alg),e,i.key,f,{p2c:k});j.encrypted_key=q(l),(i.unprotectedHeader||m)&&(j.header={...i.unprotectedHeader,...m})}return g}}function a_(a,b){let c=`SHA-${a.slice(-3)}`;switch(a){case"HS256":case"HS384":case"HS512":return{hash:c,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:c,name:"RSA-PSS",saltLength:a.slice(-3)>>3};case"RS256":case"RS384":case"RS512":return{hash:c,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:c,name:"ECDSA",namedCurve:b.namedCurve};case"EdDSA":return{name:b.name};default:throw new x(`alg ${a} is not supported either by JOSE or your javascript runtime`)}}function a0(a,b,c){if(b instanceof CryptoKey)return!function(a,b,...c){switch(b){case"HS256":case"HS384":case"HS512":{if(!P(a.algorithm,"HMAC"))throw O("HMAC");let c=parseInt(b.slice(2),10);if(Q(a.algorithm.hash)!==c)throw O(`SHA-${c}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!P(a.algorithm,"RSASSA-PKCS1-v1_5"))throw O("RSASSA-PKCS1-v1_5");let c=parseInt(b.slice(2),10);if(Q(a.algorithm.hash)!==c)throw O(`SHA-${c}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!P(a.algorithm,"RSA-PSS"))throw O("RSA-PSS");let c=parseInt(b.slice(2),10);if(Q(a.algorithm.hash)!==c)throw O(`SHA-${c}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==a.algorithm.name&&"Ed448"!==a.algorithm.name)throw O("Ed25519 or Ed448");break;case"ES256":case"ES384":case"ES512":{if(!P(a.algorithm,"ECDSA"))throw O("ECDSA");let c=function(a){switch(a){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(b);if(a.algorithm.namedCurve!==c)throw O(c,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}R(a,c)}(b,a,c),b;if(b instanceof Uint8Array){if(!a.startsWith("HS"))throw TypeError(U(b,...W));return f.subtle.importKey("raw",b,{hash:`SHA-${a.slice(-3)}`,name:"HMAC"},!1,[c])}throw TypeError(U(b,...W,"Uint8Array"))}let a1=async(a,b,c,d)=>{let e=await a0(a,b,"verify");ao(a,e);let g=a_(a,e.algorithm);try{return await f.subtle.verify(g,e,c,d)}catch(a){return!1}};async function a2(a,b,c){var d;let e,f;if(!ab(a))throw new B("Flattened JWS must be an object");if(void 0===a.protected&&void 0===a.header)throw new B('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==a.protected&&"string"!=typeof a.protected)throw new B("JWS Protected Header incorrect type");if(void 0===a.payload)throw new B("JWS Payload missing");if("string"!=typeof a.signature)throw new B("JWS Signature missing or incorrect type");if(void 0!==a.header&&!ab(a.header))throw new B("JWS Unprotected Header incorrect type");let g={};if(a.protected)try{let b=s(a.protected);g=JSON.parse(i.decode(b))}catch(a){throw new B("JWS Protected Header is invalid")}if(!aa(g,a.header))throw new B("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let k={...g,...a.header},l=aN(B,new Map([["b64",!0]]),null==c?void 0:c.crit,g,k),m=!0;if(l.has("b64")&&"boolean"!=typeof(m=g.b64))throw new B('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:n}=k;if("string"!=typeof n||!n)throw new B('JWS "alg" (Algorithm) Header Parameter missing or invalid');let o=c&&aO("algorithms",c.algorithms);if(o&&!o.has(n))throw new w('"alg" (Algorithm) Header Parameter not allowed');if(m){if("string"!=typeof a.payload)throw new B("JWS Payload must be a string")}else if("string"!=typeof a.payload&&!(a.payload instanceof Uint8Array))throw new B("JWS Payload must be a string or an Uint8Array instance");let p=!1;"function"==typeof b&&(b=await b(g,a),p=!0),aG(n,b,"verify");let q=j(h.encode(null!=(d=a.protected)?d:""),h.encode("."),"string"==typeof a.payload?h.encode(a.payload):a.payload);try{e=s(a.signature)}catch(a){throw new B("Failed to base64url decode the signature")}if(!await a1(n,b,e,q))throw new I;if(m)try{f=s(a.payload)}catch(a){throw new B("Failed to base64url decode the payload")}else f="string"==typeof a.payload?h.encode(a.payload):a.payload;let r={payload:f};return(void 0!==a.protected&&(r.protectedHeader=g),void 0!==a.header&&(r.unprotectedHeader=a.header),p)?{...r,key:b}:r}async function a3(a,b,c){if(a instanceof Uint8Array&&(a=i.decode(a)),"string"!=typeof a)throw new B("Compact JWS must be a string or Uint8Array");let{0:d,1:e,2:f,length:g}=a.split(".");if(3!==g)throw new B("Invalid Compact JWS");let h=await a2({payload:e,protected:d,signature:f},b,c),j={payload:h.payload,protectedHeader:h.protectedHeader};return"function"==typeof b?{...j,key:h.key}:j}async function a4(a,b,c){if(!ab(a))throw new B("General JWS must be an object");if(!Array.isArray(a.signatures)||!a.signatures.every(ab))throw new B("JWS Signatures missing or incorrect type");for(let d of a.signatures)try{return await a2({header:d.header,payload:a.payload,protected:d.protected,signature:d.signature},b,c)}catch(a){}throw new I}let a5=a=>Math.floor(a.getTime()/1e3),a6=/^(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i,a7=a=>{let b=a6.exec(a);if(!b)throw TypeError("Invalid time period format");let c=parseFloat(b[1]);switch(b[2].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":return Math.round(c);case"minute":case"minutes":case"min":case"mins":case"m":return Math.round(60*c);case"hour":case"hours":case"hr":case"hrs":case"h":return Math.round(3600*c);case"day":case"days":case"d":return Math.round(86400*c);case"week":case"weeks":case"w":return Math.round(604800*c);default:return Math.round(0x1e187e0*c)}},a8=a=>a.toLowerCase().replace(/^application\//,""),a9=(a,b,c={})=>{let d,e,{typ:f}=c;if(f&&("string"!=typeof a.typ||a8(a.typ)!==a8(f)))throw new u('unexpected "typ" JWT header value',"typ","check_failed");try{d=JSON.parse(i.decode(b))}catch(a){}if(!ab(d))throw new C("JWT Claims Set must be a top-level JSON object");let{requiredClaims:g=[],issuer:h,subject:j,audience:k,maxTokenAge:l}=c;for(let a of(void 0!==l&&g.push("iat"),void 0!==k&&g.push("aud"),void 0!==j&&g.push("sub"),void 0!==h&&g.push("iss"),new Set(g.reverse())))if(!(a in d))throw new u(`missing required "${a}" claim`,a,"missing");if(h&&!(Array.isArray(h)?h:[h]).includes(d.iss))throw new u('unexpected "iss" claim value',"iss","check_failed");if(j&&d.sub!==j)throw new u('unexpected "sub" claim value',"sub","check_failed");if(k&&!((a,b)=>"string"==typeof a?b.includes(a):!!Array.isArray(a)&&b.some(Set.prototype.has.bind(new Set(a))))(d.aud,"string"==typeof k?[k]:k))throw new u('unexpected "aud" claim value',"aud","check_failed");switch(typeof c.clockTolerance){case"string":e=a7(c.clockTolerance);break;case"number":e=c.clockTolerance;break;case"undefined":e=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:m}=c,n=a5(m||new Date);if((void 0!==d.iat||l)&&"number"!=typeof d.iat)throw new u('"iat" claim must be a number',"iat","invalid");if(void 0!==d.nbf){if("number"!=typeof d.nbf)throw new u('"nbf" claim must be a number',"nbf","invalid");if(d.nbf>n+e)throw new u('"nbf" claim timestamp check failed',"nbf","check_failed")}if(void 0!==d.exp){if("number"!=typeof d.exp)throw new u('"exp" claim must be a number',"exp","invalid");if(d.exp<=n-e)throw new v('"exp" claim timestamp check failed',"exp","check_failed")}if(l){let a=n-d.iat;if(a-e>("number"==typeof l?l:a7(l)))throw new v('"iat" claim timestamp check failed (too far in the past)',"iat","check_failed");if(a<0-e)throw new u('"iat" claim timestamp check failed (it should be in the past)',"iat","check_failed")}return d};async function ba(a,b,c){var d;let e=await a3(a,b,c);if((null==(d=e.protectedHeader.crit)?void 0:d.includes("b64"))&&!1===e.protectedHeader.b64)throw new C("JWTs MUST NOT use unencoded payload");let f={payload:a9(e.protectedHeader,e.payload,c),protectedHeader:e.protectedHeader};return"function"==typeof b?{...f,key:e.key}:f}async function bb(a,b,c){let d=await aQ(a,b,c),e=a9(d.protectedHeader,d.plaintext,c),{protectedHeader:f}=d;if(void 0!==f.iss&&f.iss!==e.iss)throw new u('replicated "iss" claim header parameter mismatch',"iss","mismatch");if(void 0!==f.sub&&f.sub!==e.sub)throw new u('replicated "sub" claim header parameter mismatch',"sub","mismatch");if(void 0!==f.aud&&JSON.stringify(f.aud)!==JSON.stringify(e.aud))throw new u('replicated "aud" claim header parameter mismatch',"aud","mismatch");let g={payload:e,protectedHeader:f};return"function"==typeof b?{...g,key:d.key}:g}class bc{constructor(a){this._flattened=new aY(a)}setContentEncryptionKey(a){return this._flattened.setContentEncryptionKey(a),this}setInitializationVector(a){return this._flattened.setInitializationVector(a),this}setProtectedHeader(a){return this._flattened.setProtectedHeader(a),this}setKeyManagementParameters(a){return this._flattened.setKeyManagementParameters(a),this}async encrypt(a,b){let c=await this._flattened.encrypt(a,b);return[c.protected,c.encrypted_key,c.iv,c.ciphertext,c.tag].join(".")}}let bd=async(a,b,c)=>{let d=await a0(a,b,"sign");return ao(a,d),new Uint8Array(await f.subtle.sign(a_(a,d.algorithm),d,c))};class be{constructor(a){if(!(a instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=a}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}async sign(a,b){let c;if(!this._protectedHeader&&!this._unprotectedHeader)throw new B("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!aa(this._protectedHeader,this._unprotectedHeader))throw new B("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let d={...this._protectedHeader,...this._unprotectedHeader},e=aN(B,new Map([["b64",!0]]),null==b?void 0:b.crit,this._protectedHeader,d),f=!0;if(e.has("b64")&&"boolean"!=typeof(f=this._protectedHeader.b64))throw new B('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:g}=d;if("string"!=typeof g||!g)throw new B('JWS "alg" (Algorithm) Header Parameter missing or invalid');aG(g,a,"sign");let k=this._payload;f&&(k=h.encode(q(k)));let l=j(c=this._protectedHeader?h.encode(q(JSON.stringify(this._protectedHeader))):h.encode(""),h.encode("."),k),m={signature:q(await bd(g,a,l)),payload:""};return f&&(m.payload=i.decode(k)),this._unprotectedHeader&&(m.header=this._unprotectedHeader),this._protectedHeader&&(m.protected=i.decode(c)),m}}class bf{constructor(a){this._flattened=new be(a)}setProtectedHeader(a){return this._flattened.setProtectedHeader(a),this}async sign(a,b){let c=await this._flattened.sign(a,b);if(void 0===c.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${c.protected}.${c.payload}.${c.signature}`}}class bg{constructor(a,b,c){this.parent=a,this.key=b,this.options=c}setProtectedHeader(a){if(this.protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this.protectedHeader=a,this}setUnprotectedHeader(a){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=a,this}addSignature(...a){return this.parent.addSignature(...a)}sign(...a){return this.parent.sign(...a)}done(){return this.parent}}class bh{constructor(a){this._signatures=[],this._payload=a}addSignature(a,b){let c=new bg(this,a,b);return this._signatures.push(c),c}async sign(){if(!this._signatures.length)throw new B("at least one signature must be added");let a={signatures:[],payload:""};for(let b=0;b<this._signatures.length;b++){let c=this._signatures[b],d=new be(this._payload);d.setProtectedHeader(c.protectedHeader),d.setUnprotectedHeader(c.unprotectedHeader);let{payload:e,...f}=await d.sign(c.key,c.options);if(0===b)a.payload=e;else if(a.payload!==e)throw new B("inconsistent use of JWS Unencoded Payload (RFC7797)");a.signatures.push(f)}return a}}class bi{constructor(a){if(!ab(a))throw TypeError("JWT Claims Set MUST be an object");this._payload=a}setIssuer(a){return this._payload={...this._payload,iss:a},this}setSubject(a){return this._payload={...this._payload,sub:a},this}setAudience(a){return this._payload={...this._payload,aud:a},this}setJti(a){return this._payload={...this._payload,jti:a},this}setNotBefore(a){return"number"==typeof a?this._payload={...this._payload,nbf:a}:this._payload={...this._payload,nbf:a5(new Date)+a7(a)},this}setExpirationTime(a){return"number"==typeof a?this._payload={...this._payload,exp:a}:this._payload={...this._payload,exp:a5(new Date)+a7(a)},this}setIssuedAt(a){return void 0===a?this._payload={...this._payload,iat:a5(new Date)}:this._payload={...this._payload,iat:a},this}}class bj extends bi{setProtectedHeader(a){return this._protectedHeader=a,this}async sign(a,b){var c;let d=new bf(h.encode(JSON.stringify(this._payload)));if(d.setProtectedHeader(this._protectedHeader),Array.isArray(null==(c=this._protectedHeader)?void 0:c.crit)&&this._protectedHeader.crit.includes("b64")&&!1===this._protectedHeader.b64)throw new C("JWTs MUST NOT use unencoded payload");return d.sign(a,b)}}class bk extends bi{setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setKeyManagementParameters(a){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=a,this}setContentEncryptionKey(a){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=a,this}setInitializationVector(a){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=a,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(a,b){let c=new bc(h.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),c.setProtectedHeader(this._protectedHeader),this._iv&&c.setInitializationVector(this._iv),this._cek&&c.setContentEncryptionKey(this._cek),this._keyManagementParameters&&c.setKeyManagementParameters(this._keyManagementParameters),c.encrypt(a,b)}}let bl=(a,b)=>{if("string"!=typeof a||!a)throw new D(`${b} missing or invalid`)};async function bm(a,b){let c;if(!ab(a))throw TypeError("JWK must be an object");if(null!=b||(b="sha256"),"sha256"!==b&&"sha384"!==b&&"sha512"!==b)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(a.kty){case"EC":bl(a.crv,'"crv" (Curve) Parameter'),bl(a.x,'"x" (X Coordinate) Parameter'),bl(a.y,'"y" (Y Coordinate) Parameter'),c={crv:a.crv,kty:a.kty,x:a.x,y:a.y};break;case"OKP":bl(a.crv,'"crv" (Subtype of Key Pair) Parameter'),bl(a.x,'"x" (Public Key) Parameter'),c={crv:a.crv,kty:a.kty,x:a.x};break;case"RSA":bl(a.e,'"e" (Exponent) Parameter'),bl(a.n,'"n" (Modulus) Parameter'),c={e:a.e,kty:a.kty,n:a.n};break;case"oct":bl(a.k,'"k" (Key Value) Parameter'),c={k:a.k,kty:a.kty};break;default:throw new x('"kty" (Key Type) Parameter missing or unsupported')}let d=h.encode(JSON.stringify(c));return q(await g(b,d))}async function bn(a,b){null!=b||(b="sha256");let c=await bm(a,b);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${b.slice(-3)}:${c}`}async function bo(a,b){let c={...a,...null==b?void 0:b.header};if(!ab(c.jwk))throw new B('"jwk" (JSON Web Key) Header Parameter must be a JSON object');let d=await aF({...c.jwk,ext:!0},c.alg,!0);if(d instanceof Uint8Array||"public"!==d.type)throw new B('"jwk" (JSON Web Key) Header Parameter must be a public key');return d}function bp(a){return a&&"object"==typeof a&&Array.isArray(a.keys)&&a.keys.every(bq)}function bq(a){return ab(a)}class br{constructor(a){if(this._cached=new WeakMap,!bp(a))throw new E("JSON Web Key Set malformed");this._jwks=function(a){return"function"==typeof structuredClone?structuredClone(a):JSON.parse(JSON.stringify(a))}(a)}async getKey(a,b){let{alg:c,kid:d}={...a,...null==b?void 0:b.header},e=function(a){switch("string"==typeof a&&a.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new x('Unsupported "alg" value for a JSON Web Key Set')}}(c),f=this._jwks.keys.filter(a=>{let b=e===a.kty;if(b&&"string"==typeof d&&(b=d===a.kid),b&&"string"==typeof a.alg&&(b=c===a.alg),b&&"string"==typeof a.use&&(b="sig"===a.use),b&&Array.isArray(a.key_ops)&&(b=a.key_ops.includes("verify")),b&&"EdDSA"===c&&(b="Ed25519"===a.crv||"Ed448"===a.crv),b)switch(c){case"ES256":b="P-256"===a.crv;break;case"ES256K":b="secp256k1"===a.crv;break;case"ES384":b="P-384"===a.crv;break;case"ES512":b="P-521"===a.crv}return b}),{0:g,length:h}=f;if(0===h)throw new F;if(1!==h){let a=new G,{_cached:b}=this;throw a[Symbol.asyncIterator]=async function*(){for(let a of f)try{yield await bs(b,a,c)}catch(a){continue}},a}return bs(this._cached,g,c)}}async function bs(a,b,c){let d=a.get(b)||a.set(b,{}).get(b);if(void 0===d[c]){let a=await aF({...b,ext:!0},c);if(a instanceof Uint8Array||"public"!==a.type)throw new E("JSON Web Key Set members must be public keys");d[c]=a}return d[c]}function bt(a){let b=new br(a);return async function(a,c){return b.getKey(a,c)}}let bu=async(a,b,c)=>{let d,e,f=!1;"function"==typeof AbortController&&(d=new AbortController,e=setTimeout(()=>{f=!0,d.abort()},b));let g=await fetch(a.href,{signal:d?d.signal:void 0,redirect:"manual",headers:c.headers}).catch(a=>{if(f)throw new H;throw a});if(void 0!==e&&clearTimeout(e),200!==g.status)throw new t("Expected 200 OK from the JSON Web Key Set HTTP response");try{return await g.json()}catch(a){throw new t("Failed to parse the JSON Web Key Set HTTP response as JSON")}};class bv extends br{constructor(a,b){if(super({keys:[]}),this._jwks=void 0,!(a instanceof URL))throw TypeError("url must be an instance of URL");this._url=new URL(a.href),this._options={agent:null==b?void 0:b.agent,headers:null==b?void 0:b.headers},this._timeoutDuration="number"==typeof(null==b?void 0:b.timeoutDuration)?null==b?void 0:b.timeoutDuration:5e3,this._cooldownDuration="number"==typeof(null==b?void 0:b.cooldownDuration)?null==b?void 0:b.cooldownDuration:3e4,this._cacheMaxAge="number"==typeof(null==b?void 0:b.cacheMaxAge)?null==b?void 0:b.cacheMaxAge:6e5}coolingDown(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cooldownDuration}fresh(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cacheMaxAge}async getKey(a,b){this._jwks&&this.fresh()||await this.reload();try{return await super.getKey(a,b)}catch(c){if(c instanceof F&&!1===this.coolingDown())return await this.reload(),super.getKey(a,b);throw c}}async reload(){this._pendingFetch&&("undefined"!=typeof WebSocketPair||"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent)&&(this._pendingFetch=void 0),this._pendingFetch||(this._pendingFetch=bu(this._url,this._timeoutDuration,this._options).then(a=>{if(!bp(a))throw new E("JSON Web Key Set malformed");this._jwks={keys:a.keys},this._jwksTimestamp=Date.now(),this._pendingFetch=void 0}).catch(a=>{throw this._pendingFetch=void 0,a})),await this._pendingFetch}}function bw(a,b){let c=new bv(a,b);return async function(a,b){return c.getKey(a,b)}}class bx extends bi{encode(){let a=q(JSON.stringify({alg:"none"})),b=q(JSON.stringify(this._payload));return`${a}.${b}.`}static decode(a,b){let c;if("string"!=typeof a)throw new C("Unsecured JWT must be a string");let{0:d,1:e,2:f,length:g}=a.split(".");if(3!==g||""!==f)throw new C("Invalid Unsecured JWT");try{if(c=JSON.parse(i.decode(s(d))),"none"!==c.alg)throw Error()}catch(a){throw new C("Invalid Unsecured JWT")}return{payload:a9(c,s(e),b),header:c}}}let by=q,bz=s;function bA(a){let b;if("string"==typeof a){let c=a.split(".");(3===c.length||5===c.length)&&([b]=c)}else if("object"==typeof a&&a)if("protected"in a)b=a.protected;else throw TypeError("Token does not contain a Protected Header");try{if("string"!=typeof b||!b)throw Error();let a=JSON.parse(i.decode(bz(b)));if(!ab(a))throw Error();return a}catch(a){throw TypeError("Invalid Token or Protected Header formatting")}}function bB(a){let b,c;if("string"!=typeof a)throw new C("JWTs must use Compact JWS serialization, JWT must be a string");let{1:d,length:e}=a.split(".");if(5===e)throw new C("Only JWTs using Compact JWS serialization can be decoded");if(3!==e)throw new C("Invalid JWT");if(!d)throw new C("JWTs must contain a payload");try{b=bz(d)}catch(a){throw new C("Failed to base64url decode the payload")}try{c=JSON.parse(i.decode(b))}catch(a){throw new C("Failed to parse the decoded payload as JSON")}if(!ab(c))throw new C("Invalid JWT Claims Set");return c}async function bC(a,b){var c;let d,e,g;switch(a){case"HS256":case"HS384":case"HS512":d=parseInt(a.slice(-3),10),e={name:"HMAC",hash:`SHA-${d}`,length:d},g=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return J(new Uint8Array((d=parseInt(a.slice(-3),10))>>3));case"A128KW":case"A192KW":case"A256KW":e={name:"AES-KW",length:d=parseInt(a.slice(1,4),10)},g=["wrapKey","unwrapKey"];break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":case"A128GCM":case"A192GCM":case"A256GCM":e={name:"AES-GCM",length:d=parseInt(a.slice(1,4),10)},g=["encrypt","decrypt"];break;default:throw new x('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return f.subtle.generateKey(e,null!=(c=null==b?void 0:b.extractable)&&c,g)}function bD(a){var b;let c=null!=(b=null==a?void 0:a.modulusLength)?b:2048;if("number"!=typeof c||c<2048)throw new x("Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used");return c}async function bE(a,b){var c,d,e;let g,h;switch(a){case"PS256":case"PS384":case"PS512":g={name:"RSA-PSS",hash:`SHA-${a.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:bD(b)},h=["sign","verify"];break;case"RS256":case"RS384":case"RS512":g={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${a.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:bD(b)},h=["sign","verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":g={name:"RSA-OAEP",hash:`SHA-${parseInt(a.slice(-3),10)||1}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:bD(b)},h=["decrypt","unwrapKey","encrypt","wrapKey"];break;case"ES256":g={name:"ECDSA",namedCurve:"P-256"},h=["sign","verify"];break;case"ES384":g={name:"ECDSA",namedCurve:"P-384"},h=["sign","verify"];break;case"ES512":g={name:"ECDSA",namedCurve:"P-521"},h=["sign","verify"];break;case"EdDSA":h=["sign","verify"];let i=null!=(c=null==b?void 0:b.crv)?c:"Ed25519";switch(i){case"Ed25519":case"Ed448":g={name:i};break;default:throw new x("Invalid or unsupported crv option provided")}break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{h=["deriveKey","deriveBits"];let a=null!=(d=null==b?void 0:b.crv)?d:"P-256";switch(a){case"P-256":case"P-384":case"P-521":g={name:"ECDH",namedCurve:a};break;case"X25519":case"X448":g={name:a};break;default:throw new x("Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448")}break}default:throw new x('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return f.subtle.generateKey(g,null!=(e=null==b?void 0:b.extractable)&&e,h)}async function bF(a,b){return bE(a,b)}async function bG(a,b){return bC(a,b)}let bH="WebCryptoAPI"},552:(a,b,c)=>{"use strict";var d=c(356).Buffer;Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{handleFetch:function(){return h},interceptFetch:function(){return i},reader:function(){return f}});let e=c(201),f={url:a=>a.url,header:(a,b)=>a.headers.get(b)};async function g(a,b){let{url:c,method:e,headers:f,body:g,cache:h,credentials:i,integrity:j,mode:k,redirect:l,referrer:m,referrerPolicy:n}=b;return{testData:a,api:"fetch",request:{url:c,method:e,headers:[...Array.from(f),["next-test-stack",function(){let a=(Error().stack??"").split("\n");for(let b=1;b<a.length;b++)if(a[b].length>0){a=a.slice(b);break}return(a=(a=(a=a.filter(a=>!a.includes("/next/dist/"))).slice(0,5)).map(a=>a.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:g?d.from(await b.arrayBuffer()).toString("base64"):null,cache:h,credentials:i,integrity:j,mode:k,redirect:l,referrer:m,referrerPolicy:n}}}async function h(a,b){let c=(0,e.getTestReqInfo)(b,f);if(!c)return a(b);let{testData:h,proxyPort:i}=c,j=await g(h,b),k=await a(`http://localhost:${i}`,{method:"POST",body:JSON.stringify(j),next:{internal:!0}});if(!k.ok)throw Object.defineProperty(Error(`Proxy request failed: ${k.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let l=await k.json(),{api:m}=l;switch(m){case"continue":return a(b);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${b.method} ${b.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:n,headers:o,body:p}=l.response;return new Response(p?d.from(p,"base64"):null,{status:n,headers:new Headers(o)})}function i(a){return c.g.fetch=function(b,c){var d;return(null==c||null==(d=c.next)?void 0:d.internal)?a(b,c):h(a,new Request(b,c))},()=>{c.g.fetch=a}}},556:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0})},710:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,hkdf:()=>f});let d=async(a,b,c,d,e)=>{let{crypto:{subtle:f}}=(()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")})();return new Uint8Array(await f.deriveBits({name:"HKDF",hash:`SHA-${a.substr(3)}`,salt:c,info:d},await f.importKey("raw",b,"HKDF",!1,["deriveBits"]),e<<3))};function e(a,b){if("string"==typeof a)return new TextEncoder().encode(a);if(!(a instanceof Uint8Array))throw TypeError(`"${b}"" must be an instance of Uint8Array or a string`);return a}async function f(a,b,c,f,g){return d(function(a){switch(a){case"sha256":case"sha384":case"sha512":case"sha1":return a;default:throw TypeError('unsupported "digest" value')}}(a),function(a){let b=e(a,"ikm");if(!b.byteLength)throw TypeError('"ikm" must be at least one byte in length');return b}(b),e(c,"salt"),function(a){let b=e(a,"info");if(b.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return b}(f),function(a,b){if("number"!=typeof a||!Number.isInteger(a)||a<1)throw TypeError('"keylen" must be a positive integer');if(a>255*(parseInt(b.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return a}(g,a))}},724:a=>{"use strict";var b=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,e=Object.prototype.hasOwnProperty,f={};function g(a){var b;let c=["path"in a&&a.path&&`Path=${a.path}`,"expires"in a&&(a.expires||0===a.expires)&&`Expires=${("number"==typeof a.expires?new Date(a.expires):a.expires).toUTCString()}`,"maxAge"in a&&"number"==typeof a.maxAge&&`Max-Age=${a.maxAge}`,"domain"in a&&a.domain&&`Domain=${a.domain}`,"secure"in a&&a.secure&&"Secure","httpOnly"in a&&a.httpOnly&&"HttpOnly","sameSite"in a&&a.sameSite&&`SameSite=${a.sameSite}`,"partitioned"in a&&a.partitioned&&"Partitioned","priority"in a&&a.priority&&`Priority=${a.priority}`].filter(Boolean),d=`${a.name}=${encodeURIComponent(null!=(b=a.value)?b:"")}`;return 0===c.length?d:`${d}; ${c.join("; ")}`}function h(a){let b=new Map;for(let c of a.split(/; */)){if(!c)continue;let a=c.indexOf("=");if(-1===a){b.set(c,"true");continue}let[d,e]=[c.slice(0,a),c.slice(a+1)];try{b.set(d,decodeURIComponent(null!=e?e:"true"))}catch{}}return b}function i(a){if(!a)return;let[[b,c],...d]=h(a),{domain:e,expires:f,httponly:g,maxage:i,path:l,samesite:m,secure:n,partitioned:o,priority:p}=Object.fromEntries(d.map(([a,b])=>[a.toLowerCase().replace(/-/g,""),b]));{var q,r,s={name:b,value:decodeURIComponent(c),domain:e,...f&&{expires:new Date(f)},...g&&{httpOnly:!0},..."string"==typeof i&&{maxAge:Number(i)},path:l,...m&&{sameSite:j.includes(q=(q=m).toLowerCase())?q:void 0},...n&&{secure:!0},...p&&{priority:k.includes(r=(r=p).toLowerCase())?r:void 0},...o&&{partitioned:!0}};let a={};for(let b in s)s[b]&&(a[b]=s[b]);return a}}((a,c)=>{for(var d in c)b(a,d,{get:c[d],enumerable:!0})})(f,{RequestCookies:()=>l,ResponseCookies:()=>m,parseCookie:()=>h,parseSetCookie:()=>i,stringifyCookie:()=>g}),a.exports=((a,f,g,h)=>{if(f&&"object"==typeof f||"function"==typeof f)for(let i of d(f))e.call(a,i)||i===g||b(a,i,{get:()=>f[i],enumerable:!(h=c(f,i))||h.enumerable});return a})(b({},"__esModule",{value:!0}),f);var j=["strict","lax","none"],k=["low","medium","high"],l=class{constructor(a){this._parsed=new Map,this._headers=a;let b=a.get("cookie");if(b)for(let[a,c]of h(b))this._parsed.set(a,{name:a,value:c})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed);if(!a.length)return c.map(([a,b])=>b);let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(([a])=>a===d).map(([a,b])=>b)}has(a){return this._parsed.has(a)}set(...a){let[b,c]=1===a.length?[a[0].name,a[0].value]:a,d=this._parsed;return d.set(b,{name:b,value:c}),this._headers.set("cookie",Array.from(d).map(([a,b])=>g(b)).join("; ")),this}delete(a){let b=this._parsed,c=Array.isArray(a)?a.map(a=>b.delete(a)):b.delete(a);return this._headers.set("cookie",Array.from(b).map(([a,b])=>g(b)).join("; ")),c}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a=>`${a.name}=${encodeURIComponent(a.value)}`).join("; ")}},m=class{constructor(a){var b,c,d;this._parsed=new Map,this._headers=a;let e=null!=(d=null!=(c=null==(b=a.getSetCookie)?void 0:b.call(a))?c:a.get("set-cookie"))?d:[];for(let a of Array.isArray(e)?e:function(a){if(!a)return[];var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}(e)){let b=i(a);b&&this._parsed.set(b.name,b)}}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed.values());if(!a.length)return c;let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(a=>a.name===d)}has(a){return this._parsed.has(a)}set(...a){let[b,c,d]=1===a.length?[a[0].name,a[0].value,a[0]]:a,e=this._parsed;return e.set(b,function(a={name:"",value:""}){return"number"==typeof a.expires&&(a.expires=new Date(a.expires)),a.maxAge&&(a.expires=new Date(Date.now()+1e3*a.maxAge)),(null===a.path||void 0===a.path)&&(a.path="/"),a}({name:b,value:c,...d})),function(a,b){for(let[,c]of(b.delete("set-cookie"),a)){let a=g(c);b.append("set-cookie",a)}}(e,this._headers),this}delete(...a){let[b,c]="string"==typeof a[0]?[a[0]]:[a[0].name,a[0]];return this.set({...c,name:b,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(g).join("; ")}}},802:a=>{(()=>{"use strict";var b={993:a=>{var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),(new d).__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},213:a=>{a.exports=(a,b)=>(b=b||(()=>{}),a.then(a=>new Promise(a=>{a(b())}).then(()=>a),a=>new Promise(a=>{a(b())}).then(()=>{throw a})))},574:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a,b,c){let d=0,e=a.length;for(;e>0;){let f=e/2|0,g=d+f;0>=c(a[g],b)?(d=++g,e-=f+1):e=f}return d}},821:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0});let d=c(574);class e{constructor(){this._queue=[]}enqueue(a,b){let c={priority:(b=Object.assign({priority:0},b)).priority,run:a};if(this.size&&this._queue[this.size-1].priority>=b.priority)return void this._queue.push(c);let e=d.default(this._queue,c,(a,b)=>b.priority-a.priority);this._queue.splice(e,0,c)}dequeue(){let a=this._queue.shift();return null==a?void 0:a.run}filter(a){return this._queue.filter(b=>b.priority===a.priority).map(a=>a.run)}get size(){return this._queue.length}}b.default=e},816:(a,b,c)=>{let d=c(213);class e extends Error{constructor(a){super(a),this.name="TimeoutError"}}let f=(a,b,c)=>new Promise((f,g)=>{if("number"!=typeof b||b<0)throw TypeError("Expected `milliseconds` to be a positive number");if(b===1/0)return void f(a);let h=setTimeout(()=>{if("function"==typeof c){try{f(c())}catch(a){g(a)}return}let d="string"==typeof c?c:`Promise timed out after ${b} milliseconds`,h=c instanceof Error?c:new e(d);"function"==typeof a.cancel&&a.cancel(),g(h)},b);d(a.then(f,g),()=>{clearTimeout(h)})});a.exports=f,a.exports.default=f,a.exports.TimeoutError=e}},c={};function d(a){var e=c[a];if(void 0!==e)return e.exports;var f=c[a]={exports:{}},g=!0;try{b[a](f,f.exports,d),g=!1}finally{g&&delete c[a]}return f.exports}d.ab="//";var e={};(()=>{Object.defineProperty(e,"__esModule",{value:!0});let a=d(993),b=d(816),c=d(821),f=()=>{},g=new b.TimeoutError;class h extends a{constructor(a){var b,d,e,g;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=f,this._resolveIdle=f,!("number"==typeof(a=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:c.default},a)).intervalCap&&a.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(d=null==(b=a.intervalCap)?void 0:b.toString())?d:""}\` (${typeof a.intervalCap})`);if(void 0===a.interval||!(Number.isFinite(a.interval)&&a.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(g=null==(e=a.interval)?void 0:e.toString())?g:""}\` (${typeof a.interval})`);this._carryoverConcurrencyCount=a.carryoverConcurrencyCount,this._isIntervalIgnored=a.intervalCap===1/0||0===a.interval,this._intervalCap=a.intervalCap,this._interval=a.interval,this._queue=new a.queueClass,this._queueClass=a.queueClass,this.concurrency=a.concurrency,this._timeout=a.timeout,this._throwOnTimeout=!0===a.throwOnTimeout,this._isPaused=!1===a.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=f,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=f,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let a=Date.now();if(void 0===this._intervalId){let b=this._intervalEnd-a;if(!(b<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},b)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let a=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let b=this._queue.dequeue();return!!b&&(this.emit("active"),b(),a&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(a){if(!("number"==typeof a&&a>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${a}\` (${typeof a})`);this._concurrency=a,this._processQueue()}async add(a,c={}){return new Promise((d,e)=>{let f=async()=>{this._pendingCount++,this._intervalCount++;try{let f=void 0===this._timeout&&void 0===c.timeout?a():b.default(Promise.resolve(a()),void 0===c.timeout?this._timeout:c.timeout,()=>{(void 0===c.throwOnTimeout?this._throwOnTimeout:c.throwOnTimeout)&&e(g)});d(await f)}catch(a){e(a)}this._next()};this._queue.enqueue(f,c),this._tryToStartAnother(),this.emit("add")})}async addAll(a,b){return Promise.all(a.map(async a=>this.add(a,b)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(a=>{let b=this._resolveEmpty;this._resolveEmpty=()=>{b(),a()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(a=>{let b=this._resolveIdle;this._resolveIdle=()=>{b(),a()}})}get size(){return this._queue.size}sizeBy(a){return this._queue.filter(a).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(a){this._timeout=a}}e.default=h})(),a.exports=e})()},815:(a,b,c)=>{"use strict";a.exports=c(35)},890:a=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var b={};(()=>{b.parse=function(b,c){if("string"!=typeof b)throw TypeError("argument str must be a string");for(var e={},f=b.split(d),g=(c||{}).decode||a,h=0;h<f.length;h++){var i=f[h],j=i.indexOf("=");if(!(j<0)){var k=i.substr(0,j).trim(),l=i.substr(++j,i.length).trim();'"'==l[0]&&(l=l.slice(1,-1)),void 0==e[k]&&(e[k]=function(a,b){try{return b(a)}catch(b){return a}}(l,g))}}return e},b.serialize=function(a,b,d){var f=d||{},g=f.encode||c;if("function"!=typeof g)throw TypeError("option encode is invalid");if(!e.test(a))throw TypeError("argument name is invalid");var h=g(b);if(h&&!e.test(h))throw TypeError("argument val is invalid");var i=a+"="+h;if(null!=f.maxAge){var j=f.maxAge-0;if(isNaN(j)||!isFinite(j))throw TypeError("option maxAge is invalid");i+="; Max-Age="+Math.floor(j)}if(f.domain){if(!e.test(f.domain))throw TypeError("option domain is invalid");i+="; Domain="+f.domain}if(f.path){if(!e.test(f.path))throw TypeError("option path is invalid");i+="; Path="+f.path}if(f.expires){if("function"!=typeof f.expires.toUTCString)throw TypeError("option expires is invalid");i+="; Expires="+f.expires.toUTCString()}if(f.httpOnly&&(i+="; HttpOnly"),f.secure&&(i+="; Secure"),f.sameSite)switch("string"==typeof f.sameSite?f.sameSite.toLowerCase():f.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return i};var a=decodeURIComponent,c=encodeURIComponent,d=/; */,e=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),a.exports=b})()},905:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{interceptTestApis:function(){return f},wrapRequestHandler:function(){return g}});let d=c(201),e=c(552);function f(){return(0,e.interceptFetch)(c.g.fetch)}function g(a){return(b,c)=>(0,d.withRequest)(b,e.reader,()=>a(b,c))}},921:a=>{a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports},928:(a,b,c)=>{"use strict";c.r(b),c.d(b,{NIL:()=>D,parse:()=>q,stringify:()=>m,v1:()=>p,v3:()=>z,v4:()=>A,v5:()=>C,validate:()=>j,version:()=>E});var d,e,f,g=new Uint8Array(16);function h(){if(!d&&!(d="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return d(g)}let i=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,j=function(a){return"string"==typeof a&&i.test(a)};for(var k=[],l=0;l<256;++l)k.push((l+256).toString(16).substr(1));let m=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=(k[a[b+0]]+k[a[b+1]]+k[a[b+2]]+k[a[b+3]]+"-"+k[a[b+4]]+k[a[b+5]]+"-"+k[a[b+6]]+k[a[b+7]]+"-"+k[a[b+8]]+k[a[b+9]]+"-"+k[a[b+10]]+k[a[b+11]]+k[a[b+12]]+k[a[b+13]]+k[a[b+14]]+k[a[b+15]]).toLowerCase();if(!j(c))throw TypeError("Stringified UUID is invalid");return c};var n=0,o=0;let p=function(a,b,c){var d=b&&c||0,g=b||Array(16),i=(a=a||{}).node||e,j=void 0!==a.clockseq?a.clockseq:f;if(null==i||null==j){var k=a.random||(a.rng||h)();null==i&&(i=e=[1|k[0],k[1],k[2],k[3],k[4],k[5]]),null==j&&(j=f=(k[6]<<8|k[7])&16383)}var l=void 0!==a.msecs?a.msecs:Date.now(),p=void 0!==a.nsecs?a.nsecs:o+1,q=l-n+(p-o)/1e4;if(q<0&&void 0===a.clockseq&&(j=j+1&16383),(q<0||l>n)&&void 0===a.nsecs&&(p=0),p>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");n=l,o=p,f=j;var r=((0xfffffff&(l+=122192928e5))*1e4+p)%0x100000000;g[d++]=r>>>24&255,g[d++]=r>>>16&255,g[d++]=r>>>8&255,g[d++]=255&r;var s=l/0x100000000*1e4&0xfffffff;g[d++]=s>>>8&255,g[d++]=255&s,g[d++]=s>>>24&15|16,g[d++]=s>>>16&255,g[d++]=j>>>8|128,g[d++]=255&j;for(var t=0;t<6;++t)g[d+t]=i[t];return b||m(g)},q=function(a){if(!j(a))throw TypeError("Invalid UUID");var b,c=new Uint8Array(16);return c[0]=(b=parseInt(a.slice(0,8),16))>>>24,c[1]=b>>>16&255,c[2]=b>>>8&255,c[3]=255&b,c[4]=(b=parseInt(a.slice(9,13),16))>>>8,c[5]=255&b,c[6]=(b=parseInt(a.slice(14,18),16))>>>8,c[7]=255&b,c[8]=(b=parseInt(a.slice(19,23),16))>>>8,c[9]=255&b,c[10]=(b=parseInt(a.slice(24,36),16))/0x10000000000&255,c[11]=b/0x100000000&255,c[12]=b>>>24&255,c[13]=b>>>16&255,c[14]=b>>>8&255,c[15]=255&b,c};function r(a,b,c){function d(a,d,e,f){if("string"==typeof a&&(a=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=0;c<a.length;++c)b.push(a.charCodeAt(c));return b}(a)),"string"==typeof d&&(d=q(d)),16!==d.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var g=new Uint8Array(16+a.length);if(g.set(d),g.set(a,d.length),(g=c(g))[6]=15&g[6]|b,g[8]=63&g[8]|128,e){f=f||0;for(var h=0;h<16;++h)e[f+h]=g[h];return e}return m(g)}try{d.name=a}catch(a){}return d.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",d.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",d}function s(a){return(a+64>>>9<<4)+14+1}function t(a,b){var c=(65535&a)+(65535&b);return(a>>16)+(b>>16)+(c>>16)<<16|65535&c}function u(a,b,c,d,e,f){var g;return t((g=t(t(b,a),t(d,f)))<<e|g>>>32-e,c)}function v(a,b,c,d,e,f,g){return u(b&c|~b&d,a,b,e,f,g)}function w(a,b,c,d,e,f,g){return u(b&d|c&~d,a,b,e,f,g)}function x(a,b,c,d,e,f,g){return u(b^c^d,a,b,e,f,g)}function y(a,b,c,d,e,f,g){return u(c^(b|~d),a,b,e,f,g)}let z=r("v3",48,function(a){if("string"==typeof a){var b=unescape(encodeURIComponent(a));a=new Uint8Array(b.length);for(var c=0;c<b.length;++c)a[c]=b.charCodeAt(c)}return function(a){for(var b=[],c=32*a.length,d="0123456789abcdef",e=0;e<c;e+=8){var f=a[e>>5]>>>e%32&255,g=parseInt(d.charAt(f>>>4&15)+d.charAt(15&f),16);b.push(g)}return b}(function(a,b){a[b>>5]|=128<<b%32,a[s(b)-1]=b;for(var c=0x67452301,d=-0x10325477,e=-0x67452302,f=0x10325476,g=0;g<a.length;g+=16){var h=c,i=d,j=e,k=f;c=v(c,d,e,f,a[g],7,-0x28955b88),f=v(f,c,d,e,a[g+1],12,-0x173848aa),e=v(e,f,c,d,a[g+2],17,0x242070db),d=v(d,e,f,c,a[g+3],22,-0x3e423112),c=v(c,d,e,f,a[g+4],7,-0xa83f051),f=v(f,c,d,e,a[g+5],12,0x4787c62a),e=v(e,f,c,d,a[g+6],17,-0x57cfb9ed),d=v(d,e,f,c,a[g+7],22,-0x2b96aff),c=v(c,d,e,f,a[g+8],7,0x698098d8),f=v(f,c,d,e,a[g+9],12,-0x74bb0851),e=v(e,f,c,d,a[g+10],17,-42063),d=v(d,e,f,c,a[g+11],22,-0x76a32842),c=v(c,d,e,f,a[g+12],7,0x6b901122),f=v(f,c,d,e,a[g+13],12,-0x2678e6d),e=v(e,f,c,d,a[g+14],17,-0x5986bc72),d=v(d,e,f,c,a[g+15],22,0x49b40821),c=w(c,d,e,f,a[g+1],5,-0x9e1da9e),f=w(f,c,d,e,a[g+6],9,-0x3fbf4cc0),e=w(e,f,c,d,a[g+11],14,0x265e5a51),d=w(d,e,f,c,a[g],20,-0x16493856),c=w(c,d,e,f,a[g+5],5,-0x29d0efa3),f=w(f,c,d,e,a[g+10],9,0x2441453),e=w(e,f,c,d,a[g+15],14,-0x275e197f),d=w(d,e,f,c,a[g+4],20,-0x182c0438),c=w(c,d,e,f,a[g+9],5,0x21e1cde6),f=w(f,c,d,e,a[g+14],9,-0x3cc8f82a),e=w(e,f,c,d,a[g+3],14,-0xb2af279),d=w(d,e,f,c,a[g+8],20,0x455a14ed),c=w(c,d,e,f,a[g+13],5,-0x561c16fb),f=w(f,c,d,e,a[g+2],9,-0x3105c08),e=w(e,f,c,d,a[g+7],14,0x676f02d9),d=w(d,e,f,c,a[g+12],20,-0x72d5b376),c=x(c,d,e,f,a[g+5],4,-378558),f=x(f,c,d,e,a[g+8],11,-0x788e097f),e=x(e,f,c,d,a[g+11],16,0x6d9d6122),d=x(d,e,f,c,a[g+14],23,-0x21ac7f4),c=x(c,d,e,f,a[g+1],4,-0x5b4115bc),f=x(f,c,d,e,a[g+4],11,0x4bdecfa9),e=x(e,f,c,d,a[g+7],16,-0x944b4a0),d=x(d,e,f,c,a[g+10],23,-0x41404390),c=x(c,d,e,f,a[g+13],4,0x289b7ec6),f=x(f,c,d,e,a[g],11,-0x155ed806),e=x(e,f,c,d,a[g+3],16,-0x2b10cf7b),d=x(d,e,f,c,a[g+6],23,0x4881d05),c=x(c,d,e,f,a[g+9],4,-0x262b2fc7),f=x(f,c,d,e,a[g+12],11,-0x1924661b),e=x(e,f,c,d,a[g+15],16,0x1fa27cf8),d=x(d,e,f,c,a[g+2],23,-0x3b53a99b),c=y(c,d,e,f,a[g],6,-0xbd6ddbc),f=y(f,c,d,e,a[g+7],10,0x432aff97),e=y(e,f,c,d,a[g+14],15,-0x546bdc59),d=y(d,e,f,c,a[g+5],21,-0x36c5fc7),c=y(c,d,e,f,a[g+12],6,0x655b59c3),f=y(f,c,d,e,a[g+3],10,-0x70f3336e),e=y(e,f,c,d,a[g+10],15,-1051523),d=y(d,e,f,c,a[g+1],21,-0x7a7ba22f),c=y(c,d,e,f,a[g+8],6,0x6fa87e4f),f=y(f,c,d,e,a[g+15],10,-0x1d31920),e=y(e,f,c,d,a[g+6],15,-0x5cfebcec),d=y(d,e,f,c,a[g+13],21,0x4e0811a1),c=y(c,d,e,f,a[g+4],6,-0x8ac817e),f=y(f,c,d,e,a[g+11],10,-0x42c50dcb),e=y(e,f,c,d,a[g+2],15,0x2ad7d2bb),d=y(d,e,f,c,a[g+9],21,-0x14792c6f),c=t(c,h),d=t(d,i),e=t(e,j),f=t(f,k)}return[c,d,e,f]}(function(a){if(0===a.length)return[];for(var b=8*a.length,c=new Uint32Array(s(b)),d=0;d<b;d+=8)c[d>>5]|=(255&a[d/8])<<d%32;return c}(a),8*a.length))}),A=function(a,b,c){var d=(a=a||{}).random||(a.rng||h)();if(d[6]=15&d[6]|64,d[8]=63&d[8]|128,b){c=c||0;for(var e=0;e<16;++e)b[c+e]=d[e];return b}return m(d)};function B(a,b){return a<<b|a>>>32-b}let C=r("v5",80,function(a){var b=[0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xca62c1d6],c=[0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0];if("string"==typeof a){var d=unescape(encodeURIComponent(a));a=[];for(var e=0;e<d.length;++e)a.push(d.charCodeAt(e))}else Array.isArray(a)||(a=Array.prototype.slice.call(a));a.push(128);for(var f=Math.ceil((a.length/4+2)/16),g=Array(f),h=0;h<f;++h){for(var i=new Uint32Array(16),j=0;j<16;++j)i[j]=a[64*h+4*j]<<24|a[64*h+4*j+1]<<16|a[64*h+4*j+2]<<8|a[64*h+4*j+3];g[h]=i}g[f-1][14]=(a.length-1)*8/0x100000000,g[f-1][14]=Math.floor(g[f-1][14]),g[f-1][15]=(a.length-1)*8|0;for(var k=0;k<f;++k){for(var l=new Uint32Array(80),m=0;m<16;++m)l[m]=g[k][m];for(var n=16;n<80;++n)l[n]=B(l[n-3]^l[n-8]^l[n-14]^l[n-16],1);for(var o=c[0],p=c[1],q=c[2],r=c[3],s=c[4],t=0;t<80;++t){var u=Math.floor(t/20),v=B(o,5)+function(a,b,c,d){switch(a){case 0:return b&c^~b&d;case 1:case 3:return b^c^d;case 2:return b&c^b&d^c&d}}(u,p,q,r)+s+b[u]+l[t]>>>0;s=r,r=q,q=B(p,30)>>>0,p=o,o=v}c[0]=c[0]+o>>>0,c[1]=c[1]+p>>>0,c[2]=c[2]+q>>>0,c[3]=c[3]+r>>>0,c[4]=c[4]+s>>>0}return[c[0]>>24&255,c[0]>>16&255,c[0]>>8&255,255&c[0],c[1]>>24&255,c[1]>>16&255,c[1]>>8&255,255&c[1],c[2]>>24&255,c[2]>>16&255,c[2]>>8&255,255&c[2],c[3]>>24&255,c[3]>>16&255,c[3]>>8&255,255&c[3],c[4]>>24&255,c[4]>>16&255,c[4]>>8&255,255&c[4]]}),D="00000000-0000-0000-0000-000000000000",E=function(a){if(!j(a))throw TypeError("Invalid UUID");return parseInt(a.substr(14,1),16)}},956:(a,b,c)=>{(()=>{"use strict";var b={491:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ContextAPI=void 0;let d=c(223),e=c(172),f=c(930),g="context",h=new d.NoopContextManager;class i{constructor(){}static getInstance(){return this._instance||(this._instance=new i),this._instance}setGlobalContextManager(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}active(){return this._getContextManager().active()}with(a,b,c,...d){return this._getContextManager().with(a,b,c,...d)}bind(a,b){return this._getContextManager().bind(a,b)}_getContextManager(){return(0,e.getGlobal)(g)||h}disable(){this._getContextManager().disable(),(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.ContextAPI=i},930:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagAPI=void 0;let d=c(56),e=c(912),f=c(957),g=c(172);class h{constructor(){function a(a){return function(...b){let c=(0,g.getGlobal)("diag");if(c)return c[a](...b)}}let b=this;b.setLogger=(a,c={logLevel:f.DiagLogLevel.INFO})=>{var d,h,i;if(a===b){let a=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return b.error(null!=(d=a.stack)?d:a.message),!1}"number"==typeof c&&(c={logLevel:c});let j=(0,g.getGlobal)("diag"),k=(0,e.createLogLevelDiagLogger)(null!=(h=c.logLevel)?h:f.DiagLogLevel.INFO,a);if(j&&!c.suppressOverrideMessage){let a=null!=(i=Error().stack)?i:"<failed to generate stacktrace>";j.warn(`Current logger will be overwritten from ${a}`),k.warn(`Current logger will overwrite one already registered from ${a}`)}return(0,g.registerGlobal)("diag",k,b,!0)},b.disable=()=>{(0,g.unregisterGlobal)("diag",b)},b.createComponentLogger=a=>new d.DiagComponentLogger(a),b.verbose=a("verbose"),b.debug=a("debug"),b.info=a("info"),b.warn=a("warn"),b.error=a("error")}static instance(){return this._instance||(this._instance=new h),this._instance}}b.DiagAPI=h},653:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.MetricsAPI=void 0;let d=c(660),e=c(172),f=c(930),g="metrics";class h{constructor(){}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalMeterProvider(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}getMeterProvider(){return(0,e.getGlobal)(g)||d.NOOP_METER_PROVIDER}getMeter(a,b,c){return this.getMeterProvider().getMeter(a,b,c)}disable(){(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.MetricsAPI=h},181:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.PropagationAPI=void 0;let d=c(172),e=c(874),f=c(194),g=c(277),h=c(369),i=c(930),j="propagation",k=new e.NoopTextMapPropagator;class l{constructor(){this.createBaggage=h.createBaggage,this.getBaggage=g.getBaggage,this.getActiveBaggage=g.getActiveBaggage,this.setBaggage=g.setBaggage,this.deleteBaggage=g.deleteBaggage}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalPropagator(a){return(0,d.registerGlobal)(j,a,i.DiagAPI.instance())}inject(a,b,c=f.defaultTextMapSetter){return this._getGlobalPropagator().inject(a,b,c)}extract(a,b,c=f.defaultTextMapGetter){return this._getGlobalPropagator().extract(a,b,c)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,d.unregisterGlobal)(j,i.DiagAPI.instance())}_getGlobalPropagator(){return(0,d.getGlobal)(j)||k}}b.PropagationAPI=l},997:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceAPI=void 0;let d=c(172),e=c(846),f=c(139),g=c(607),h=c(930),i="trace";class j{constructor(){this._proxyTracerProvider=new e.ProxyTracerProvider,this.wrapSpanContext=f.wrapSpanContext,this.isSpanContextValid=f.isSpanContextValid,this.deleteSpan=g.deleteSpan,this.getSpan=g.getSpan,this.getActiveSpan=g.getActiveSpan,this.getSpanContext=g.getSpanContext,this.setSpan=g.setSpan,this.setSpanContext=g.setSpanContext}static getInstance(){return this._instance||(this._instance=new j),this._instance}setGlobalTracerProvider(a){let b=(0,d.registerGlobal)(i,this._proxyTracerProvider,h.DiagAPI.instance());return b&&this._proxyTracerProvider.setDelegate(a),b}getTracerProvider(){return(0,d.getGlobal)(i)||this._proxyTracerProvider}getTracer(a,b){return this.getTracerProvider().getTracer(a,b)}disable(){(0,d.unregisterGlobal)(i,h.DiagAPI.instance()),this._proxyTracerProvider=new e.ProxyTracerProvider}}b.TraceAPI=j},277:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.deleteBaggage=b.setBaggage=b.getActiveBaggage=b.getBaggage=void 0;let d=c(491),e=(0,c(780).createContextKey)("OpenTelemetry Baggage Key");function f(a){return a.getValue(e)||void 0}b.getBaggage=f,b.getActiveBaggage=function(){return f(d.ContextAPI.getInstance().active())},b.setBaggage=function(a,b){return a.setValue(e,b)},b.deleteBaggage=function(a){return a.deleteValue(e)}},993:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.BaggageImpl=void 0;class c{constructor(a){this._entries=a?new Map(a):new Map}getEntry(a){let b=this._entries.get(a);if(b)return Object.assign({},b)}getAllEntries(){return Array.from(this._entries.entries()).map(([a,b])=>[a,b])}setEntry(a,b){let d=new c(this._entries);return d._entries.set(a,b),d}removeEntry(a){let b=new c(this._entries);return b._entries.delete(a),b}removeEntries(...a){let b=new c(this._entries);for(let c of a)b._entries.delete(c);return b}clear(){return new c}}b.BaggageImpl=c},830:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataSymbol=void 0,b.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataFromString=b.createBaggage=void 0;let d=c(930),e=c(993),f=c(830),g=d.DiagAPI.instance();b.createBaggage=function(a={}){return new e.BaggageImpl(new Map(Object.entries(a)))},b.baggageEntryMetadataFromString=function(a){return"string"!=typeof a&&(g.error(`Cannot create baggage metadata from unknown type: ${typeof a}`),a=""),{__TYPE__:f.baggageEntryMetadataSymbol,toString:()=>a}}},67:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.context=void 0,b.context=c(491).ContextAPI.getInstance()},223:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopContextManager=void 0;let d=c(780);class e{active(){return d.ROOT_CONTEXT}with(a,b,c,...d){return b.call(c,...d)}bind(a,b){return b}enable(){return this}disable(){return this}}b.NoopContextManager=e},780:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ROOT_CONTEXT=b.createContextKey=void 0,b.createContextKey=function(a){return Symbol.for(a)};class c{constructor(a){let b=this;b._currentContext=a?new Map(a):new Map,b.getValue=a=>b._currentContext.get(a),b.setValue=(a,d)=>{let e=new c(b._currentContext);return e._currentContext.set(a,d),e},b.deleteValue=a=>{let d=new c(b._currentContext);return d._currentContext.delete(a),d}}}b.ROOT_CONTEXT=new c},506:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.diag=void 0,b.diag=c(930).DiagAPI.instance()},56:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagComponentLogger=void 0;let d=c(172);class e{constructor(a){this._namespace=a.namespace||"DiagComponentLogger"}debug(...a){return f("debug",this._namespace,a)}error(...a){return f("error",this._namespace,a)}info(...a){return f("info",this._namespace,a)}warn(...a){return f("warn",this._namespace,a)}verbose(...a){return f("verbose",this._namespace,a)}}function f(a,b,c){let e=(0,d.getGlobal)("diag");if(e)return c.unshift(b),e[a](...c)}b.DiagComponentLogger=e},972:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagConsoleLogger=void 0;let c=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class d{constructor(){for(let a=0;a<c.length;a++)this[c[a].n]=function(a){return function(...b){if(console){let c=console[a];if("function"!=typeof c&&(c=console.log),"function"==typeof c)return c.apply(console,b)}}}(c[a].c)}}b.DiagConsoleLogger=d},912:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createLogLevelDiagLogger=void 0;let d=c(957);b.createLogLevelDiagLogger=function(a,b){function c(c,d){let e=b[c];return"function"==typeof e&&a>=d?e.bind(b):function(){}}return a<d.DiagLogLevel.NONE?a=d.DiagLogLevel.NONE:a>d.DiagLogLevel.ALL&&(a=d.DiagLogLevel.ALL),b=b||{},{error:c("error",d.DiagLogLevel.ERROR),warn:c("warn",d.DiagLogLevel.WARN),info:c("info",d.DiagLogLevel.INFO),debug:c("debug",d.DiagLogLevel.DEBUG),verbose:c("verbose",d.DiagLogLevel.VERBOSE)}}},957:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagLogLevel=void 0,function(a){a[a.NONE=0]="NONE",a[a.ERROR=30]="ERROR",a[a.WARN=50]="WARN",a[a.INFO=60]="INFO",a[a.DEBUG=70]="DEBUG",a[a.VERBOSE=80]="VERBOSE",a[a.ALL=9999]="ALL"}(b.DiagLogLevel||(b.DiagLogLevel={}))},172:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.unregisterGlobal=b.getGlobal=b.registerGlobal=void 0;let d=c(200),e=c(521),f=c(130),g=e.VERSION.split(".")[0],h=Symbol.for(`opentelemetry.js.api.${g}`),i=d._globalThis;b.registerGlobal=function(a,b,c,d=!1){var f;let g=i[h]=null!=(f=i[h])?f:{version:e.VERSION};if(!d&&g[a]){let b=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${a}`);return c.error(b.stack||b.message),!1}if(g.version!==e.VERSION){let b=Error(`@opentelemetry/api: Registration of version v${g.version} for ${a} does not match previously registered API v${e.VERSION}`);return c.error(b.stack||b.message),!1}return g[a]=b,c.debug(`@opentelemetry/api: Registered a global for ${a} v${e.VERSION}.`),!0},b.getGlobal=function(a){var b,c;let d=null==(b=i[h])?void 0:b.version;if(d&&(0,f.isCompatible)(d))return null==(c=i[h])?void 0:c[a]},b.unregisterGlobal=function(a,b){b.debug(`@opentelemetry/api: Unregistering a global for ${a} v${e.VERSION}.`);let c=i[h];c&&delete c[a]}},130:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.isCompatible=b._makeCompatibilityCheck=void 0;let d=c(521),e=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function f(a){let b=new Set([a]),c=new Set,d=a.match(e);if(!d)return()=>!1;let f={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=f.prerelease)return function(b){return b===a};function g(a){return c.add(a),!1}return function(a){if(b.has(a))return!0;if(c.has(a))return!1;let d=a.match(e);if(!d)return g(a);let h={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=h.prerelease||f.major!==h.major)return g(a);if(0===f.major)return f.minor===h.minor&&f.patch<=h.patch?(b.add(a),!0):g(a);return f.minor<=h.minor?(b.add(a),!0):g(a)}}b._makeCompatibilityCheck=f,b.isCompatible=f(d.VERSION)},886:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.metrics=void 0,b.metrics=c(653).MetricsAPI.getInstance()},901:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ValueType=void 0,function(a){a[a.INT=0]="INT",a[a.DOUBLE=1]="DOUBLE"}(b.ValueType||(b.ValueType={}))},102:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createNoopMeter=b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=b.NOOP_OBSERVABLE_GAUGE_METRIC=b.NOOP_OBSERVABLE_COUNTER_METRIC=b.NOOP_UP_DOWN_COUNTER_METRIC=b.NOOP_HISTOGRAM_METRIC=b.NOOP_COUNTER_METRIC=b.NOOP_METER=b.NoopObservableUpDownCounterMetric=b.NoopObservableGaugeMetric=b.NoopObservableCounterMetric=b.NoopObservableMetric=b.NoopHistogramMetric=b.NoopUpDownCounterMetric=b.NoopCounterMetric=b.NoopMetric=b.NoopMeter=void 0;class c{constructor(){}createHistogram(a,c){return b.NOOP_HISTOGRAM_METRIC}createCounter(a,c){return b.NOOP_COUNTER_METRIC}createUpDownCounter(a,c){return b.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(a,c){return b.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(a,c){return b.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(a,c){return b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(a,b){}removeBatchObservableCallback(a){}}b.NoopMeter=c;class d{}b.NoopMetric=d;class e extends d{add(a,b){}}b.NoopCounterMetric=e;class f extends d{add(a,b){}}b.NoopUpDownCounterMetric=f;class g extends d{record(a,b){}}b.NoopHistogramMetric=g;class h{addCallback(a){}removeCallback(a){}}b.NoopObservableMetric=h;class i extends h{}b.NoopObservableCounterMetric=i;class j extends h{}b.NoopObservableGaugeMetric=j;class k extends h{}b.NoopObservableUpDownCounterMetric=k,b.NOOP_METER=new c,b.NOOP_COUNTER_METRIC=new e,b.NOOP_HISTOGRAM_METRIC=new g,b.NOOP_UP_DOWN_COUNTER_METRIC=new f,b.NOOP_OBSERVABLE_COUNTER_METRIC=new i,b.NOOP_OBSERVABLE_GAUGE_METRIC=new j,b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new k,b.createNoopMeter=function(){return b.NOOP_METER}},660:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NOOP_METER_PROVIDER=b.NoopMeterProvider=void 0;let d=c(102);class e{getMeter(a,b,c){return d.NOOP_METER}}b.NoopMeterProvider=e,b.NOOP_METER_PROVIDER=new e},200:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(46),b)},651:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b._globalThis=void 0,b._globalThis="object"==typeof globalThis?globalThis:c.g},46:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(651),b)},939:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.propagation=void 0,b.propagation=c(181).PropagationAPI.getInstance()},874:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTextMapPropagator=void 0;class c{inject(a,b){}extract(a,b){return a}fields(){return[]}}b.NoopTextMapPropagator=c},194:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.defaultTextMapSetter=b.defaultTextMapGetter=void 0,b.defaultTextMapGetter={get(a,b){if(null!=a)return a[b]},keys:a=>null==a?[]:Object.keys(a)},b.defaultTextMapSetter={set(a,b,c){null!=a&&(a[b]=c)}}},845:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.trace=void 0,b.trace=c(997).TraceAPI.getInstance()},403:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NonRecordingSpan=void 0;let d=c(476);class e{constructor(a=d.INVALID_SPAN_CONTEXT){this._spanContext=a}spanContext(){return this._spanContext}setAttribute(a,b){return this}setAttributes(a){return this}addEvent(a,b){return this}setStatus(a){return this}updateName(a){return this}end(a){}isRecording(){return!1}recordException(a,b){}}b.NonRecordingSpan=e},614:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracer=void 0;let d=c(491),e=c(607),f=c(403),g=c(139),h=d.ContextAPI.getInstance();class i{startSpan(a,b,c=h.active()){var d;if(null==b?void 0:b.root)return new f.NonRecordingSpan;let i=c&&(0,e.getSpanContext)(c);return"object"==typeof(d=i)&&"string"==typeof d.spanId&&"string"==typeof d.traceId&&"number"==typeof d.traceFlags&&(0,g.isSpanContextValid)(i)?new f.NonRecordingSpan(i):new f.NonRecordingSpan}startActiveSpan(a,b,c,d){let f,g,i;if(arguments.length<2)return;2==arguments.length?i=b:3==arguments.length?(f=b,i=c):(f=b,g=c,i=d);let j=null!=g?g:h.active(),k=this.startSpan(a,f,j),l=(0,e.setSpan)(j,k);return h.with(l,i,void 0,k)}}b.NoopTracer=i},124:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracerProvider=void 0;let d=c(614);class e{getTracer(a,b,c){return new d.NoopTracer}}b.NoopTracerProvider=e},125:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracer=void 0;let d=new(c(614)).NoopTracer;class e{constructor(a,b,c,d){this._provider=a,this.name=b,this.version=c,this.options=d}startSpan(a,b,c){return this._getTracer().startSpan(a,b,c)}startActiveSpan(a,b,c,d){let e=this._getTracer();return Reflect.apply(e.startActiveSpan,e,arguments)}_getTracer(){if(this._delegate)return this._delegate;let a=this._provider.getDelegateTracer(this.name,this.version,this.options);return a?(this._delegate=a,this._delegate):d}}b.ProxyTracer=e},846:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracerProvider=void 0;let d=c(125),e=new(c(124)).NoopTracerProvider;class f{getTracer(a,b,c){var e;return null!=(e=this.getDelegateTracer(a,b,c))?e:new d.ProxyTracer(this,a,b,c)}getDelegate(){var a;return null!=(a=this._delegate)?a:e}setDelegate(a){this._delegate=a}getDelegateTracer(a,b,c){var d;return null==(d=this._delegate)?void 0:d.getTracer(a,b,c)}}b.ProxyTracerProvider=f},996:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SamplingDecision=void 0,function(a){a[a.NOT_RECORD=0]="NOT_RECORD",a[a.RECORD=1]="RECORD",a[a.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(b.SamplingDecision||(b.SamplingDecision={}))},607:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.getSpanContext=b.setSpanContext=b.deleteSpan=b.setSpan=b.getActiveSpan=b.getSpan=void 0;let d=c(780),e=c(403),f=c(491),g=(0,d.createContextKey)("OpenTelemetry Context Key SPAN");function h(a){return a.getValue(g)||void 0}function i(a,b){return a.setValue(g,b)}b.getSpan=h,b.getActiveSpan=function(){return h(f.ContextAPI.getInstance().active())},b.setSpan=i,b.deleteSpan=function(a){return a.deleteValue(g)},b.setSpanContext=function(a,b){return i(a,new e.NonRecordingSpan(b))},b.getSpanContext=function(a){var b;return null==(b=h(a))?void 0:b.spanContext()}},325:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceStateImpl=void 0;let d=c(564);class e{constructor(a){this._internalState=new Map,a&&this._parse(a)}set(a,b){let c=this._clone();return c._internalState.has(a)&&c._internalState.delete(a),c._internalState.set(a,b),c}unset(a){let b=this._clone();return b._internalState.delete(a),b}get(a){return this._internalState.get(a)}serialize(){return this._keys().reduce((a,b)=>(a.push(b+"="+this.get(b)),a),[]).join(",")}_parse(a){!(a.length>512)&&(this._internalState=a.split(",").reverse().reduce((a,b)=>{let c=b.trim(),e=c.indexOf("=");if(-1!==e){let f=c.slice(0,e),g=c.slice(e+1,b.length);(0,d.validateKey)(f)&&(0,d.validateValue)(g)&&a.set(f,g)}return a},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let a=new e;return a._internalState=new Map(this._internalState),a}}b.TraceStateImpl=e},564:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.validateValue=b.validateKey=void 0;let c="[_0-9a-z-*/]",d=`[a-z]${c}{0,255}`,e=`[a-z0-9]${c}{0,240}@[a-z]${c}{0,13}`,f=RegExp(`^(?:${d}|${e})$`),g=/^[ -~]{0,255}[!-~]$/,h=/,|=/;b.validateKey=function(a){return f.test(a)},b.validateValue=function(a){return g.test(a)&&!h.test(a)}},98:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createTraceState=void 0;let d=c(325);b.createTraceState=function(a){return new d.TraceStateImpl(a)}},476:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.INVALID_SPAN_CONTEXT=b.INVALID_TRACEID=b.INVALID_SPANID=void 0;let d=c(475);b.INVALID_SPANID="0000000000000000",b.INVALID_TRACEID="00000000000000000000000000000000",b.INVALID_SPAN_CONTEXT={traceId:b.INVALID_TRACEID,spanId:b.INVALID_SPANID,traceFlags:d.TraceFlags.NONE}},357:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanKind=void 0,function(a){a[a.INTERNAL=0]="INTERNAL",a[a.SERVER=1]="SERVER",a[a.CLIENT=2]="CLIENT",a[a.PRODUCER=3]="PRODUCER",a[a.CONSUMER=4]="CONSUMER"}(b.SpanKind||(b.SpanKind={}))},139:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.wrapSpanContext=b.isSpanContextValid=b.isValidSpanId=b.isValidTraceId=void 0;let d=c(476),e=c(403),f=/^([0-9a-f]{32})$/i,g=/^[0-9a-f]{16}$/i;function h(a){return f.test(a)&&a!==d.INVALID_TRACEID}function i(a){return g.test(a)&&a!==d.INVALID_SPANID}b.isValidTraceId=h,b.isValidSpanId=i,b.isSpanContextValid=function(a){return h(a.traceId)&&i(a.spanId)},b.wrapSpanContext=function(a){return new e.NonRecordingSpan(a)}},847:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanStatusCode=void 0,function(a){a[a.UNSET=0]="UNSET",a[a.OK=1]="OK",a[a.ERROR=2]="ERROR"}(b.SpanStatusCode||(b.SpanStatusCode={}))},475:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceFlags=void 0,function(a){a[a.NONE=0]="NONE",a[a.SAMPLED=1]="SAMPLED"}(b.TraceFlags||(b.TraceFlags={}))},521:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.VERSION=void 0,b.VERSION="1.6.0"}},d={};function e(a){var c=d[a];if(void 0!==c)return c.exports;var f=d[a]={exports:{}},g=!0;try{b[a].call(f.exports,f,f.exports,e),g=!1}finally{g&&delete d[a]}return f.exports}e.ab="//";var f={};(()=>{Object.defineProperty(f,"__esModule",{value:!0}),f.trace=f.propagation=f.metrics=f.diag=f.context=f.INVALID_SPAN_CONTEXT=f.INVALID_TRACEID=f.INVALID_SPANID=f.isValidSpanId=f.isValidTraceId=f.isSpanContextValid=f.createTraceState=f.TraceFlags=f.SpanStatusCode=f.SpanKind=f.SamplingDecision=f.ProxyTracerProvider=f.ProxyTracer=f.defaultTextMapSetter=f.defaultTextMapGetter=f.ValueType=f.createNoopMeter=f.DiagLogLevel=f.DiagConsoleLogger=f.ROOT_CONTEXT=f.createContextKey=f.baggageEntryMetadataFromString=void 0;var a=e(369);Object.defineProperty(f,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return a.baggageEntryMetadataFromString}});var b=e(780);Object.defineProperty(f,"createContextKey",{enumerable:!0,get:function(){return b.createContextKey}}),Object.defineProperty(f,"ROOT_CONTEXT",{enumerable:!0,get:function(){return b.ROOT_CONTEXT}});var c=e(972);Object.defineProperty(f,"DiagConsoleLogger",{enumerable:!0,get:function(){return c.DiagConsoleLogger}});var d=e(957);Object.defineProperty(f,"DiagLogLevel",{enumerable:!0,get:function(){return d.DiagLogLevel}});var g=e(102);Object.defineProperty(f,"createNoopMeter",{enumerable:!0,get:function(){return g.createNoopMeter}});var h=e(901);Object.defineProperty(f,"ValueType",{enumerable:!0,get:function(){return h.ValueType}});var i=e(194);Object.defineProperty(f,"defaultTextMapGetter",{enumerable:!0,get:function(){return i.defaultTextMapGetter}}),Object.defineProperty(f,"defaultTextMapSetter",{enumerable:!0,get:function(){return i.defaultTextMapSetter}});var j=e(125);Object.defineProperty(f,"ProxyTracer",{enumerable:!0,get:function(){return j.ProxyTracer}});var k=e(846);Object.defineProperty(f,"ProxyTracerProvider",{enumerable:!0,get:function(){return k.ProxyTracerProvider}});var l=e(996);Object.defineProperty(f,"SamplingDecision",{enumerable:!0,get:function(){return l.SamplingDecision}});var m=e(357);Object.defineProperty(f,"SpanKind",{enumerable:!0,get:function(){return m.SpanKind}});var n=e(847);Object.defineProperty(f,"SpanStatusCode",{enumerable:!0,get:function(){return n.SpanStatusCode}});var o=e(475);Object.defineProperty(f,"TraceFlags",{enumerable:!0,get:function(){return o.TraceFlags}});var p=e(98);Object.defineProperty(f,"createTraceState",{enumerable:!0,get:function(){return p.createTraceState}});var q=e(139);Object.defineProperty(f,"isSpanContextValid",{enumerable:!0,get:function(){return q.isSpanContextValid}}),Object.defineProperty(f,"isValidTraceId",{enumerable:!0,get:function(){return q.isValidTraceId}}),Object.defineProperty(f,"isValidSpanId",{enumerable:!0,get:function(){return q.isValidSpanId}});var r=e(476);Object.defineProperty(f,"INVALID_SPANID",{enumerable:!0,get:function(){return r.INVALID_SPANID}}),Object.defineProperty(f,"INVALID_TRACEID",{enumerable:!0,get:function(){return r.INVALID_TRACEID}}),Object.defineProperty(f,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return r.INVALID_SPAN_CONTEXT}});let s=e(67);Object.defineProperty(f,"context",{enumerable:!0,get:function(){return s.context}});let t=e(506);Object.defineProperty(f,"diag",{enumerable:!0,get:function(){return t.diag}});let u=e(886);Object.defineProperty(f,"metrics",{enumerable:!0,get:function(){return u.metrics}});let v=e(939);Object.defineProperty(f,"propagation",{enumerable:!0,get:function(){return v.propagation}});let w=e(845);Object.defineProperty(f,"trace",{enumerable:!0,get:function(){return w.trace}}),f.default={context:s.context,diag:t.diag,metrics:u.metrics,propagation:v.propagation,trace:w.trace}})(),a.exports=f})()},991:(a,b,c)=>{"use strict";var d=c(921);Object.defineProperty(b,"__esModule",{value:!0});var e={encode:!0,decode:!0,getToken:!0};b.decode=l,b.encode=k,b.getToken=m;var f=c(537),g=d(c(710)),h=c(928),i=c(131),j=c(556);async function k(a){let{token:b={},secret:c,maxAge:d=2592e3,salt:e=""}=a,g=await n(c,e);return await new f.EncryptJWT(b).setProtectedHeader({alg:"dir",enc:"A256GCM"}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+d).setJti((0,h.v4)()).encrypt(g)}async function l(a){let{token:b,secret:c,salt:d=""}=a;if(!b)return null;let e=await n(c,d),{payload:g}=await (0,f.jwtDecrypt)(b,e,{clockTolerance:15});return g}async function m(a){var b,c,d,e;let{req:f,secureCookie:g=null!=(b=null==(c=process.env.NEXTAUTH_URL)?void 0:c.startsWith("https://"))?b:!!process.env.VERCEL,cookieName:h=g?"__Secure-next-auth.session-token":"next-auth.session-token",raw:j,decode:k=l,logger:m=console,secret:n=null!=(d=process.env.NEXTAUTH_SECRET)?d:process.env.AUTH_SECRET}=a;if(!f)throw Error("Must pass `req` to JWT getToken()");let o=new i.SessionStore({name:h,options:{secure:g}},{cookies:f.cookies,headers:f.headers},m).value,p=f.headers instanceof Headers?f.headers.get("authorization"):null==(e=f.headers)?void 0:e.authorization;if(o||(null==p?void 0:p.split(" ")[0])!=="Bearer"||(o=decodeURIComponent(p.split(" ")[1])),!o)return null;if(j)return o;try{return await k({token:o,secret:n})}catch(a){return null}}async function n(a,b){return await (0,g.default)("sha256",a,b,`NextAuth.js Generated Encryption Key${b?` (${b})`:""}`,32)}Object.keys(j).forEach(function(a){!("default"===a||"__esModule"===a||Object.prototype.hasOwnProperty.call(e,a))&&(a in b&&b[a]===j[a]||Object.defineProperty(b,a,{enumerable:!0,get:function(){return j[a]}}))})}},a=>{var b=a(a.s=0);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=b}]);
//# sourceMappingURL=middleware.js.map