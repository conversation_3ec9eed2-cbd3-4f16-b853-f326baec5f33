(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[895],{2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(5155),i=a(2115),s=a(9434);let l=i.forwardRef((e,t)=>{let{className:a,type:i,...l}=e;return(0,r.jsx)("input",{type:i,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...l})});l.displayName="Input"},2877:(e,t,a)=>{Promise.resolve().then(a.bind(a,8197))},2921:(e,t,a)=>{"use strict";a.d(t,{x:()=>s});var r=a(5453),i=a(6786);let s=(0,r.v)()((0,i.Zr)((e,t)=>({items:[],isOpen:!1,addItem:a=>{let r=t().items,i=r.findIndex(e=>{var t,r;return e.productId===a.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(r=a.variant)?void 0:r.id)});if(i>-1){let t=r[i],s=t.quantity+(a.quantity||1),l=Math.min(a.maxQuantity,t.maxQuantity);e({items:r.map((e,t)=>t===i?{...e,quantity:Math.min(s,l)}:e)})}else{var s;e({items:[...r,{...a,id:"".concat(a.productId,"-").concat((null==(s=a.variant)?void 0:s.id)||"default","-").concat(Date.now()),quantity:a.quantity||1}]})}},removeItem:a=>{e({items:t().items.filter(e=>e.id!==a)})},updateQuantity:(a,r)=>{if(r<=0)return void t().removeItem(a);e({items:t().items.map(e=>e.id===a?{...e,quantity:Math.min(r,e.maxQuantity)}:e)})},clearCart:()=>{e({items:[]})},toggleCart:()=>{e({isOpen:!t().isOpen})},openCart:()=>{e({isOpen:!0})},closeCart:()=>{e({isOpen:!1})},get totalItems(){return t().items.reduce((e,t)=>e+t.quantity,0)},get subtotal(){return t().items.reduce((e,t)=>{var a;return e+((null==(a=t.variant)?void 0:a.price)||t.price)*t.quantity},0)},get tax(){return .08*t().subtotal},get shipping(){return t().subtotal>=100?0:10},get total(){return t().subtotal+t().tax+t().shipping}}),{name:"cart-storage",partialize:e=>({items:e.items})}))},6468:(e,t,a)=>{"use strict";a.d(t,{q:()=>s});var r=a(5453),i=a(6786);let s=(0,r.v)()((0,i.Zr)((e,t)=>({items:[],addItem:a=>{let r=t().items;if(-1===r.findIndex(e=>{var t,r;return e.productId===a.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(r=a.variant)?void 0:r.id)})){var i;e({items:[...r,{...a,id:"".concat(a.productId,"-").concat((null==(i=a.variant)?void 0:i.id)||"default","-").concat(Date.now()),addedAt:new Date}]})}},removeItem:a=>{e({items:t().items.filter(e=>e.id!==a)})},toggleItem:a=>{let r=t().items,i=r.findIndex(e=>{var t,r;return e.productId===a.productId&&(null==(t=e.variant)?void 0:t.id)===(null==(r=a.variant)?void 0:r.id)});return i>-1?(e({items:r.filter((e,t)=>t!==i)}),!1):(t().addItem(a),!0)},clearWishlist:()=>{e({items:[]})},isInWishlist:(e,a)=>t().items.some(t=>{var r;return t.productId===e&&(null==(r=t.variant)?void 0:r.id)===a}),get totalItems(){return t().items.length}}),{name:"wishlist-storage",partialize:e=>({items:e.items})}))},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>n,wL:()=>m});var r=a(5155),i=a(2115),s=a(9434);let l=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i})});l.displayName="Card";let n=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...i})});n.displayName="CardHeader";let c=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",a),...i})});c.displayName="CardTitle";let d=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",a),...i})});d.displayName="CardDescription";let o=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",a),...i})});o.displayName="CardContent";let m=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",a),...i})});m.displayName="CardFooter"},8197:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var r=a(5155),i=a(2115),s=a(6874),l=a.n(s),n=a(285),c=a(2523),d=a(6695),o=a(4817),m=a(7481),u=a(2921),p=a(6468),g=a(9434),x=a(1976),h=a(8564),v=a(7809),f=a(7924),j=a(257),y=a(4653),b=a(5968);let N=[{id:"1",name:"Premium Wireless Headphones",slug:"premium-wireless-headphones",price:199.99,comparePrice:249.99,rating:4.8,reviewCount:124,image:"/placeholder-product.jpg",category:"Electronics",inStock:!0,featured:!0,description:"High-quality wireless headphones with noise cancellation"},{id:"2",name:"Smart Fitness Watch",slug:"smart-fitness-watch",price:299.99,comparePrice:null,rating:4.6,reviewCount:89,image:"/placeholder-product.jpg",category:"Electronics",inStock:!0,featured:!1,description:"Advanced fitness tracking with heart rate monitoring"},{id:"3",name:"Organic Cotton T-Shirt",slug:"organic-cotton-t-shirt",price:29.99,comparePrice:39.99,rating:4.4,reviewCount:67,image:"/placeholder-product.jpg",category:"Clothing",inStock:!0,featured:!1,description:"Comfortable organic cotton t-shirt in various colors"},{id:"4",name:"Professional Camera Lens",slug:"professional-camera-lens",price:899.99,comparePrice:null,rating:4.9,reviewCount:45,image:"/placeholder-product.jpg",category:"Photography",inStock:!1,featured:!0,description:"Professional grade camera lens for stunning photography"},{id:"5",name:"Ergonomic Office Chair",slug:"ergonomic-office-chair",price:449.99,comparePrice:599.99,rating:4.7,reviewCount:156,image:"/placeholder-product.jpg",category:"Furniture",inStock:!0,featured:!1,description:"Comfortable ergonomic chair for long work sessions"},{id:"6",name:"Wireless Charging Pad",slug:"wireless-charging-pad",price:49.99,comparePrice:null,rating:4.3,reviewCount:203,image:"/placeholder-product.jpg",category:"Electronics",inStock:!0,featured:!1,description:"Fast wireless charging for compatible devices"}],w=["All Categories","Electronics","Clothing","Photography","Furniture","Home & Garden","Sports & Outdoors"],C=[{value:"featured",label:"Featured"},{value:"price-low",label:"Price: Low to High"},{value:"price-high",label:"Price: High to Low"},{value:"rating",label:"Highest Rated"},{value:"newest",label:"Newest"}];function k(){let[e,t]=(0,i.useState)(""),[a,s]=(0,i.useState)("All Categories"),[k,S]=(0,i.useState)("featured"),[I,P]=(0,i.useState)("grid"),[A,D]=(0,i.useState)(!1),[O,R]=(0,i.useState)({min:0,max:1e3}),[q,W]=(0,i.useState)(0),{t:$}=(0,o.o)(),{toast:z}=(0,m.dj)(),{addItem:E,openCart:F}=(0,u.x)(),{toggleItem:M,isInWishlist:H}=(0,p.q)(),L=N.filter(t=>{let r=t.name.toLowerCase().includes(e.toLowerCase()),i="All Categories"===a||t.category===a,s=t.price>=O.min&&t.price<=O.max,l=0===q||t.rating>=q;return r&&i&&s&&l}),Q=e=>{let{product:t}=e;return(0,r.jsxs)(d.Zp,{className:"group hover:shadow-lg transition-all duration-300",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-gray-400 text-4xl",children:"\uD83D\uDCE6"})}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2",children:[(0,r.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>(e=>{let t=M({productId:e.id,name:e.name,slug:e.slug,price:e.price,comparePrice:e.comparePrice,image:e.image,inStock:e.inStock,category:e.category,rating:e.rating});z({title:t?"Added to Wishlist":"Removed from Wishlist",description:"".concat(e.name," has been ").concat(t?"added to":"removed from"," your wishlist")})})(t),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),(0,r.jsx)(l(),{href:"/product/".concat(t.slug),children:(0,r.jsx)(n.$,{size:"sm",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"absolute top-2 left-2 space-y-1",children:[t.featured&&(0,r.jsx)("span",{className:"bg-primary-500 text-white px-2 py-1 text-xs rounded",children:"Featured"}),t.comparePrice&&(0,r.jsx)("span",{className:"bg-red-500 text-white px-2 py-1 text-xs rounded",children:"Sale"}),!t.inStock&&(0,r.jsx)("span",{className:"bg-gray-500 text-white px-2 py-1 text-xs rounded",children:"Out of Stock"})]})]}),(0,r.jsx)(d.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm line-clamp-2",children:(0,r.jsx)(l(),{href:"/product/".concat(t.slug),className:"hover:text-primary-600 transition-colors",children:t.name})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2",children:t.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,r.jsx)(h.A,{className:"h-3 w-3 ".concat(a<Math.floor(t.rating)?"text-yellow-400 fill-current":"text-gray-300")},a))}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[t.rating," (",t.reviewCount,")"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"font-bold text-lg",children:(0,g.$g)(t.price)}),t.comparePrice&&(0,r.jsx)("span",{className:"text-sm text-gray-500 line-through",children:(0,g.$g)(t.comparePrice)})]}),(0,r.jsxs)(n.$,{className:"w-full",size:"sm",onClick:()=>{E({productId:t.id,name:t.name,slug:t.slug,price:t.price,image:t.image,inStock:t.inStock,maxQuantity:10}),z({title:"Added to Cart",description:"".concat(t.name," has been added to your cart")}),setTimeout(()=>F(),100)},disabled:!t.inStock,children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),t.inStock?"Add to Cart":"Out of Stock"]})]})})]})};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Shop"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Discover our amazing products"})]}),(0,r.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.p,{placeholder:"Search products...",value:e,onChange:e=>t(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,r.jsx)("select",{value:a,onChange:e=>s(e.target.value),className:"px-3 py-2 border rounded-md bg-background",children:w.map(e=>(0,r.jsx)("option",{value:e,children:e},e))}),(0,r.jsx)("select",{value:k,onChange:e=>S(e.target.value),className:"px-3 py-2 border rounded-md bg-background",children:C.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>D(!A),children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Filters"]}),(0,r.jsxs)("div",{className:"flex border rounded-md",children:[(0,r.jsx)(n.$,{variant:"grid"===I?"default":"ghost",size:"sm",onClick:()=>P("grid"),className:"rounded-r-none",children:(0,r.jsx)(y.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"list"===I?"default":"ghost",size:"sm",onClick:()=>P("list"),className:"rounded-l-none",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"ml-auto text-sm text-gray-500",children:[L.length," products found"]})]}),A&&(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Price Range"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.p,{type:"number",placeholder:"Min",value:O.min,onChange:e=>R(t=>({...t,min:Number(e.target.value)}))}),(0,r.jsx)("span",{children:"-"}),(0,r.jsx)(c.p,{type:"number",placeholder:"Max",value:O.max,onChange:e=>R(t=>({...t,max:Number(e.target.value)}))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Minimum Rating"}),(0,r.jsxs)("select",{value:q,onChange:e=>W(Number(e.target.value)),className:"w-full px-3 py-2 border rounded-md bg-background",children:[(0,r.jsx)("option",{value:0,children:"Any Rating"}),(0,r.jsx)("option",{value:4,children:"4+ Stars"}),(0,r.jsx)("option",{value:4.5,children:"4.5+ Stars"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Availability"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"mr-2",defaultChecked:!0}),"In Stock"]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"mr-2"}),"Out of Stock"]})]})]})]})})})]}),(0,r.jsx)("div",{className:"grid gap-6 ".concat("grid"===I?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4":"grid-cols-1"),children:L.map(e=>(0,r.jsx)(Q,{product:e},e.id))}),0===L.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"No products found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Try adjusting your search or filter criteria"}),(0,r.jsx)(n.$,{onClick:()=>{t(""),s("All Categories"),R({min:0,max:1e3}),W(0)},children:"Clear Filters"})]}),L.length>0&&(0,r.jsx)("div",{className:"text-center mt-12",children:(0,r.jsx)(n.$,{variant:"outline",size:"lg",children:"Load More Products"})})]})})}}},e=>{e.O(0,[274,420,197,441,964,358],()=>e(e.s=2877)),_N_E=e.O()}]);