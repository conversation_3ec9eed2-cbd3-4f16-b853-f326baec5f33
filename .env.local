# Database
DATABASE_URL="postgresql://username:password@localhost:5432/aidevcommerce"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Development settings
DISABLE_ESLINT_PLUGIN=true
NEXT_TELEMETRY_DISABLED=1

# OAuth Providers
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
FACEBOOK_CLIENT_ID=""
FACEBOOK_CLIENT_SECRET=""
APPLE_CLIENT_ID=""
APPLE_CLIENT_SECRET=""

# Stripe
STRIPE_PUBLISHABLE_KEY=""
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Email (Resend)
RESEND_API_KEY=""
FROM_EMAIL="<EMAIL>"

# OpenAI
OPENAI_API_KEY=""

# Supabase (for file storage)
NEXT_PUBLIC_SUPABASE_URL=""
NEXT_PUBLIC_SUPABASE_ANON_KEY=""
SUPABASE_SERVICE_ROLE_KEY=""

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="AIDEVCOMMERCE"
NEXT_PUBLIC_APP_DESCRIPTION="Advanced E-commerce Platform"

# Security
JWT_SECRET="your-jwt-secret-here"
ENCRYPTION_KEY="your-encryption-key-here"

# Analytics
NEXT_PUBLIC_GA_ID=""

# Feature Flags
NEXT_PUBLIC_ENABLE_AI_FEATURES="true"
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
NEXT_PUBLIC_ENABLE_PWA="true"
