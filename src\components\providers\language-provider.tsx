"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { LANGUAGES } from '@/config/constants'

type Language = keyof typeof LANGUAGES
type Direction = 'ltr' | 'rtl'

interface LanguageContextType {
  language: Language
  direction: Direction
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, string>) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Simple translation function (in a real app, you'd use a proper i18n library)
const translations: Record<Language, Record<string, string>> = {
  en: {
    'nav.home': 'Home',
    'nav.shop': 'Shop',
    'nav.services': 'Services',
    'nav.production-lines': 'Production Lines',
    'nav.blog': 'Blog',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'auth.login': 'Login',
    'auth.register': 'Register',
    'auth.logout': 'Logout',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.view-all': 'View All',
    'common.read-more': 'Read More',
    'common.add-to-cart': 'Add to Cart',
    'common.buy-now': 'Buy Now',
    'common.out-of-stock': 'Out of Stock',
    'common.in-stock': 'In Stock',
    'common.price': 'Price',
    'common.quantity': 'Quantity',
    'common.total': 'Total',
    'common.subtotal': 'Subtotal',
    'common.shipping': 'Shipping',
    'common.tax': 'Tax',
    'common.discount': 'Discount',
  },
  ar: {
    'nav.home': 'الرئيسية',
    'nav.shop': 'المتجر',
    'nav.services': 'الخدمات',
    'nav.production-lines': 'خطوط الإنتاج',
    'nav.blog': 'المدونة',
    'nav.about': 'من نحن',
    'nav.contact': 'اتصل بنا',
    'auth.login': 'تسجيل الدخول',
    'auth.register': 'إنشاء حساب',
    'auth.logout': 'تسجيل الخروج',
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.search': 'بحث',
    'common.filter': 'تصفية',
    'common.sort': 'ترتيب',
    'common.view-all': 'عرض الكل',
    'common.read-more': 'اقرأ المزيد',
    'common.add-to-cart': 'أضف للسلة',
    'common.buy-now': 'اشتري الآن',
    'common.out-of-stock': 'غير متوفر',
    'common.in-stock': 'متوفر',
    'common.price': 'السعر',
    'common.quantity': 'الكمية',
    'common.total': 'المجموع',
    'common.subtotal': 'المجموع الفرعي',
    'common.shipping': 'الشحن',
    'common.tax': 'الضريبة',
    'common.discount': 'الخصم',
  },
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en')
  const [direction, setDirection] = useState<Direction>('ltr')

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    setDirection(LANGUAGES[lang].dir as Direction)
    
    // Update document attributes
    document.documentElement.lang = lang
    document.documentElement.dir = LANGUAGES[lang].dir
    
    // Store in localStorage
    localStorage.setItem('language', lang)
  }

  const t = (key: string, params?: Record<string, string>): string => {
    let translation = translations[language][key] || key
    
    // Simple parameter replacement
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, value)
      })
    }
    
    return translation
  }

  useEffect(() => {
    // Load language from localStorage or detect from browser
    const savedLanguage = localStorage.getItem('language') as Language
    const browserLanguage = navigator.language.startsWith('ar') ? 'ar' : 'en'
    const initialLanguage = savedLanguage || browserLanguage
    
    setLanguage(initialLanguage)
  }, [])

  return (
    <LanguageContext.Provider value={{ language, direction, setLanguage, t }}>
      <div dir={direction} className={direction === 'rtl' ? 'font-arabic' : 'font-sans'}>
        {children}
      </div>
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
