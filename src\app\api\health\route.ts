import { NextResponse } from 'next/server'

// Health check endpoint for monitoring and load balancers
export async function GET() {
  try {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: 'healthy', // In production, check actual database connection
        redis: 'healthy',    // In production, check actual Redis connection
        email: 'healthy',    // In production, check email service
        ai: 'healthy',       // In production, check AI service
      },
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
      }
    }

    return NextResponse.json(healthCheck, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    })
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    )
  }
}

// Detailed health check for internal monitoring
export async function POST() {
  try {
    const detailedCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: await checkDatabase(),
        redis: await checkRedis(),
        email: await checkEmailService(),
        ai: await checkAIService(),
        filesystem: await checkFilesystem(),
        network: await checkNetwork(),
      },
      performance: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      }
    }

    const allHealthy = Object.values(detailedCheck.checks).every(
      check => check.status === 'healthy'
    )

    return NextResponse.json(detailedCheck, {
      status: allHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    })
  } catch (error) {
    console.error('Detailed health check failed:', error)
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 }
    )
  }
}

// Individual service health checks
async function checkDatabase() {
  try {
    // In production, implement actual database connection check
    // const result = await prisma.$queryRaw`SELECT 1`
    return {
      status: 'healthy',
      responseTime: Math.random() * 10, // Mock response time
      lastChecked: new Date().toISOString(),
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Database connection failed',
      lastChecked: new Date().toISOString(),
    }
  }
}

async function checkRedis() {
  try {
    // In production, implement actual Redis connection check
    // const result = await redis.ping()
    return {
      status: 'healthy',
      responseTime: Math.random() * 5, // Mock response time
      lastChecked: new Date().toISOString(),
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Redis connection failed',
      lastChecked: new Date().toISOString(),
    }
  }
}

async function checkEmailService() {
  try {
    // In production, implement actual email service check
    // const result = await emailService.healthCheck()
    return {
      status: 'healthy',
      responseTime: Math.random() * 15, // Mock response time
      lastChecked: new Date().toISOString(),
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Email service failed',
      lastChecked: new Date().toISOString(),
    }
  }
}

async function checkAIService() {
  try {
    // In production, implement actual AI service check
    // const result = await aiService.healthCheck()
    return {
      status: 'healthy',
      responseTime: Math.random() * 20, // Mock response time
      lastChecked: new Date().toISOString(),
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'AI service failed',
      lastChecked: new Date().toISOString(),
    }
  }
}

async function checkFilesystem() {
  try {
    const fs = require('fs').promises
    await fs.access('/tmp', fs.constants.W_OK)
    
    return {
      status: 'healthy',
      writable: true,
      lastChecked: new Date().toISOString(),
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Filesystem check failed',
      lastChecked: new Date().toISOString(),
    }
  }
}

async function checkNetwork() {
  try {
    // Simple network connectivity check
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000), // 5 second timeout
    })
    
    return {
      status: response.ok ? 'healthy' : 'unhealthy',
      responseTime: Date.now(), // In production, measure actual response time
      lastChecked: new Date().toISOString(),
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Network check failed',
      lastChecked: new Date().toISOString(),
    }
  }
}
