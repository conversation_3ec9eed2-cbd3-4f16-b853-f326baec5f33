// AI Service for product recommendations, search, and customer support
import { Product } from "@/types"

export interface AIRecommendation {
  productId: string
  score: number
  reason: string
  category: string
}

export interface AISearchResult {
  products: Product[]
  suggestions: string[]
  filters: {
    categories: string[]
    priceRanges: { min: number; max: number; label: string }[]
    brands: string[]
  }
  totalResults: number
  searchTime: number
}

export interface AIChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  metadata?: {
    products?: string[]
    actions?: string[]
    confidence?: number
  }
}

export interface AIInsight {
  type: 'trend' | 'opportunity' | 'warning' | 'recommendation'
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  actionable: boolean
  data?: unknown
}

class AIService {
  private apiKey: string
  private baseUrl: string

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_AI_API_KEY || 'demo-key'
    this.baseUrl = process.env.NEXT_PUBLIC_AI_BASE_URL || 'https://api.aidevcommerce.com'
  }

  // Product Recommendations
  async getProductRecommendations(
    userId?: string,
    productId?: string,
    category?: string,
    limit: number = 6
  ): Promise<AIRecommendation[]> {
    try {
      // Simulate AI recommendation logic
      const mockRecommendations: AIRecommendation[] = [
        {
          productId: "1",
          score: 0.95,
          reason: "Based on your recent purchases and browsing history",
          category: "Electronics"
        },
        {
          productId: "2",
          score: 0.89,
          reason: "Customers who bought similar items also purchased this",
          category: "Electronics"
        },
        {
          productId: "3",
          score: 0.84,
          reason: "Trending in your preferred category",
          category: "Electronics"
        },
        {
          productId: "4",
          score: 0.78,
          reason: "Highly rated by users with similar preferences",
          category: "Accessories"
        },
        {
          productId: "5",
          score: 0.72,
          reason: "Perfect complement to your recent purchase",
          category: "Accessories"
        },
        {
          productId: "6",
          score: 0.68,
          reason: "Popular choice among similar customers",
          category: "Electronics"
        }
      ]

      // Filter by category if specified
      let filtered = mockRecommendations
      if (category) {
        filtered = mockRecommendations.filter(rec => rec.category === category)
      }

      return filtered.slice(0, limit)
    } catch (error) {
      console.error('AI Recommendations Error:', error)
      return []
    }
  }

  // Intelligent Search
  async intelligentSearch(
    query: string,
    filters?: {
      category?: string
      priceMin?: number
      priceMax?: number
      brand?: string
      rating?: number
    },
    page: number = 1,
    limit: number = 20
  ): Promise<AISearchResult> {
    try {
      // Simulate AI-powered search
      const mockResult: AISearchResult = {
        products: [], // Would be populated with actual products
        suggestions: [
          `${query} accessories`,
          `${query} reviews`,
          `best ${query} 2024`,
          `${query} comparison`,
          `affordable ${query}`
        ],
        filters: {
          categories: ["Electronics", "Accessories", "Computers", "Mobile"],
          priceRanges: [
            { min: 0, max: 50, label: "Under $50" },
            { min: 50, max: 100, label: "$50 - $100" },
            { min: 100, max: 200, label: "$100 - $200" },
            { min: 200, max: 500, label: "$200 - $500" },
            { min: 500, max: 9999, label: "Over $500" }
          ],
          brands: ["Apple", "Samsung", "Sony", "Microsoft", "Google"]
        },
        totalResults: 156,
        searchTime: 0.23
      }

      return mockResult
    } catch (error) {
      console.error('AI Search Error:', error)
      return {
        products: [],
        suggestions: [],
        filters: { categories: [], priceRanges: [], brands: [] },
        totalResults: 0,
        searchTime: 0
      }
    }
  }

  // AI Chat Support
  async chatWithAI(
    messages: AIChatMessage[],
    context?: {
      userId?: string
      currentPage?: string
      cartItems?: string[]
      recentProducts?: string[]
    }
  ): Promise<AIChatMessage> {
    try {
      const lastMessage = messages[messages.length - 1]
      
      // Simulate AI response based on context
      let response = "I'm here to help! "
      
      if (lastMessage.content.toLowerCase().includes('product')) {
        response += "I can help you find the perfect product. What are you looking for?"
      } else if (lastMessage.content.toLowerCase().includes('order')) {
        response += "I can help you with your order. Can you provide your order number?"
      } else if (lastMessage.content.toLowerCase().includes('return')) {
        response += "I can help you with returns. Our return policy allows returns within 30 days."
      } else if (lastMessage.content.toLowerCase().includes('shipping')) {
        response += "We offer free shipping on orders over $50. Standard shipping takes 3-5 business days."
      } else {
        response += "How can I assist you today? I can help with products, orders, returns, and shipping."
      }

      const aiMessage: AIChatMessage = {
        id: `ai-${Date.now()}`,
        role: 'assistant',
        content: response,
        timestamp: new Date(),
        metadata: {
          confidence: 0.85,
          actions: ['search_products', 'check_order', 'contact_support']
        }
      }

      return aiMessage
    } catch (error) {
      console.error('AI Chat Error:', error)
      return {
        id: `ai-error-${Date.now()}`,
        role: 'assistant',
        content: "I'm sorry, I'm having trouble processing your request right now. Please try again or contact our support team.",
        timestamp: new Date(),
        metadata: { confidence: 0 }
      }
    }
  }

  // Business Insights
  async getBusinessInsights(
    timeRange: '7d' | '30d' | '90d' | '1y' = '30d',
    categories?: string[]
  ): Promise<AIInsight[]> {
    try {
      const mockInsights: AIInsight[] = [
        {
          type: 'trend',
          title: 'Electronics Sales Surge',
          description: 'Electronics category showing 35% growth compared to last month. Consider increasing inventory.',
          impact: 'high',
          actionable: true,
          data: { growth: 35, category: 'Electronics' }
        },
        {
          type: 'opportunity',
          title: 'Mobile Accessories Opportunity',
          description: 'High search volume for mobile accessories but low conversion. Optimize product descriptions.',
          impact: 'medium',
          actionable: true,
          data: { searches: 1250, conversions: 45 }
        },
        {
          type: 'warning',
          title: 'Inventory Alert',
          description: 'Premium Wireless Headphones running low on stock. Only 12 units remaining.',
          impact: 'high',
          actionable: true,
          data: { productId: '1', stock: 12, threshold: 20 }
        },
        {
          type: 'recommendation',
          title: 'Price Optimization',
          description: 'Consider adjusting prices for Smart Fitness Watch based on competitor analysis.',
          impact: 'medium',
          actionable: true,
          data: { productId: '2', currentPrice: 299.99, suggestedPrice: 279.99 }
        }
      ]

      return mockInsights
    } catch (error) {
      console.error('AI Insights Error:', error)
      return []
    }
  }

  // Sentiment Analysis
  async analyzeSentiment(text: string): Promise<{
    sentiment: 'positive' | 'negative' | 'neutral'
    confidence: number
    keywords: string[]
  }> {
    try {
      // Simple sentiment analysis simulation
      const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'perfect', 'awesome']
      const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disappointing']
      
      const words = text.toLowerCase().split(/\s+/)
      const positiveCount = words.filter(word => positiveWords.includes(word)).length
      const negativeCount = words.filter(word => negativeWords.includes(word)).length
      
      let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral'
      let confidence = 0.5
      
      if (positiveCount > negativeCount) {
        sentiment = 'positive'
        confidence = Math.min(0.9, 0.5 + (positiveCount - negativeCount) * 0.1)
      } else if (negativeCount > positiveCount) {
        sentiment = 'negative'
        confidence = Math.min(0.9, 0.5 + (negativeCount - positiveCount) * 0.1)
      }
      
      const keywords = [...positiveWords, ...negativeWords].filter(word => 
        words.includes(word)
      )
      
      return { sentiment, confidence, keywords }
    } catch (error) {
      console.error('Sentiment Analysis Error:', error)
      return { sentiment: 'neutral', confidence: 0, keywords: [] }
    }
  }

  // Personalization
  async getPersonalizedContent(
    userId: string,
    contentType: 'homepage' | 'products' | 'blog' | 'services'
  ): Promise<{
    sections: Array<{
      type: string
      title: string
      items: unknown[]
      priority: number
    }>
  }> {
    try {
      // Simulate personalized content
      const mockContent = {
        sections: [
          {
            type: 'recommended_products',
            title: 'Recommended for You',
            items: ['1', '2', '3', '4'],
            priority: 1
          },
          {
            type: 'trending_products',
            title: 'Trending Now',
            items: ['5', '6', '7', '8'],
            priority: 2
          },
          {
            type: 'recently_viewed',
            title: 'Recently Viewed',
            items: ['9', '10', '11'],
            priority: 3
          },
          {
            type: 'blog_posts',
            title: 'Articles You Might Like',
            items: ['blog-1', 'blog-2', 'blog-3'],
            priority: 4
          }
        ]
      }

      return mockContent
    } catch (error) {
      console.error('Personalization Error:', error)
      return { sections: [] }
    }
  }

  // A/B Testing
  async getABTestVariant(
    testId: string,
    userId: string
  ): Promise<{
    variant: string
    config: unknown
  }> {
    try {
      // Simple A/B testing logic
      const hash = this.hashString(userId + testId)
      const variant = hash % 2 === 0 ? 'A' : 'B'
      
      const configs = {
        'homepage_layout': {
          A: { layout: 'standard', heroStyle: 'carousel' },
          B: { layout: 'modern', heroStyle: 'video' }
        },
        'product_page': {
          A: { layout: 'sidebar', reviewsPosition: 'bottom' },
          B: { layout: 'tabs', reviewsPosition: 'side' }
        }
      }
      
      return {
        variant,
        config: configs[testId as keyof typeof configs]?.[variant as 'A' | 'B'] || {}
      }
    } catch (error) {
      console.error('A/B Testing Error:', error)
      return { variant: 'A', config: {} }
    }
  }

  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }
}

export const aiService = new AIService()
export default aiService
