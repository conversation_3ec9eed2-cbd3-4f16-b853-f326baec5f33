(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[536],{381:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},749:e=>{e.exports={style:{fontFamily:"'Tajawal', 'Tajawal Fallback'",fontStyle:"normal"},className:"__className_4beeb2",variable:"__variable_4beeb2"}},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1362:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,N:()=>c});var r=s(2115),i=(e,t,s,r,i,n,a,o)=>{let u=document.documentElement,l=["light","dark"];function c(t){var s;(Array.isArray(e)?e:[e]).forEach(e=>{let s="class"===e,r=s&&n?i.map(e=>n[e]||e):i;s?(u.classList.remove(...r),u.classList.add(n&&n[t]?n[t]:t)):u.setAttribute(e,t)}),s=t,o&&l.includes(s)&&(u.style.colorScheme=s)}if(r)c(r);else try{let e=localStorage.getItem(t)||s,r=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(r)}catch(e){}},n=["light","dark"],a="(prefers-color-scheme: dark)",o=r.createContext(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=r.useContext(o))?e:u},c=e=>r.useContext(o)?r.createElement(r.Fragment,null,e.children):r.createElement(d,{...e}),h=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:s=!1,enableSystem:i=!0,enableColorScheme:u=!0,storageKey:l="theme",themes:c=h,defaultTheme:d=i?"system":"light",attribute:v="data-theme",value:b,children:g,nonce:w,scriptProps:E}=e,[C,x]=r.useState(()=>p(l,d)),[P,T]=r.useState(()=>"system"===C?m():C),S=b?Object.values(b):c,O=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&i&&(t=m());let r=b?b[t]:t,a=s?y(w):null,o=document.documentElement,l=e=>{"class"===e?(o.classList.remove(...S),r&&o.classList.add(r)):e.startsWith("data-")&&(r?o.setAttribute(e,r):o.removeAttribute(e))};if(Array.isArray(v)?v.forEach(l):l(v),u){let e=n.includes(d)?d:null,s=n.includes(t)?t:e;o.style.colorScheme=s}null==a||a()},[w]),k=r.useCallback(e=>{let t="function"==typeof e?e(C):e;x(t);try{localStorage.setItem(l,t)}catch(e){}},[C]),A=r.useCallback(e=>{T(m(e)),"system"===C&&i&&!t&&O("system")},[C,t]);r.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),r.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?x(e.newValue):k(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[k]),r.useEffect(()=>{O(null!=t?t:C)},[t,C]);let D=r.useMemo(()=>({theme:C,setTheme:k,forcedTheme:t,resolvedTheme:"system"===C?P:C,themes:i?[...c,"system"]:c,systemTheme:i?P:void 0}),[C,k,t,P,i,c]);return r.createElement(o.Provider,{value:D},r.createElement(f,{forcedTheme:t,storageKey:l,attribute:v,enableSystem:i,enableColorScheme:u,defaultTheme:d,value:b,themes:c,nonce:w,scriptProps:E}),g)},f=r.memo(e=>{let{forcedTheme:t,storageKey:s,attribute:n,enableSystem:a,enableColorScheme:o,defaultTheme:u,value:l,themes:c,nonce:h,scriptProps:d}=e,f=JSON.stringify([n,s,u,t,c,l,a,o]).slice(1,-1);return r.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(i.toString(),")(").concat(f,")")}})}),p=(e,t)=>{let s;try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},2098:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2922:(e,t,s)=>{"use strict";s.d(t,{E:()=>j});var r="undefined"==typeof window||"Deno"in globalThis;function i(){}function n(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:s="all",exact:r,fetchStatus:i,predicate:n,queryKey:a,stale:o}=e;if(a){if(r){if(t.queryHash!==u(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==s){let e=t.isActive();if("active"===s&&!e||"inactive"===s&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!i||i===t.state.fetchStatus)&&(!n||!!n(t))}function o(e,t){let{exact:s,status:r,predicate:i,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(s){if(l(t.options.mutationKey)!==l(n))return!1}else if(!c(t.options.mutationKey,n))return!1}return(!r||t.state.status===r)&&(!i||!!i(t))}function u(e,t){return(t?.queryKeyHashFn||l)(e)}function l(e){return JSON.stringify(e,(e,t)=>d(t)?Object.keys(t).sort().reduce((e,s)=>(e[s]=t[s],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(s=>c(e[s],t[s]))}function h(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function d(e){if(!f(e))return!1;let t=e.constructor;if(void 0===t)return!0;let s=t.prototype;return!!f(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,s=0){let r=[...e,t];return s&&r.length>s?r.slice(1):r}function y(e,t,s=0){let r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var m=Symbol();function v(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==m?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var b=e=>setTimeout(e,0),g=function(){let e=[],t=0,s=e=>{e()},r=e=>{e()},i=b,n=r=>{t?e.push(r):i(()=>{s(r)})};return{batch:n=>{let a;t++;try{a=n()}finally{--t||(()=>{let t=e;e=[],t.length&&i(()=>{r(()=>{t.forEach(e=>{s(e)})})})})()}return a},batchCalls:e=>(...t)=>{n(()=>{e(...t)})},schedule:n,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{i=e}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},E=new class extends w{#e;#t;#s;constructor(){super(),this.#s=e=>{if(!r&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},C=new class extends w{#r=!0;#t;#s;constructor(){super(),this.#s=e=>{if(!r&&window.addEventListener){let t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#r!==e&&(this.#r=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#r}};function x(e){return Math.min(1e3*2**e,3e4)}function P(e){return(e??"online")!=="online"||C.isOnline()}var T=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function S(e){return e instanceof T}function O(e){let t,s=!1,i=0,n=!1,a=function(){let e,t,s=new Promise((s,r)=>{e=s,t=r});function r(e){Object.assign(s,e),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},s.reject=e=>{r({status:"rejected",reason:e}),t(e)},s}(),o=()=>E.isFocused()&&("always"===e.networkMode||C.isOnline())&&e.canRun(),u=()=>P(e.networkMode)&&e.canRun(),l=s=>{n||(n=!0,e.onSuccess?.(s),t?.(),a.resolve(s))},c=s=>{n||(n=!0,e.onError?.(s),t?.(),a.reject(s))},h=()=>new Promise(s=>{t=e=>{(n||o())&&s(e)},e.onPause?.()}).then(()=>{t=void 0,n||e.onContinue?.()}),d=()=>{let t;if(n)return;let a=0===i?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch(t=>{if(n)return;let a=e.retry??3*!r,u=e.retryDelay??x,l="function"==typeof u?u(i,t):u,f=!0===a||"number"==typeof a&&i<a||"function"==typeof a&&a(i,t);if(s||!f)return void c(t);i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,l)}).then(()=>o()?void 0:h()).then(()=>{s?c(t):d()})})};return{promise:a,cancel:t=>{n||(c(new T(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():h().then(d),a)}}var k=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},A=class extends k{#n;#a;#o;#u;#l;#c;#h;constructor(e){super(),this.#h=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#o=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#n=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,r=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){var s,r;let i=(s=this.state.data,"function"==typeof(r=this.options).structuralSharing?r.structuralSharing(s,e):!1!==r.structuralSharing?function e(t,s){if(t===s)return t;let r=h(t)&&h(s);if(r||d(t)&&d(s)){let i=r?t:Object.keys(t),n=i.length,a=r?s:Object.keys(s),o=a.length,u=r?[]:{},l=new Set(i),c=0;for(let i=0;i<o;i++){let n=r?i:a[i];(!r&&l.has(n)||r)&&void 0===t[n]&&void 0===s[n]?(u[n]=void 0,c++):(u[n]=e(t[n],s[n]),u[n]===t[n]&&void 0!==t[n]&&c++)}return n===o&&c===n?t:u}return s}(s,e):e);return this.#d({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#l?.promise;return this.#l?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(e=>{var t;return!1!==(t=e.options.enabled,"function"==typeof t?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===n(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#l&&(this.#h?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let s=new AbortController,r=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},i=()=>{let e=v(this.options,t),s=(()=>{let e={client:this.#u,queryKey:this.queryKey,meta:this.meta};return r(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,s,this):e(s)},n=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:i};return r(e),e})();this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let a=e=>{S(e)&&e.silent||this.#d({type:"error",error:e}),S(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#l=O({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0===e)return void a(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){a(e);return}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#l.start()}#d(e){let t=t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var s;return{...t,...(s=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:P(this.options.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return this.#a=void 0,{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if(S(r)&&r.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}};this.state=t(this.state),g.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:e})})}},D=class extends w{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,s){let r=t.queryKey,i=t.queryHash??u(r,t),n=this.get(i);return n||(n=new A({client:e,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(r)}),this.add(n)),n}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){g.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){g.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){g.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},F=class extends k{#p;#y;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#y=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#d({type:"continue"})};this.#l=O({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let s="pending"===this.state.status,r=!this.#l.canStart();try{if(s)t();else{this.#d({type:"pending",variables:e,isPaused:r}),await this.#y.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:r})}let i=await this.#l.start();return await this.#y.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#y.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#d({type:"success",data:i}),i}catch(t){try{throw await this.#y.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#y.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#y.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),g.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#y.notify({mutation:this,type:"updated",action:e})})}},q=class extends w{constructor(e={}){super(),this.config=e,this.#m=new Set,this.#v=new Map,this.#b=0}#m;#v;#b;build(e,t,s){let r=new F({mutationCache:this,mutationId:++this.#b,options:e.defaultMutationOptions(t),state:s});return this.add(r),r}add(e){this.#m.add(e);let t=R(e);if("string"==typeof t){let s=this.#v.get(t);s?s.push(e):this.#v.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#m.delete(e)){let t=R(e);if("string"==typeof t){let s=this.#v.get(t);if(s)if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#v.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=R(e);if("string"!=typeof t)return!0;{let s=this.#v.get(t),r=s?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=R(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#v.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){g.batch(()=>{this.#m.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>o(t,e))}findAll(e={}){return this.getAll().filter(t=>o(e,t))}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return g.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function R(e){return e.options.scope?.id}function M(e){return{onFetch:(t,s)=>{let r=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],a=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,l=async()=>{let s=!1,l=v(t.options,t.fetchOptions),c=async(e,r,i)=>{if(s)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:r,direction:i?"backward":"forward",meta:t.options.meta};return Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)}),e})(),a=await l(n),{maxPages:o}=t.options,u=i?y:p;return{pages:u(e.pages,a,o),pageParams:u(e.pageParams,r,o)}};if(i&&n.length){let e="backward"===i,t={pages:n,pageParams:a},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:L)(r,t);o=await c(t,s,e)}else{let t=e??n.length;do{let e=0===u?a[0]??r.initialPageParam:L(r,o);if(u>0&&null==e)break;o=await c(o,e),u++}while(u<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=l}}}function L(e,{pages:t,pageParams:s}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}var j=class{#g;#y;#c;#w;#E;#C;#x;#P;constructor(e={}){this.#g=e.queryCache||new D,this.#y=e.mutationCache||new q,this.#c=e.defaultOptions||{},this.#w=new Map,this.#E=new Map,this.#C=0}mount(){this.#C++,1===this.#C&&(this.#x=E.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#P=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#C--,0===this.#C&&(this.#x?.(),this.#x=void 0,this.#P?.(),this.#P=void 0)}isFetching(e){return this.#g.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#y.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#g.build(this,t),r=s.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(n(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#g.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let r=this.defaultQueryOptions({queryKey:e}),i=this.#g.get(r.queryHash),n=i?.state.data,a="function"==typeof t?t(n):t;if(void 0!==a)return this.#g.build(this,r).setData(a,{...s,manual:!0})}setQueriesData(e,t,s){return g.batch(()=>this.#g.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state}removeQueries(e){let t=this.#g;g.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#g;return g.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(g.batch(()=>this.#g.findAll(e).map(e=>e.cancel(s)))).then(i).catch(i)}invalidateQueries(e,t={}){return g.batch(()=>(this.#g.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(g.batch(()=>this.#g.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#g.build(this,t);return s.isStaleByTime(n(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=M(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=M(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return C.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#w.set(l(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#w.values()],s={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#E.set(l(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#E.values()],s={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=u(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===m&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}}},3156:(e,t,s)=>{"use strict";s.d(t,{rc:()=>eu,bm:()=>el,VY:()=>eo,Kq:()=>er,bL:()=>en,hE:()=>ea,LM:()=>ei});var r,i=s(2115),n=s(7650),a=s(5185),o=s(6101),u=s(7328),l=s(6081),c=s(3655),h=s(9033),d=s(5155),f="dismissableLayer.update",p=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=i.forwardRef((e,t)=>{var s,n;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:l,onPointerDownOutside:y,onFocusOutside:m,onInteractOutside:g,onDismiss:w,...E}=e,C=i.useContext(p),[x,P]=i.useState(null),T=null!=(n=null==x?void 0:x.ownerDocument)?n:null==(s=globalThis)?void 0:s.document,[,S]=i.useState({}),O=(0,o.s)(t,e=>P(e)),k=Array.from(C.layers),[A]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),D=k.indexOf(A),F=x?k.indexOf(x):-1,q=C.layersWithOutsidePointerEventsDisabled.size>0,R=F>=D,M=function(e){var t;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,h.c)(e),n=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(s.removeEventListener("click",a.current),a.current=t,s.addEventListener("click",a.current,{once:!0})):t()}else s.removeEventListener("click",a.current);n.current=!1},t=window.setTimeout(()=>{s.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),s.removeEventListener("pointerdown",e),s.removeEventListener("click",a.current)}},[s,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,s=[...C.branches].some(e=>e.contains(t));R&&!s&&(null==y||y(e),null==g||g(e),e.defaultPrevented||null==w||w())},T),L=function(e){var t;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,h.c)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return s.addEventListener("focusin",e),()=>s.removeEventListener("focusin",e)},[s,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==m||m(e),null==g||g(e),e.defaultPrevented||null==w||w())},T);return!function(e,t=globalThis?.document){let s=(0,h.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&s(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[s,t])}(e=>{F===C.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},T),i.useEffect(()=>{if(x)return u&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(x)),C.layers.add(x),v(),()=>{u&&1===C.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[x,T,u,C]),i.useEffect(()=>()=>{x&&(C.layers.delete(x),C.layersWithOutsidePointerEventsDisabled.delete(x),v())},[x,C]),i.useEffect(()=>{let e=()=>S({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,d.jsx)(c.sG.div,{...E,ref:O,style:{pointerEvents:q?R?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});y.displayName="DismissableLayer";var m=i.forwardRef((e,t)=>{let s=i.useContext(p),r=i.useRef(null),n=(0,o.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return s.branches.add(e),()=>{s.branches.delete(e)}},[s.branches]),(0,d.jsx)(c.sG.div,{...e,ref:n})});function v(){let e=new CustomEvent(f);document.dispatchEvent(e)}function b(e,t,s,r){let{discrete:i}=r,n=s.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:s});t&&n.addEventListener(e,t,{once:!0}),i?(0,c.hO)(n,a):n.dispatchEvent(a)}m.displayName="DismissableLayerBranch";var g=s(2712),w=i.forwardRef((e,t)=>{var s,r;let{container:a,...o}=e,[u,l]=i.useState(!1);(0,g.N)(()=>l(!0),[]);let h=a||u&&(null==(r=globalThis)||null==(s=r.document)?void 0:s.body);return h?n.createPortal((0,d.jsx)(c.sG.div,{...o,ref:t}),h):null});w.displayName="Portal";var E=s(8905),C=s(5845),x=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),P=i.forwardRef((e,t)=>(0,d.jsx)(c.sG.span,{...e,ref:t,style:{...x,...e.style}}));P.displayName="VisuallyHidden";var T="ToastProvider",[S,O,k]=(0,u.N)("Toast"),[A,D]=(0,l.A)("Toast",[k]),[F,q]=A(T),R=e=>{let{__scopeToast:t,label:s="Notification",duration:r=5e3,swipeDirection:n="right",swipeThreshold:a=50,children:o}=e,[u,l]=i.useState(null),[c,h]=i.useState(0),f=i.useRef(!1),p=i.useRef(!1);return s.trim()||console.error("Invalid prop `label` supplied to `".concat(T,"`. Expected non-empty `string`.")),(0,d.jsx)(S.Provider,{scope:t,children:(0,d.jsx)(F,{scope:t,label:s,duration:r,swipeDirection:n,swipeThreshold:a,toastCount:c,viewport:u,onViewportChange:l,onToastAdd:i.useCallback(()=>h(e=>e+1),[]),onToastRemove:i.useCallback(()=>h(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:o})})};R.displayName=T;var M="ToastViewport",L=["F8"],j="toast.viewportPause",N="toast.viewportResume",I=i.forwardRef((e,t)=>{let{__scopeToast:s,hotkey:r=L,label:n="Notifications ({hotkey})",...a}=e,u=q(M,s),l=O(s),h=i.useRef(null),f=i.useRef(null),p=i.useRef(null),y=i.useRef(null),v=(0,o.s)(t,y,u.onViewportChange),b=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=u.toastCount>0;i.useEffect(()=>{let e=e=>{var t;0!==r.length&&r.every(t=>e[t]||e.code===t)&&(null==(t=y.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[r]),i.useEffect(()=>{let e=h.current,t=y.current;if(g&&e&&t){let s=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(j);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},r=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||r()},n=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",s),e.addEventListener("focusout",i),e.addEventListener("pointermove",s),e.addEventListener("pointerleave",n),window.addEventListener("blur",s),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",s),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",s),e.removeEventListener("pointerleave",n),window.removeEventListener("blur",s),window.removeEventListener("focus",r)}}},[g,u.isClosePausedRef]);let w=i.useCallback(e=>{let{tabbingDirection:t}=e,s=l().map(e=>{let s=e.ref.current,r=[s,...function(e){let t=[],s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)t.push(s.currentNode);return t}(s)];return"forwards"===t?r:r.reverse()});return("forwards"===t?s.reverse():s).flat()},[l]);return i.useEffect(()=>{let e=y.current;if(e){let t=t=>{let s=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!s){var r,i,n;let s=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null==(r=f.current)||r.focus();return}let o=w({tabbingDirection:a?"backwards":"forwards"}),u=o.findIndex(e=>e===s);es(o.slice(u+1))?t.preventDefault():a?null==(i=f.current)||i.focus():null==(n=p.current)||n.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[l,w]),(0,d.jsxs)(m,{ref:h,role:"region","aria-label":n.replace("{hotkey}",b),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&(0,d.jsx)(Q,{ref:f,onFocusFromOutsideViewport:()=>{es(w({tabbingDirection:"forwards"}))}}),(0,d.jsx)(S.Slot,{scope:s,children:(0,d.jsx)(c.sG.ol,{tabIndex:-1,...a,ref:v})}),g&&(0,d.jsx)(Q,{ref:p,onFocusFromOutsideViewport:()=>{es(w({tabbingDirection:"backwards"}))}})]})});I.displayName=M;var K="ToastFocusProxy",Q=i.forwardRef((e,t)=>{let{__scopeToast:s,onFocusFromOutsideViewport:r,...i}=e,n=q(K,s);return(0,d.jsx)(P,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let s=e.relatedTarget;(null==(t=n.viewport)?void 0:t.contains(s))||r()}})});Q.displayName=K;var _="Toast",U=i.forwardRef((e,t)=>{let{forceMount:s,open:r,defaultOpen:i,onOpenChange:n,...o}=e,[u,l]=(0,C.i)({prop:r,defaultProp:null==i||i,onChange:n,caller:_});return(0,d.jsx)(E.C,{present:s||u,children:(0,d.jsx)(V,{open:u,...o,ref:t,onClose:()=>l(!1),onPause:(0,h.c)(e.onPause),onResume:(0,h.c)(e.onResume),onSwipeStart:(0,a.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.m)(e.onSwipeMove,e=>{let{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(s,"px"))}),onSwipeCancel:(0,a.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.m)(e.onSwipeEnd,e=>{let{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(s,"px")),l(!1)})})})});U.displayName=_;var[G,H]=A(_,{onClose(){}}),V=i.forwardRef((e,t)=>{let{__scopeToast:s,type:r="foreground",duration:u,open:l,onClose:f,onEscapeKeyDown:p,onPause:m,onResume:v,onSwipeStart:b,onSwipeMove:g,onSwipeCancel:w,onSwipeEnd:E,...C}=e,x=q(_,s),[P,T]=i.useState(null),O=(0,o.s)(t,e=>T(e)),k=i.useRef(null),A=i.useRef(null),D=u||x.duration,F=i.useRef(0),R=i.useRef(D),M=i.useRef(0),{onToastAdd:L,onToastRemove:I}=x,K=(0,h.c)(()=>{var e;(null==P?void 0:P.contains(document.activeElement))&&(null==(e=x.viewport)||e.focus()),f()}),Q=i.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(M.current),F.current=new Date().getTime(),M.current=window.setTimeout(K,e))},[K]);i.useEffect(()=>{let e=x.viewport;if(e){let t=()=>{Q(R.current),null==v||v()},s=()=>{let e=new Date().getTime()-F.current;R.current=R.current-e,window.clearTimeout(M.current),null==m||m()};return e.addEventListener(j,s),e.addEventListener(N,t),()=>{e.removeEventListener(j,s),e.removeEventListener(N,t)}}},[x.viewport,D,m,v,Q]),i.useEffect(()=>{l&&!x.isClosePausedRef.current&&Q(D)},[l,D,x.isClosePausedRef,Q]),i.useEffect(()=>(L(),()=>I()),[L,I]);let U=i.useMemo(()=>P?function e(t){let s=[];return Array.from(t.childNodes).forEach(t=>{var r;if(t.nodeType===t.TEXT_NODE&&t.textContent&&s.push(t.textContent),(r=t).nodeType===r.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!r)if(i){let e=t.dataset.radixToastAnnounceAlt;e&&s.push(e)}else s.push(...e(t))}}),s}(P):null,[P]);return x.viewport?(0,d.jsxs)(d.Fragment,{children:[U&&(0,d.jsx)(z,{__scopeToast:s,role:"status","aria-live":"foreground"===r?"assertive":"polite","aria-atomic":!0,children:U}),(0,d.jsx)(G,{scope:s,onClose:K,children:n.createPortal((0,d.jsx)(S.ItemSlot,{scope:s,children:(0,d.jsx)(y,{asChild:!0,onEscapeKeyDown:(0,a.m)(p,()=>{x.isFocusedToastEscapeKeyDownRef.current||K(),x.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,d.jsx)(c.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":x.swipeDirection,...C,ref:O,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,K()))}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{if(!k.current)return;let t=e.clientX-k.current.x,s=e.clientY-k.current.y,r=!!A.current,i=["left","right"].includes(x.swipeDirection),n=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,a=i?n(0,t):0,o=i?0:n(0,s),u="touch"===e.pointerType?10:2,l={x:a,y:o},c={originalEvent:e,delta:l};r?(A.current=l,ee("toast.swipeMove",g,c,{discrete:!1})):et(l,x.swipeDirection,u)?(A.current=l,ee("toast.swipeStart",b,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(s)>u)&&(k.current=null)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=A.current,s=e.target;if(s.hasPointerCapture(e.pointerId)&&s.releasePointerCapture(e.pointerId),A.current=null,k.current=null,t){let s=e.currentTarget,r={originalEvent:e,delta:t};et(t,x.swipeDirection,x.swipeThreshold)?ee("toast.swipeEnd",E,r,{discrete:!0}):ee("toast.swipeCancel",w,r,{discrete:!0}),s.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),x.viewport)})]}):null}),z=e=>{let{__scopeToast:t,children:s,...r}=e,n=q(_,t),[a,o]=i.useState(!1),[u,l]=i.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,h.c)(e);(0,g.N)(()=>{let e=0,s=0;return e=window.requestAnimationFrame(()=>s=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(s)}},[t])}(()=>o(!0)),i.useEffect(()=>{let e=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,d.jsx)(w,{asChild:!0,children:(0,d.jsx)(P,{...r,children:a&&(0,d.jsxs)(d.Fragment,{children:[n.label," ",s]})})})},W=i.forwardRef((e,t)=>{let{__scopeToast:s,...r}=e;return(0,d.jsx)(c.sG.div,{...r,ref:t})});W.displayName="ToastTitle";var B=i.forwardRef((e,t)=>{let{__scopeToast:s,...r}=e;return(0,d.jsx)(c.sG.div,{...r,ref:t})});B.displayName="ToastDescription";var X="ToastAction",Y=i.forwardRef((e,t)=>{let{altText:s,...r}=e;return s.trim()?(0,d.jsx)(Z,{altText:s,asChild:!0,children:(0,d.jsx)($,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(X,"`. Expected non-empty `string`.")),null)});Y.displayName=X;var J="ToastClose",$=i.forwardRef((e,t)=>{let{__scopeToast:s,...r}=e,i=H(J,s);return(0,d.jsx)(Z,{asChild:!0,children:(0,d.jsx)(c.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,i.onClose)})})});$.displayName=J;var Z=i.forwardRef((e,t)=>{let{__scopeToast:s,altText:r,...i}=e;return(0,d.jsx)(c.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...i,ref:t})});function ee(e,t,s,r){let{discrete:i}=r,n=s.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:s});t&&n.addEventListener(e,t,{once:!0}),i?(0,c.hO)(n,a):n.dispatchEvent(a)}var et=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),i=Math.abs(e.y),n=r>i;return"left"===t||"right"===t?n&&r>s:!n&&i>s};function es(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var er=R,ei=I,en=U,ea=W,eo=B,eu=Y,el=$},3509:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},6707:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6715:(e,t,s)=>{"use strict";s.d(t,{Ht:()=>a});var r=s(2115),i=s(5155),n=r.createContext(void 0),a=e=>{let{client:t,children:s}=e;return r.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(n.Provider,{value:t,children:s})}},7108:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7809:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}}]);