"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { ArrowRight, Play, Star, Users, ShoppingBag, Zap } from "lucide-react"

const heroSlides = [
  {
    id: 1,
    title: "Revolutionary AI-Powered E-commerce",
    subtitle: "Experience the future of online shopping",
    description: "Discover cutting-edge products with AI-driven recommendations, seamless shopping experience, and world-class customer service.",
    image: "/hero-bg-1.jpg",
    cta: "Shop Now",
    ctaLink: "/shop",
    badge: "New Launch",
    stats: [
      { icon: Users, value: "50K+", label: "Happy Customers" },
      { icon: ShoppingBag, value: "100K+", label: "Products Sold" },
      { icon: Star, value: "4.9", label: "Average Rating" },
    ]
  },
  {
    id: 2,
    title: "Premium Services & Solutions",
    subtitle: "Professional services tailored for you",
    description: "From consultation to implementation, our expert team delivers comprehensive solutions that drive your business forward.",
    image: "/hero-bg-2.jpg",
    cta: "Explore Services",
    ctaLink: "/services",
    badge: "Professional",
    stats: [
      { icon: Zap, value: "99%", label: "Success Rate" },
      { icon: Users, value: "500+", label: "Projects Completed" },
      { icon: Star, value: "5.0", label: "Client Satisfaction" },
    ]
  },
  {
    id: 3,
    title: "Industrial Production Lines",
    subtitle: "State-of-the-art manufacturing solutions",
    description: "Explore our advanced production line equipment designed for efficiency, reliability, and scalability in modern manufacturing.",
    image: "/hero-bg-3.jpg",
    cta: "View Equipment",
    ctaLink: "/production-lines",
    badge: "Industrial",
    stats: [
      { icon: Zap, value: "24/7", label: "Support" },
      { icon: Users, value: "200+", label: "Installations" },
      { icon: Star, value: "15+", label: "Years Experience" },
    ]
  }
]

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const { t } = useLanguage()

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 6000)

    return () => clearInterval(timer)
  }, [])

  const currentHero = heroSlides[currentSlide]

  return (
    <section className="relative h-[700px] lg:h-[700px] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-pattern"></div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 dark:bg-primary-800 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200 dark:bg-secondary-800 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-accent-200 dark:bg-accent-800 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8 text-center lg:text-left">
            {/* Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
                <Zap className="h-4 w-4 mr-2" />
                {currentHero.badge}
              </Badge>
            </div>

            {/* Title */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                  {currentHero.title}
                </span>
              </h1>
              <h2 className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-medium">
                {currentHero.subtitle}
              </h2>
            </div>

            {/* Description */}
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
              {currentHero.description}
            </p>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 py-8">
              {currentHero.stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <stat.icon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href={currentHero.ctaLink}>
                <Button size="lg" className="group px-8 py-4 text-lg font-semibold">
                  {currentHero.cta}
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              
              <Button variant="outline" size="lg" className="group px-8 py-4 text-lg font-semibold">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="relative z-10 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
              <div className="aspect-square bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900 dark:to-secondary-900 rounded-xl flex items-center justify-center">
                <div className="text-6xl">
                  {currentSlide === 0 && "🛒"}
                  {currentSlide === 1 && "⚙️"}
                  {currentSlide === 2 && "🏭"}
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-primary-500 text-white rounded-full p-3 shadow-lg animate-bounce">
                <Star className="h-6 w-6 fill-current" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-secondary-500 text-white rounded-full p-3 shadow-lg animate-pulse">
                <Zap className="h-6 w-6" />
              </div>
            </div>

            {/* Background Decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary-200 to-secondary-200 dark:from-primary-800 dark:to-secondary-800 rounded-2xl transform -rotate-6 scale-105 opacity-20"></div>
          </div>
        </div>

        {/* Slide Indicators */}
        <div className="flex justify-center mt-12 space-x-2">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? "bg-primary-600 w-8"
                  : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
              }`}
            />
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}
