"use client"

import { useState } from "react"
import Link from "next/link"
import { AdminLayout } from "@/components/layout/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Wrench,
  Code,
  Palette,
  BarChart3,
  Shield,
  Star,
  Clock,
  Users,
  MessageCircle,
  CheckCircle,
  AlertCircle,
  XCircle,
  Download,
  Upload,
  Calendar,
  DollarSign,
} from "lucide-react"

// Mock services data for admin
const mockServices = [
  {
    id: "1",
    title: "Web Development",
    slug: "web-development",
    description: "Custom web applications built with modern technologies and best practices.",
    shortDescription: "Modern web applications with cutting-edge technology",
    category: "Development",
    status: "active",
    featured: true,
    popular: true,
    price: 2999,
    duration: "4-8 weeks",
    rating: 4.9,
    reviewCount: 45,
    inquiries: 23,
    conversions: 12,
    revenue: 35988,
    tags: ["React", "Next.js", "TypeScript", "Node.js"],
    createdAt: new Date("2023-08-15"),
    updatedAt: new Date("2024-01-20"),
    author: {
      name: "Tech Team",
      email: "<EMAIL>"
    }
  },
  {
    id: "2",
    title: "Mobile App Development",
    slug: "mobile-app-development",
    description: "Native and cross-platform mobile applications for iOS and Android.",
    shortDescription: "Native and cross-platform mobile solutions",
    category: "Development",
    status: "active",
    featured: true,
    popular: false,
    price: 4999,
    duration: "6-12 weeks",
    rating: 4.8,
    reviewCount: 32,
    inquiries: 18,
    conversions: 8,
    revenue: 39992,
    tags: ["React Native", "Flutter", "iOS", "Android"],
    createdAt: new Date("2023-09-10"),
    updatedAt: new Date("2024-01-18"),
    author: {
      name: "Mobile Team",
      email: "<EMAIL>"
    }
  },
  {
    id: "3",
    title: "UI/UX Design",
    slug: "ui-ux-design",
    description: "User-centered design solutions that enhance user experience and drive engagement.",
    shortDescription: "User-centered design for better engagement",
    category: "Design",
    status: "active",
    featured: false,
    popular: true,
    price: 1999,
    duration: "2-4 weeks",
    rating: 4.9,
    reviewCount: 67,
    inquiries: 34,
    conversions: 19,
    revenue: 37981,
    tags: ["Figma", "Adobe XD", "Prototyping", "User Research"],
    createdAt: new Date("2023-07-20"),
    updatedAt: new Date("2024-01-15"),
    author: {
      name: "Design Team",
      email: "<EMAIL>"
    }
  },
  {
    id: "4",
    title: "Digital Marketing",
    slug: "digital-marketing",
    description: "Comprehensive digital marketing strategies to grow your online presence.",
    shortDescription: "Comprehensive strategies for online growth",
    category: "Marketing",
    status: "draft",
    featured: false,
    popular: false,
    price: 1499,
    duration: "Ongoing",
    rating: 4.7,
    reviewCount: 89,
    inquiries: 15,
    conversions: 7,
    revenue: 10493,
    tags: ["SEO", "SEM", "Social Media", "Content Marketing"],
    createdAt: new Date("2023-10-05"),
    updatedAt: new Date("2024-01-22"),
    author: {
      name: "Marketing Team",
      email: "<EMAIL>"
    }
  },
  {
    id: "5",
    title: "Cloud Solutions",
    slug: "cloud-solutions",
    description: "Scalable cloud infrastructure and migration services for modern businesses.",
    shortDescription: "Scalable cloud infrastructure solutions",
    category: "Infrastructure",
    status: "paused",
    featured: true,
    popular: false,
    price: 3499,
    duration: "3-6 weeks",
    rating: 4.8,
    reviewCount: 23,
    inquiries: 12,
    conversions: 5,
    revenue: 17495,
    tags: ["AWS", "Azure", "Docker", "Kubernetes"],
    createdAt: new Date("2023-11-12"),
    updatedAt: new Date("2024-01-10"),
    author: {
      name: "Cloud Team",
      email: "<EMAIL>"
    }
  }
]

const categories = ["All Categories", "Development", "Design", "Marketing", "Infrastructure", "Analytics"]
const statuses = ["All Status", "active", "draft", "paused", "archived"]

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    case "draft":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    case "paused":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
    case "archived":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return CheckCircle
    case "draft":
      return AlertCircle
    case "paused":
      return Clock
    case "archived":
      return XCircle
    default:
      return AlertCircle
  }
}

const getCategoryIcon = (category: string) => {
  switch (category) {
    case "Development":
      return Code
    case "Design":
      return Palette
    case "Marketing":
      return BarChart3
    case "Infrastructure":
      return Shield
    default:
      return Wrench
  }
}

export default function ServicesManagementPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedStatus, setSelectedStatus] = useState("All Status")
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  
  const { t } = useLanguage()
  const { toast } = useToast()

  const filteredServices = mockServices.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         service.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === "All Categories" || service.category === selectedCategory
    const matchesStatus = selectedStatus === "All Status" || service.status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const handleSelectService = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    )
  }

  const handleSelectAll = () => {
    setSelectedServices(
      selectedServices.length === filteredServices.length 
        ? [] 
        : filteredServices.map(s => s.id)
    )
  }

  const handleBulkAction = (action: string) => {
    toast({
      title: "Bulk Action",
      description: `${action} applied to ${selectedServices.length} services`,
    })
    setSelectedServices([])
  }

  const handleDeleteService = (serviceId: string) => {
    toast({
      title: "Service Deleted",
      description: "Service has been successfully deleted",
    })
  }

  const handleUpdateStatus = (serviceId: string, newStatus: string) => {
    toast({
      title: "Status Updated",
      description: `Service status updated to ${newStatus}`,
    })
  }

  const ServiceCard = ({ service }: { service: typeof mockServices[0] }) => {
    const StatusIcon = getStatusIcon(service.status)
    const CategoryIcon = getCategoryIcon(service.category)

    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              <input
                type="checkbox"
                checked={selectedServices.includes(service.id)}
                onChange={() => handleSelectService(service.id)}
                className="mt-1"
              />
              
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                <CategoryIcon className="h-8 w-8 text-gray-400" />
              </div>
              
              <div className="flex-1 min-w-0">
                {/* Service Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-lg line-clamp-1">
                        {service.title}
                      </h3>
                      {service.featured && (
                        <Badge variant="secondary" className="text-xs">Featured</Badge>
                      )}
                      {service.popular && (
                        <Badge className="bg-orange-500 text-white text-xs">Popular</Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                      {service.shortDescription}
                    </p>
                  </div>
                  
                  <div className="text-right ml-4">
                    <Badge className={`flex items-center space-x-1 ${getStatusColor(service.status)}`}>
                      <StatusIcon className="h-3 w-3" />
                      <span className="capitalize">{service.status}</span>
                    </Badge>
                  </div>
                </div>

                {/* Service Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-center">
                    <p className="text-lg font-semibold">{formatPrice(service.price)}</p>
                    <p className="text-xs text-gray-500">Starting Price</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-semibold">{service.inquiries}</p>
                    <p className="text-xs text-gray-500">Inquiries</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-semibold">{service.conversions}</p>
                    <p className="text-xs text-gray-500">Conversions</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-semibold">{formatPrice(service.revenue)}</p>
                    <p className="text-xs text-gray-500">Revenue</p>
                  </div>
                </div>

                {/* Meta Information */}
                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                  <div className="flex items-center space-x-1">
                    <Badge variant="outline" className="text-xs">{service.category}</Badge>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{service.duration}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{service.rating} ({service.reviewCount})</span>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {service.tags.slice(0, 4).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {service.tags.length > 4 && (
                    <Badge variant="outline" className="text-xs">
                      +{service.tags.length - 4}
                    </Badge>
                  )}
                </div>

                {/* Author and Dates */}
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{service.author.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Updated {formatDate(service.updatedAt)}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Link href={`/services/${service.slug}`} target="_blank">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                    </Link>
                    <Link href={`/admin/services/${service.id}/edit`}>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </Link>
                    <Link href={`/admin/services/${service.id}/inquiries`}>
                      <Button variant="ghost" size="sm">
                        <MessageCircle className="h-4 w-4 mr-1" />
                        Inquiries
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeleteService(service.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <select
                      value={service.status}
                      onChange={(e) => handleUpdateStatus(service.id, e.target.value)}
                      className="px-2 py-1 text-sm border rounded"
                    >
                      <option value="active">Active</option>
                      <option value="draft">Draft</option>
                      <option value="paused">Paused</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Services Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage services, categories, and inquiries
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Link href="/admin/services/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Service
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Services
                  </p>
                  <p className="text-2xl font-bold">
                    {mockServices.length}
                  </p>
                </div>
                <Wrench className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active Services
                  </p>
                  <p className="text-2xl font-bold">
                    {mockServices.filter(s => s.status === 'active').length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Inquiries
                  </p>
                  <p className="text-2xl font-bold">
                    {mockServices.reduce((sum, service) => sum + service.inquiries, 0)}
                  </p>
                </div>
                <MessageCircle className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Revenue
                  </p>
                  <p className="text-2xl font-bold">
                    {formatPrice(mockServices.reduce((sum, service) => sum + service.revenue, 0))}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search services..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
                >
                  {statuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedServices.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">
                    {selectedServices.length} services selected
                  </span>
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {selectedServices.length === filteredServices.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Activate')}>
                    Activate
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Pause')}>
                    Pause
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Archive')}>
                    Archive
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('Delete')} className="text-red-600">
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Services List */}
        <div className="space-y-4">
          {filteredServices.map((service) => (
            <ServiceCard key={service.id} service={service} />
          ))}
        </div>

        {/* Empty State */}
        {filteredServices.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Wrench className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No services found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search or filter criteria
              </p>
              <Button onClick={() => {
                setSearchQuery("")
                setSelectedCategory("All Categories")
                setSelectedStatus("All Status")
              }}>
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {filteredServices.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredServices.length} of {mockServices.length} services
            </p>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" className="bg-primary-50 dark:bg-primary-900">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
