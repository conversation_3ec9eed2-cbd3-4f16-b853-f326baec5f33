(()=>{var a={};a.id=949,a.ids=[949],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2911:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(27605),g=c(63442),h=c(37566),i=c(32186),j=c(29523),k=c(89667),l=c(80013),m=c(44493),n=c(29867),o=c(98436),p=c(96474),q=c(62688);let r=(0,q.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),s=(0,q.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var t=c(64398),u=c(63143),v=c(88233);let w=(0,q.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var x=c(97992);let y=h.Ik({type:h.k5(["shipping","billing"]),firstName:h.Yj().min(2,"First name must be at least 2 characters"),lastName:h.Yj().min(2,"Last name must be at least 2 characters"),company:h.Yj().optional(),address1:h.Yj().min(5,"Address must be at least 5 characters"),address2:h.Yj().optional(),city:h.Yj().min(2,"City must be at least 2 characters"),state:h.Yj().min(2,"State must be at least 2 characters"),postalCode:h.Yj().min(5,"Postal code must be at least 5 characters"),country:h.Yj().min(2,"Country must be at least 2 characters"),phone:h.Yj().optional(),isDefault:h.zM().default(!1)}),z=[{id:"1",type:"shipping",firstName:"John",lastName:"Doe",company:"",address1:"123 Main Street",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"United States",phone:"+****************",isDefault:!0},{id:"2",type:"billing",firstName:"John",lastName:"Doe",company:"Acme Corp",address1:"456 Business Ave",address2:"Suite 200",city:"New York",state:"NY",postalCode:"10002",country:"United States",phone:"+****************",isDefault:!1}];function A(){let[a,b]=(0,e.useState)(z),[c,h]=(0,e.useState)(!1),[q,A]=(0,e.useState)(null),[B,C]=(0,e.useState)(!1),{toast:D}=(0,n.dj)(),{t:E}=(0,o.o)(),{register:F,handleSubmit:G,formState:{errors:H},reset:I,setValue:J}=(0,f.mN)({resolver:(0,g.u)(y)}),K=async a=>{C(!0);try{if(await new Promise(a=>setTimeout(a,1e3)),q)b(b=>b.map(b=>b.id===q?{...b,...a}:b)),D({title:E("common.success"),description:"Address updated successfully"}),A(null);else{let c={id:Date.now().toString(),...a};b(a=>[...a,c]),D({title:E("common.success"),description:"Address added successfully"}),h(!1)}I()}catch(a){D({title:E("common.error"),description:"Failed to save address",variant:"destructive"})}finally{C(!1)}},L=async a=>{if(confirm("Are you sure you want to delete this address?")){C(!0);try{await new Promise(a=>setTimeout(a,500)),b(b=>b.filter(b=>b.id!==a)),D({title:E("common.success"),description:"Address deleted successfully"})}catch(a){D({title:E("common.error"),description:"Failed to delete address",variant:"destructive"})}finally{C(!1)}}},M=async a=>{C(!0);try{await new Promise(a=>setTimeout(a,500)),b(b=>b.map(b=>({...b,isDefault:b.id===a}))),D({title:E("common.success"),description:"Default address updated"})}catch(a){D({title:E("common.error"),description:"Failed to update default address",variant:"destructive"})}finally{C(!1)}};return(0,d.jsx)(i.O,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Addresses"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage your shipping and billing addresses"})]}),!c&&(0,d.jsxs)(j.$,{onClick:()=>h(!0),children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Address"]})]}),c&&(0,d.jsxs)(m.Zp,{className:"mb-6",children:[(0,d.jsxs)(m.aR,{children:[(0,d.jsx)(m.ZB,{children:q?"Edit Address":"Add New Address"}),(0,d.jsx)(m.BT,{children:q?"Update your address information":"Add a new shipping or billing address"})]}),(0,d.jsx)(m.Wu,{children:(0,d.jsxs)("form",{onSubmit:G(K),className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"Address Type"}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"radio",value:"shipping",...F("type"),className:"text-primary-600"}),(0,d.jsx)(r,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Shipping"})]}),(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"radio",value:"billing",...F("type"),className:"text-primary-600"}),(0,d.jsx)(s,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Billing"})]})]}),H.type&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.type.message})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"firstName",children:"First Name"}),(0,d.jsx)(k.p,{id:"firstName",...F("firstName"),placeholder:"Enter first name"}),H.firstName&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.firstName.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"lastName",children:"Last Name"}),(0,d.jsx)(k.p,{id:"lastName",...F("lastName"),placeholder:"Enter last name"}),H.lastName&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.lastName.message})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"company",children:"Company (Optional)"}),(0,d.jsx)(k.p,{id:"company",...F("company"),placeholder:"Enter company name"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"address1",children:"Address Line 1"}),(0,d.jsx)(k.p,{id:"address1",...F("address1"),placeholder:"Enter street address"}),H.address1&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.address1.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),(0,d.jsx)(k.p,{id:"address2",...F("address2"),placeholder:"Apartment, suite, etc."})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"city",children:"City"}),(0,d.jsx)(k.p,{id:"city",...F("city"),placeholder:"Enter city"}),H.city&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.city.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"state",children:"State/Province"}),(0,d.jsx)(k.p,{id:"state",...F("state"),placeholder:"Enter state"}),H.state&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.state.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"postalCode",children:"Postal Code"}),(0,d.jsx)(k.p,{id:"postalCode",...F("postalCode"),placeholder:"Enter postal code"}),H.postalCode&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.postalCode.message})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"country",children:"Country"}),(0,d.jsx)(k.p,{id:"country",...F("country"),placeholder:"Enter country"}),H.country&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:H.country.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"phone",children:"Phone (Optional)"}),(0,d.jsx)(k.p,{id:"phone",type:"tel",...F("phone"),placeholder:"Enter phone number"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"isDefault",...F("isDefault"),className:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"}),(0,d.jsx)(l.J,{htmlFor:"isDefault",children:"Set as default address"})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,d.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>{h(!1),A(null),I()},disabled:B,children:"Cancel"}),(0,d.jsx)(j.$,{type:"submit",disabled:B,children:B?"Saving...":q?"Update Address":"Add Address"})]})]})})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.map(a=>(0,d.jsxs)(m.Zp,{className:"relative",children:[a.isDefault&&(0,d.jsx)("div",{className:"absolute top-4 right-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-full text-xs font-medium",children:[(0,d.jsx)(t.A,{className:"h-3 w-3 fill-current"}),(0,d.jsx)("span",{children:"Default"})]})}),(0,d.jsx)(m.aR,{children:(0,d.jsxs)(m.ZB,{className:"flex items-center text-lg",children:["shipping"===a.type?(0,d.jsx)(r,{className:"h-5 w-5 mr-2"}):(0,d.jsx)(s,{className:"h-5 w-5 mr-2"}),"shipping"===a.type?"Shipping":"Billing"," Address"]})}),(0,d.jsxs)(m.Wu,{children:[(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("p",{className:"font-medium",children:[a.firstName," ",a.lastName]}),a.company&&(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.company}),(0,d.jsx)("p",{children:a.address1}),a.address2&&(0,d.jsx)("p",{children:a.address2}),(0,d.jsxs)("p",{children:[a.city,", ",a.state," ",a.postalCode]}),(0,d.jsx)("p",{children:a.country}),a.phone&&(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.phone})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-4 border-t",children:[(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(j.$,{size:"sm",variant:"outline",onClick:()=>{A(a.id),h(!0),Object.entries(a).forEach(([a,b])=>{"id"!==a&&J(a,b)})},children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,d.jsxs)(j.$,{size:"sm",variant:"outline",onClick:()=>L(a.id),className:"text-red-600 hover:text-red-700",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]}),!a.isDefault&&(0,d.jsxs)(j.$,{size:"sm",variant:"ghost",onClick:()=>M(a.id),className:"text-primary-600 hover:text-primary-700",children:[(0,d.jsx)(w,{className:"h-4 w-4 mr-1"}),"Set Default"]})]})]})]},a.id))}),0===a.length&&!c&&(0,d.jsx)(m.Zp,{children:(0,d.jsxs)(m.Wu,{className:"text-center py-12",children:[(0,d.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No addresses yet"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Add your first address to get started with orders"}),(0,d.jsxs)(j.$,{onClick:()=>h(!0),children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Your First Address"]})]})})]})})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32186:(a,b,c)=>{"use strict";c.d(b,{O:()=>t});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(4780),i=c(98436),j=c(58869),k=c(97992),l=c(19080),m=c(67760),n=c(97051),o=c(85778),p=c(99891),q=c(84027);let r=(0,c(62688).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var s=c(40083);function t({children:a}){let b=(0,g.usePathname)(),{t:c}=(0,i.o)(),e=[{name:"Profile",href:"/account/profile",icon:j.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:k.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:l.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:m.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:n.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:o.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:p.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:q.A,description:"Account preferences and settings"}];return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,d.jsx)(j.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,d.jsxs)("nav",{className:"p-2",children:[(0,d.jsx)("div",{className:"space-y-1",children:e.map(a=>{let c=b===a.href;return(0,d.jsxs)(f(),{href:a.href,className:(0,h.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",c?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,d.jsx)(a.icon,{className:(0,h.cn)("mr-3 h-5 w-5",c?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{children:a.name}),(0,d.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:a.description})]})]},a.href)})}),(0,d.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"space-y-1",children:[[{name:"Help Center",href:"/help",icon:r},{name:"Contact Support",href:"/contact",icon:r}].map(a=>(0,d.jsxs)(f(),{href:a.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,d.jsx)(a.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),a.name]},a.href)),(0,d.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,d.jsx)(s.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:a})})]})})})}},33873:a=>{"use strict";a.exports=require("path")},36283:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["account",{children:["addresses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,39955)),"E:\\aidevcommerce\\src\\app\\account\\addresses\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,15515)),"E:\\aidevcommerce\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\aidevcommerce\\src\\app\\account\\addresses\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/account/addresses/page",pathname:"/account/addresses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/account/addresses/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},39955:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\aidevcommerce\\\\src\\\\app\\\\account\\\\addresses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\aidevcommerce\\src\\app\\account\\addresses\\page.tsx","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42618:(a,b,c)=>{Promise.resolve().then(c.bind(c,39955))},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},80013:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(4780);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},82786:(a,b,c)=>{Promise.resolve().then(c.bind(c,2911))},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},97051:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,54,732,776],()=>b(b.s=36283));module.exports=c})();