import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Security configuration
const SECURITY_CONFIG = {
  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // requests per window
    apiMaxRequests: 50, // API requests per window
    authMaxRequests: 5, // Auth requests per window
  },
  
  // Blocked IPs (in production, use a proper IP blocking service)
  blockedIPs: new Set([
    // Add blocked IPs here
  ]),
  
  // Suspicious patterns
  suspiciousPatterns: [
    /\b(union|select|insert|delete|drop|create|alter|exec|script)\b/i,
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
  ],
  
  // Protected routes
  protectedRoutes: [
    '/admin',
    '/account',
    '/api/admin',
    '/api/user',
  ],
  
  // Admin routes
  adminRoutes: [
    '/admin',
    '/api/admin',
  ],
}

// Rate limiting function
function rateLimit(ip: string, maxRequests: number, windowMs: number): boolean {
  const now = Date.now()
  const key = `${ip}:${Math.floor(now / windowMs)}`
  
  const current = rateLimitStore.get(key)
  if (!current) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (current.count >= maxRequests) {
    return false
  }
  
  current.count++
  return true
}

// Clean up old rate limit entries
function cleanupRateLimit() {
  const now = Date.now()
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}

// Security headers
function addSecurityHeaders(response: NextResponse) {
  // Security headers
  response.headers.set('X-DNS-Prefetch-Control', 'on')
  response.headers.set('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=()')
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data:",
    "connect-src 'self' https: wss:",
    "media-src 'self' https:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; ')
  
  response.headers.set('Content-Security-Policy', csp)
  
  return response
}

// Input validation
function validateInput(request: NextRequest): boolean {
  const url = request.url
  const userAgent = request.headers.get('user-agent') || ''
  
  // Check for suspicious patterns in URL
  for (const pattern of SECURITY_CONFIG.suspiciousPatterns) {
    if (pattern.test(url) || pattern.test(userAgent)) {
      return false
    }
  }
  
  return true
}

// Check if route is protected
function isProtectedRoute(pathname: string): boolean {
  return SECURITY_CONFIG.protectedRoutes.some(route => 
    pathname.startsWith(route)
  )
}

// Check if route is admin route
function isAdminRoute(pathname: string): boolean {
  return SECURITY_CONFIG.adminRoutes.some(route => 
    pathname.startsWith(route)
  )
}

// Get client IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return request.ip || 'unknown'
}

// Main middleware function
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const clientIP = getClientIP(request)
  
  // Clean up rate limit store periodically
  if (Math.random() < 0.01) { // 1% chance
    cleanupRateLimit()
  }
  
  // Block suspicious IPs
  if (SECURITY_CONFIG.blockedIPs.has(clientIP)) {
    return new NextResponse('Access Denied', { status: 403 })
  }
  
  // Validate input
  if (!validateInput(request)) {
    console.warn(`Suspicious request detected from ${clientIP}: ${request.url}`)
    return new NextResponse('Bad Request', { status: 400 })
  }
  
  // Rate limiting
  let maxRequests = SECURITY_CONFIG.rateLimit.maxRequests
  
  // Different limits for different routes
  if (pathname.startsWith('/api/auth')) {
    maxRequests = SECURITY_CONFIG.rateLimit.authMaxRequests
  } else if (pathname.startsWith('/api')) {
    maxRequests = SECURITY_CONFIG.rateLimit.apiMaxRequests
  }
  
  if (!rateLimit(clientIP, maxRequests, SECURITY_CONFIG.rateLimit.windowMs)) {
    console.warn(`Rate limit exceeded for ${clientIP}`)
    return new NextResponse('Too Many Requests', { 
      status: 429,
      headers: {
        'Retry-After': '900' // 15 minutes
      }
    })
  }
  
  // Authentication check for protected routes
  if (isProtectedRoute(pathname)) {
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET 
    })
    
    if (!token) {
      // Redirect to login for protected routes
      const loginUrl = new URL('/auth/login', request.url)
      loginUrl.searchParams.set('callbackUrl', request.url)
      return NextResponse.redirect(loginUrl)
    }
    
    // Admin route check
    if (isAdminRoute(pathname)) {
      const userRole = token.role as string
      if (userRole !== 'admin' && userRole !== 'moderator') {
        return new NextResponse('Forbidden', { status: 403 })
      }
    }
  }
  
  // Create response
  const response = NextResponse.next()
  
  // Add security headers
  addSecurityHeaders(response)
  
  // Add performance headers for static assets
  if (pathname.startsWith('/_next/static/') || 
      pathname.startsWith('/images/') ||
      pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/)) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
  }
  
  // Add CORS headers for API routes
  if (pathname.startsWith('/api/')) {
    response.headers.set('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    response.headers.set('Access-Control-Max-Age', '86400')
  }
  
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, { status: 200 })
  }
  
  return response
}

// Configure which routes the middleware runs on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
