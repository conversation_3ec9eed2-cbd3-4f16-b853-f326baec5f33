// Email Service for notifications, marketing campaigns, and transactional emails
export interface EmailTemplate {
  id: string
  name: string
  subject: string
  htmlContent: string
  textContent: string
  variables: string[]
  category: 'transactional' | 'marketing' | 'notification'
  status: 'active' | 'draft' | 'archived'
  createdAt: Date
  updatedAt: Date
}

export interface EmailCampaign {
  id: string
  name: string
  subject: string
  templateId: string
  recipients: string[]
  segmentId?: string
  scheduledAt?: Date
  sentAt?: Date
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused'
  stats: {
    sent: number
    delivered: number
    opened: number
    clicked: number
    bounced: number
    unsubscribed: number
  }
  createdAt: Date
  updatedAt: Date
}

export interface EmailNotification {
  id: string
  type: 'order_confirmation' | 'shipping_update' | 'password_reset' | 'welcome' | 'newsletter'
  recipient: string
  subject: string
  content: string
  status: 'pending' | 'sent' | 'failed'
  sentAt?: Date
  metadata?: unknown
}

class EmailService {
  private apiKey: string
  private baseUrl: string

  constructor() {
    this.apiKey = process.env.EMAIL_API_KEY || 'demo-key'
    this.baseUrl = process.env.EMAIL_API_URL || 'https://api.emailservice.com'
  }

  // Email Templates
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      // Mock email templates
      const templates: EmailTemplate[] = [
        {
          id: '1',
          name: 'Order Confirmation',
          subject: 'Order Confirmation - #{orderNumber}',
          htmlContent: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Thank you for your order!</h1>
              <p>Hi {{customerName}},</p>
              <p>We've received your order and are preparing it for shipment.</p>
              <div style="background: #f5f5f5; padding: 20px; margin: 20px 0;">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{orderNumber}}</p>
                <p><strong>Total:</strong> {{orderTotal}}</p>
                <p><strong>Estimated Delivery:</strong> {{deliveryDate}}</p>
              </div>
              <p>You can track your order <a href="{{trackingUrl}}">here</a>.</p>
              <p>Thank you for shopping with us!</p>
            </div>
          `,
          textContent: 'Thank you for your order! Order #{{orderNumber}} - Total: {{orderTotal}}',
          variables: ['customerName', 'orderNumber', 'orderTotal', 'deliveryDate', 'trackingUrl'],
          category: 'transactional',
          status: 'active',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-15')
        },
        {
          id: '2',
          name: 'Welcome Email',
          subject: 'Welcome to AIDEVCOMMERCE!',
          htmlContent: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #6366f1;">Welcome to AIDEVCOMMERCE!</h1>
              <p>Hi {{firstName}},</p>
              <p>Welcome to our community! We're excited to have you on board.</p>
              <div style="background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; margin: 20px 0; border-radius: 8px;">
                <h3>Get Started</h3>
                <p>Explore our products and discover amazing deals!</p>
                <a href="{{shopUrl}}" style="background: white; color: #6366f1; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;">Start Shopping</a>
              </div>
              <p>If you have any questions, feel free to contact our support team.</p>
            </div>
          `,
          textContent: 'Welcome to AIDEVCOMMERCE! Start exploring our products at {{shopUrl}}',
          variables: ['firstName', 'shopUrl'],
          category: 'marketing',
          status: 'active',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-10')
        },
        {
          id: '3',
          name: 'Password Reset',
          subject: 'Reset Your Password',
          htmlContent: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Reset Your Password</h1>
              <p>Hi {{firstName}},</p>
              <p>You requested to reset your password. Click the button below to create a new password:</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{resetUrl}}" style="background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
              </div>
              <p>This link will expire in 24 hours. If you didn't request this, please ignore this email.</p>
              <p>For security reasons, please don't share this link with anyone.</p>
            </div>
          `,
          textContent: 'Reset your password: {{resetUrl}}',
          variables: ['firstName', 'resetUrl'],
          category: 'transactional',
          status: 'active',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-05')
        },
        {
          id: '4',
          name: 'Newsletter Template',
          subject: 'Weekly Newsletter - {{weekDate}}',
          htmlContent: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <header style="background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; text-align: center;">
                <h1>AIDEVCOMMERCE Newsletter</h1>
                <p>{{weekDate}}</p>
              </header>
              <div style="padding: 20px;">
                <h2>This Week's Highlights</h2>
                <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-left: 4px solid #6366f1;">
                  <h3>{{highlightTitle}}</h3>
                  <p>{{highlightContent}}</p>
                </div>
                <h2>Featured Products</h2>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0;">
                  {{#each featuredProducts}}
                  <div style="border: 1px solid #e2e8f0; padding: 15px; border-radius: 8px;">
                    <h4>{{name}}</h4>
                    <p style="color: #6366f1; font-weight: bold;">{{price}}</p>
                  </div>
                  {{/each}}
                </div>
              </div>
            </div>
          `,
          textContent: 'AIDEVCOMMERCE Newsletter - {{weekDate}}',
          variables: ['weekDate', 'highlightTitle', 'highlightContent', 'featuredProducts'],
          category: 'marketing',
          status: 'active',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-20')
        }
      ]

      return templates
    } catch (error) {
      console.error('Failed to fetch email templates:', error)
      return []
    }
  }

  async createEmailTemplate(template: Omit<EmailTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<EmailTemplate> {
    try {
      const newTemplate: EmailTemplate = {
        ...template,
        id: `template-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // In a real implementation, this would save to database
      console.log('Creating email template:', newTemplate)
      
      return newTemplate
    } catch (error) {
      console.error('Failed to create email template:', error)
      throw error
    }
  }

  // Email Campaigns
  async getEmailCampaigns(): Promise<EmailCampaign[]> {
    try {
      const campaigns: EmailCampaign[] = [
        {
          id: '1',
          name: 'Welcome Series - Week 1',
          subject: 'Welcome to AIDEVCOMMERCE!',
          templateId: '2',
          recipients: ['<EMAIL>', '<EMAIL>'],
          status: 'sent',
          stats: {
            sent: 1250,
            delivered: 1235,
            opened: 892,
            clicked: 234,
            bounced: 15,
            unsubscribed: 8
          },
          sentAt: new Date('2024-01-15T10:00:00'),
          createdAt: new Date('2024-01-14'),
          updatedAt: new Date('2024-01-15')
        },
        {
          id: '2',
          name: 'Product Launch - Smart Watch',
          subject: 'Introducing Our New Smart Watch!',
          templateId: '4',
          recipients: [],
          segmentId: 'electronics-interested',
          scheduledAt: new Date('2024-01-25T09:00:00'),
          status: 'scheduled',
          stats: {
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            bounced: 0,
            unsubscribed: 0
          },
          createdAt: new Date('2024-01-20'),
          updatedAt: new Date('2024-01-22')
        }
      ]

      return campaigns
    } catch (error) {
      console.error('Failed to fetch email campaigns:', error)
      return []
    }
  }

  async createEmailCampaign(campaign: Omit<EmailCampaign, 'id' | 'stats' | 'createdAt' | 'updatedAt'>): Promise<EmailCampaign> {
    try {
      const newCampaign: EmailCampaign = {
        ...campaign,
        id: `campaign-${Date.now()}`,
        stats: {
          sent: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
          bounced: 0,
          unsubscribed: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      console.log('Creating email campaign:', newCampaign)
      
      return newCampaign
    } catch (error) {
      console.error('Failed to create email campaign:', error)
      throw error
    }
  }

  // Send Transactional Emails
  async sendTransactionalEmail(
    templateId: string,
    recipient: string,
    variables: Record<string, unknown>,
    options?: {
      subject?: string
      replyTo?: string
      attachments?: Array<{ filename: string; content: string; type: string }>
    }
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Simulate email sending
      console.log('Sending transactional email:', {
        templateId,
        recipient,
        variables,
        options
      })

      // Mock successful response
      return {
        success: true,
        messageId: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }
    } catch (error) {
      console.error('Failed to send transactional email:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Send Marketing Emails
  async sendMarketingEmail(campaignId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Sending marketing email campaign:', campaignId)
      
      // Mock successful response
      return { success: true }
    } catch (error) {
      console.error('Failed to send marketing email:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Email Notifications
  async sendOrderConfirmation(orderData: {
    customerEmail: string
    customerName: string
    orderNumber: string
    orderTotal: string
    deliveryDate: string
  }): Promise<boolean> {
    try {
      const result = await this.sendTransactionalEmail(
        '1', // Order confirmation template
        orderData.customerEmail,
        {
          customerName: orderData.customerName,
          orderNumber: orderData.orderNumber,
          orderTotal: orderData.orderTotal,
          deliveryDate: orderData.deliveryDate,
          trackingUrl: `https://aidevcommerce.com/track/${orderData.orderNumber}`
        }
      )

      return result.success
    } catch (error) {
      console.error('Failed to send order confirmation:', error)
      return false
    }
  }

  async sendWelcomeEmail(userData: {
    email: string
    firstName: string
  }): Promise<boolean> {
    try {
      const result = await this.sendTransactionalEmail(
        '2', // Welcome template
        userData.email,
        {
          firstName: userData.firstName,
          shopUrl: 'https://aidevcommerce.com/shop'
        }
      )

      return result.success
    } catch (error) {
      console.error('Failed to send welcome email:', error)
      return false
    }
  }

  async sendPasswordResetEmail(userData: {
    email: string
    firstName: string
    resetToken: string
  }): Promise<boolean> {
    try {
      const result = await this.sendTransactionalEmail(
        '3', // Password reset template
        userData.email,
        {
          firstName: userData.firstName,
          resetUrl: `https://aidevcommerce.com/auth/reset-password?token=${userData.resetToken}`
        }
      )

      return result.success
    } catch (error) {
      console.error('Failed to send password reset email:', error)
      return false
    }
  }

  // Email Analytics
  async getEmailAnalytics(timeRange: '7d' | '30d' | '90d' = '30d'): Promise<{
    totalSent: number
    totalDelivered: number
    totalOpened: number
    totalClicked: number
    deliveryRate: number
    openRate: number
    clickRate: number
    bounceRate: number
    unsubscribeRate: number
  }> {
    try {
      // Mock analytics data
      const analytics = {
        totalSent: 15420,
        totalDelivered: 15180,
        totalOpened: 8945,
        totalClicked: 2134,
        deliveryRate: 98.4,
        openRate: 58.9,
        clickRate: 13.8,
        bounceRate: 1.6,
        unsubscribeRate: 0.3
      }

      return analytics
    } catch (error) {
      console.error('Failed to fetch email analytics:', error)
      return {
        totalSent: 0,
        totalDelivered: 0,
        totalOpened: 0,
        totalClicked: 0,
        deliveryRate: 0,
        openRate: 0,
        clickRate: 0,
        bounceRate: 0,
        unsubscribeRate: 0
      }
    }
  }

  // Email Validation
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Template Variable Replacement
  replaceTemplateVariables(template: string, variables: Record<string, unknown>): string {
    let result = template
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      result = result.replace(regex, String(value))
    })

    return result
  }
}

export const emailService = new EmailService()
export default emailService
