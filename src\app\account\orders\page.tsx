"use client"

import { useState } from "react"
import Link from "next/link"
import { AccountLayout } from "@/components/layout/account-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useLanguage } from "@/components/providers/language-provider"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Download,
  RefreshCw,
  ShoppingBag,
} from "lucide-react"

// Mock order data
const mockOrders = [
  {
    id: "ORD-2024-001",
    orderNumber: "ORD-2024-001",
    status: "delivered",
    paymentStatus: "succeeded",
    total: 299.99,
    currency: "USD",
    items: [
      {
        id: "1",
        name: "Wireless Bluetooth Headphones",
        image: "/placeholder-product.jpg",
        price: 149.99,
        quantity: 1,
      },
      {
        id: "2",
        name: "USB-C Charging Cable",
        image: "/placeholder-product.jpg",
        price: 24.99,
        quantity: 2,
      },
    ],
    createdAt: new Date("2024-01-15"),
    shippedAt: new Date("2024-01-16"),
    deliveredAt: new Date("2024-01-18"),
    trackingNumber: "1Z999AA1234567890",
  },
  {
    id: "ORD-2024-002",
    orderNumber: "ORD-2024-002",
    status: "shipped",
    paymentStatus: "succeeded",
    total: 89.99,
    currency: "USD",
    items: [
      {
        id: "3",
        name: "Smartphone Case",
        image: "/placeholder-product.jpg",
        price: 29.99,
        quantity: 1,
      },
      {
        id: "4",
        name: "Screen Protector",
        image: "/placeholder-product.jpg",
        price: 19.99,
        quantity: 3,
      },
    ],
    createdAt: new Date("2024-01-20"),
    shippedAt: new Date("2024-01-21"),
    trackingNumber: "1Z999AA1234567891",
  },
  {
    id: "ORD-2024-003",
    orderNumber: "ORD-2024-003",
    status: "processing",
    paymentStatus: "succeeded",
    total: 199.99,
    currency: "USD",
    items: [
      {
        id: "5",
        name: "Wireless Mouse",
        image: "/placeholder-product.jpg",
        price: 79.99,
        quantity: 1,
      },
      {
        id: "6",
        name: "Keyboard",
        image: "/placeholder-product.jpg",
        price: 119.99,
        quantity: 1,
      },
    ],
    createdAt: new Date("2024-01-22"),
  },
]

const getStatusIcon = (status: string) => {
  switch (status) {
    case "pending":
      return <Clock className="h-4 w-4 text-yellow-500" />
    case "confirmed":
      return <CheckCircle className="h-4 w-4 text-blue-500" />
    case "processing":
      return <RefreshCw className="h-4 w-4 text-blue-500" />
    case "shipped":
      return <Truck className="h-4 w-4 text-purple-500" />
    case "delivered":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "cancelled":
      return <XCircle className="h-4 w-4 text-red-500" />
    default:
      return <Package className="h-4 w-4 text-gray-500" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "text-yellow-700 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900/30"
    case "confirmed":
      return "text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/30"
    case "processing":
      return "text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/30"
    case "shipped":
      return "text-purple-700 bg-purple-100 dark:text-purple-300 dark:bg-purple-900/30"
    case "delivered":
      return "text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/30"
    case "cancelled":
      return "text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30"
    default:
      return "text-gray-700 bg-gray-100 dark:text-gray-300 dark:bg-gray-900/30"
  }
}

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState("all")
  const { t } = useLanguage()

  const filterOrders = (status?: string) => {
    if (!status || status === "all") return mockOrders
    return mockOrders.filter(order => order.status === status)
  }

  const OrderCard = ({ order }: { order: typeof mockOrders[0] }) => (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              Order #{order.orderNumber}
            </CardTitle>
            <CardDescription>
              Placed on {formatDate(order.createdAt)}
            </CardDescription>
          </div>
          <div className="text-right">
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
              {getStatusIcon(order.status)}
              <span className="ml-1 capitalize">{order.status}</span>
            </div>
            <div className="text-lg font-semibold mt-1">
              {formatPrice(order.total, { currency: order.currency })}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Order Items */}
        <div className="space-y-3 mb-4">
          {order.items.map((item) => (
            <div key={item.id} className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                <Package className="h-6 w-6 text-gray-400" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-sm">{item.name}</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Qty: {item.quantity} × {formatPrice(item.price, { currency: order.currency })}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Order Timeline */}
        {(order.shippedAt || order.deliveredAt || order.trackingNumber) && (
          <div className="border-t pt-4 mb-4">
            <div className="space-y-2">
              {order.trackingNumber && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Tracking Number:</span>
                  <span className="font-mono">{order.trackingNumber}</span>
                </div>
              )}
              {order.shippedAt && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Shipped:</span>
                  <span>{formatDate(order.shippedAt)}</span>
                </div>
              )}
              {order.deliveredAt && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Delivered:</span>
                  <span>{formatDate(order.deliveredAt)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex space-x-2">
            <Link href={`/account/orders/${order.id}`}>
              <Button size="sm" variant="outline">
                <Eye className="h-4 w-4 mr-1" />
                View Details
              </Button>
            </Link>
            {order.status === "delivered" && (
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-1" />
                Invoice
              </Button>
            )}
          </div>
          
          <div className="flex space-x-2">
            {order.trackingNumber && order.status === "shipped" && (
              <Button size="sm" variant="outline">
                <Truck className="h-4 w-4 mr-1" />
                Track Package
              </Button>
            )}
            {order.status === "delivered" && (
              <Button size="sm">
                <RefreshCw className="h-4 w-4 mr-1" />
                Reorder
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <AccountLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Order History
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track and manage your orders
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="all">All Orders</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="processing">Processing</TabsTrigger>
            <TabsTrigger value="shipped">Shipped</TabsTrigger>
            <TabsTrigger value="delivered">Delivered</TabsTrigger>
            <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {filterOrders().length > 0 ? (
              filterOrders().map((order) => (
                <OrderCard key={order.id} order={order} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No orders yet
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Start shopping to see your orders here
                  </p>
                  <Link href="/shop">
                    <Button>
                      <ShoppingBag className="h-4 w-4 mr-2" />
                      Start Shopping
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="pending" className="space-y-4">
            {filterOrders("pending").length > 0 ? (
              filterOrders("pending").map((order) => (
                <OrderCard key={order.id} order={order} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No pending orders
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    All your orders have been processed
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="processing" className="space-y-4">
            {filterOrders("processing").length > 0 ? (
              filterOrders("processing").map((order) => (
                <OrderCard key={order.id} order={order} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <RefreshCw className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No processing orders
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    No orders are currently being processed
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="shipped" className="space-y-4">
            {filterOrders("shipped").length > 0 ? (
              filterOrders("shipped").map((order) => (
                <OrderCard key={order.id} order={order} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No shipped orders
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    No orders are currently shipped
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="delivered" className="space-y-4">
            {filterOrders("delivered").length > 0 ? (
              filterOrders("delivered").map((order) => (
                <OrderCard key={order.id} order={order} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No delivered orders
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    No orders have been delivered yet
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="cancelled" className="space-y-4">
            {filterOrders("cancelled").length > 0 ? (
              filterOrders("cancelled").map((order) => (
                <OrderCard key={order.id} order={order} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <XCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No cancelled orders
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    You haven't cancelled any orders
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AccountLayout>
  )
}
