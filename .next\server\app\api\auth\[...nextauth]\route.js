(()=>{var a={};a.id=14,a.ids=[14],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},767:(a,b,c)=>{var d=c(57502),e=c(30994),f=c(44726),g=c(9684);function h(b){var c="function"==typeof Map?new Map:void 0;return a.exports=h=function(a){if(null===a||!f(a))return a;if("function"!=typeof a)throw TypeError("Super expression must either be null or a function");if(void 0!==c){if(c.has(a))return c.get(a);c.set(a,b)}function b(){return g(a,arguments,d(this).constructor)}return b.prototype=Object.create(a.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),e(b,a)},a.exports.__esModule=!0,a.exports.default=a.exports,h(b)}a.exports=h,a.exports.__esModule=!0,a.exports.default=a.exports},913:(a,b,c)=>{let d=c(70885);a.exports=a=>{if("string"!=typeof a||!a)throw TypeError("JWT must be a string");let{0:b,1:c,2:e,length:f}=a.split(".");if(5===f)throw TypeError("encrypted JWTs cannot be decoded");if(3!==f)throw Error("JWTs must have three components");try{return{header:JSON.parse(d.decode(b)),payload:JSON.parse(d.decode(c)),signature:e}}catch(a){throw Error("JWT is malformed")}}},1149:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.EmbeddedJWK=void 0;let d=c(97920),e=c(25110),f=c(99938);b.EmbeddedJWK=async function(a,b){let c={...a,...null==b?void 0:b.header};if(!(0,e.default)(c.jwk))throw new f.JWSInvalid('"jwk" (JSON Web Key) Header Parameter must be a JSON object');let g=await (0,d.importJWK)({...c.jwk,ext:!0},c.alg,!0);if(g instanceof Uint8Array||"public"!==g.type)throw new f.JWSInvalid('"jwk" (JSON Web Key) Header Parameter must be a public key');return g}},1441:(a,b)=>{var c,d,e,f,g,h,i,j,k,l,m,n={},o=[],p=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,q=Array.isArray;function r(a,b){for(var c in b)a[c]=b[c];return a}function s(a){a&&a.parentNode&&a.parentNode.removeChild(a)}function t(a,b,d){var e,f,g,h={};for(g in b)"key"==g?e=b[g]:"ref"==g?f=b[g]:h[g]=b[g];if(arguments.length>2&&(h.children=arguments.length>3?c.call(arguments,2):d),"function"==typeof a&&null!=a.defaultProps)for(g in a.defaultProps)void 0===h[g]&&(h[g]=a.defaultProps[g]);return u(a,h,e,f,null)}function u(a,b,c,f,g){var h={type:a,props:b,key:c,ref:f,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==g?++e:g,__i:-1,__u:0};return null==g&&null!=d.vnode&&d.vnode(h),h}function v(a){return a.children}function w(a,b){this.props=a,this.context=b}function x(a,b){if(null==b)return a.__?x(a.__,a.__i+1):null;for(var c;b<a.__k.length;b++)if(null!=(c=a.__k[b])&&null!=c.__e)return c.__e;return"function"==typeof a.type?x(a):null}function y(a){(!a.__d&&(a.__d=!0)&&f.push(a)&&!z.__r++||g!==d.debounceRendering)&&((g=d.debounceRendering)||h)(z)}function z(){var a,b,c,e,g,h,j,k;for(f.sort(i);a=f.shift();)a.__d&&(b=f.length,e=void 0,h=(g=(c=a).__v).__e,j=[],k=[],c.__P&&((e=r({},g)).__v=g.__v+1,d.vnode&&d.vnode(e),E(c.__P,e,g,c.__n,c.__P.namespaceURI,32&g.__u?[h]:null,j,null==h?x(g):h,!!(32&g.__u),k),e.__v=g.__v,e.__.__k[e.__i]=e,F(j,e,k),e.__e!=h&&function a(b){var c,d;if(null!=(b=b.__)&&null!=b.__c){for(b.__e=b.__c.base=null,c=0;c<b.__k.length;c++)if(null!=(d=b.__k[c])&&null!=d.__e){b.__e=b.__c.base=d.__e;break}return a(b)}}(e)),f.length>b&&f.sort(i));z.__r=0}function A(a,b,c,e,f,g,h,i,j,k,l){var m,p,r,t,w,y=e&&e.__k||o,z=b.length;for(c.__d=j,function(a,b,c){var e,f,g,h,i,j=b.length,k=c.length,l=k,m=0;for(a.__k=[],e=0;e<j;e++)null!=(f=b[e])&&"boolean"!=typeof f&&"function"!=typeof f?(h=e+m,(f=a.__k[e]="string"==typeof f||"number"==typeof f||"bigint"==typeof f||f.constructor==String?u(null,f,null,null,null):q(f)?u(v,{children:f},null,null,null):void 0===f.constructor&&f.__b>0?u(f.type,f.props,f.key,f.ref?f.ref:null,f.__v):f).__=a,f.__b=a.__b+1,g=null,-1!==(i=f.__i=function(a,b,c,d){var e=a.key,f=a.type,g=c-1,h=c+1,i=b[c];if(null===i||i&&e==i.key&&f===i.type&&0==(131072&i.__u))return c;if(d>+(null!=i&&0==(131072&i.__u)))for(;g>=0||h<b.length;){if(g>=0){if((i=b[g])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return g;g--}if(h<b.length){if((i=b[h])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return h;h++}}return -1}(f,c,h,l))&&(l--,(g=c[i])&&(g.__u|=131072)),null==g||null===g.__v?(-1==i&&m--,"function"!=typeof f.type&&(f.__u|=65536)):i!==h&&(i==h-1?m--:i==h+1?m++:(i>h?m--:m++,f.__u|=65536))):f=a.__k[e]=null;if(l)for(e=0;e<k;e++)null!=(g=c[e])&&0==(131072&g.__u)&&(g.__e==a.__d&&(a.__d=x(g)),function a(b,c,e){var f,g;if(d.unmount&&d.unmount(b),(f=b.ref)&&(f.current&&f.current!==b.__e||G(f,null,c)),null!=(f=b.__c)){if(f.componentWillUnmount)try{f.componentWillUnmount()}catch(a){d.__e(a,c)}f.base=f.__P=null}if(f=b.__k)for(g=0;g<f.length;g++)f[g]&&a(f[g],c,e||"function"!=typeof b.type);e||s(b.__e),b.__c=b.__=b.__e=b.__d=void 0}(g,g))}(c,b,y),j=c.__d,m=0;m<z;m++)null!=(r=c.__k[m])&&(p=-1===r.__i?n:y[r.__i]||n,r.__i=m,E(a,r,p,f,g,h,i,j,k,l),t=r.__e,r.ref&&p.ref!=r.ref&&(p.ref&&G(p.ref,null,r),l.push(r.ref,r.__c||t,r)),null==w&&null!=t&&(w=t),65536&r.__u||p.__k===r.__k?j=function a(b,c,d){var e,f;if("function"==typeof b.type){for(e=b.__k,f=0;e&&f<e.length;f++)e[f]&&(e[f].__=b,c=a(e[f],c,d));return c}b.__e!=c&&(c&&b.type&&!d.contains(c)&&(c=x(b)),d.insertBefore(b.__e,c||null),c=b.__e);do c=c&&c.nextSibling;while(null!=c&&8===c.nodeType);return c}(r,j,a):"function"==typeof r.type&&void 0!==r.__d?j=r.__d:t&&(j=t.nextSibling),r.__d=void 0,r.__u&=-196609);c.__d=j,c.__e=w}function B(a,b,c){"-"===b[0]?a.setProperty(b,null==c?"":c):a[b]=null==c?"":"number"!=typeof c||p.test(b)?c:c+"px"}function C(a,b,c,d,e){var f;a:if("style"===b)if("string"==typeof c)a.style.cssText=c;else{if("string"==typeof d&&(a.style.cssText=d=""),d)for(b in d)c&&b in c||B(a.style,b,"");if(c)for(b in c)d&&c[b]===d[b]||B(a.style,b,c[b])}else if("o"===b[0]&&"n"===b[1])f=b!==(b=b.replace(/(PointerCapture)$|Capture$/i,"$1")),b=b.toLowerCase()in a||"onFocusOut"===b||"onFocusIn"===b?b.toLowerCase().slice(2):b.slice(2),a.l||(a.l={}),a.l[b+f]=c,c?d?c.t=d.t:(c.t=j,a.addEventListener(b,f?l:k,f)):a.removeEventListener(b,f?l:k,f);else{if("http://www.w3.org/2000/svg"==e)b=b.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=b&&"height"!=b&&"href"!=b&&"list"!=b&&"form"!=b&&"tabIndex"!=b&&"download"!=b&&"rowSpan"!=b&&"colSpan"!=b&&"role"!=b&&"popover"!=b&&b in a)try{a[b]=null==c?"":c;break a}catch(a){}"function"==typeof c||(null==c||!1===c&&"-"!==b[4]?a.removeAttribute(b):a.setAttribute(b,"popover"==b&&1==c?"":c))}}function D(a){return function(b){if(this.l){var c=this.l[b.type+a];if(null==b.u)b.u=j++;else if(b.u<c.t)return;return c(d.event?d.event(b):b)}}}function E(a,b,e,f,g,h,i,j,k,l){var m,o,p,t,u,y,z,B,D,E,F,G,I,J,K,L,M=b.type;if(void 0!==b.constructor)return null;128&e.__u&&(k=!!(32&e.__u),h=[j=b.__e=e.__e]),(m=d.__b)&&m(b);a:if("function"==typeof M)try{if(B=b.props,D="prototype"in M&&M.prototype.render,E=(m=M.contextType)&&f[m.__c],F=m?E?E.props.value:m.__:f,e.__c?z=(o=b.__c=e.__c).__=o.__E:(D?b.__c=o=new M(B,F):(b.__c=o=new w(B,F),o.constructor=M,o.render=H),E&&E.sub(o),o.props=B,o.state||(o.state={}),o.context=F,o.__n=f,p=o.__d=!0,o.__h=[],o._sb=[]),D&&null==o.__s&&(o.__s=o.state),D&&null!=M.getDerivedStateFromProps&&(o.__s==o.state&&(o.__s=r({},o.__s)),r(o.__s,M.getDerivedStateFromProps(B,o.__s))),t=o.props,u=o.state,o.__v=b,p)D&&null==M.getDerivedStateFromProps&&null!=o.componentWillMount&&o.componentWillMount(),D&&null!=o.componentDidMount&&o.__h.push(o.componentDidMount);else{if(D&&null==M.getDerivedStateFromProps&&B!==t&&null!=o.componentWillReceiveProps&&o.componentWillReceiveProps(B,F),!o.__e&&(null!=o.shouldComponentUpdate&&!1===o.shouldComponentUpdate(B,o.__s,F)||b.__v===e.__v)){for(b.__v!==e.__v&&(o.props=B,o.state=o.__s,o.__d=!1),b.__e=e.__e,b.__k=e.__k,b.__k.some(function(a){a&&(a.__=b)}),G=0;G<o._sb.length;G++)o.__h.push(o._sb[G]);o._sb=[],o.__h.length&&i.push(o);break a}null!=o.componentWillUpdate&&o.componentWillUpdate(B,o.__s,F),D&&null!=o.componentDidUpdate&&o.__h.push(function(){o.componentDidUpdate(t,u,y)})}if(o.context=F,o.props=B,o.__P=a,o.__e=!1,I=d.__r,J=0,D){for(o.state=o.__s,o.__d=!1,I&&I(b),m=o.render(o.props,o.state,o.context),K=0;K<o._sb.length;K++)o.__h.push(o._sb[K]);o._sb=[]}else do o.__d=!1,I&&I(b),m=o.render(o.props,o.state,o.context),o.state=o.__s;while(o.__d&&++J<25);o.state=o.__s,null!=o.getChildContext&&(f=r(r({},f),o.getChildContext())),D&&!p&&null!=o.getSnapshotBeforeUpdate&&(y=o.getSnapshotBeforeUpdate(t,u)),A(a,q(L=null!=m&&m.type===v&&null==m.key?m.props.children:m)?L:[L],b,e,f,g,h,i,j,k,l),o.base=b.__e,b.__u&=-161,o.__h.length&&i.push(o),z&&(o.__E=o.__=null)}catch(a){if(b.__v=null,k||null!=h){for(b.__u|=k?160:128;j&&8===j.nodeType&&j.nextSibling;)j=j.nextSibling;h[h.indexOf(j)]=null,b.__e=j}else b.__e=e.__e,b.__k=e.__k;d.__e(a,b,e)}else null==h&&b.__v===e.__v?(b.__k=e.__k,b.__e=e.__e):b.__e=function(a,b,e,f,g,h,i,j,k){var l,m,o,p,r,t,u,v=e.props,w=b.props,y=b.type;if("svg"===y?g="http://www.w3.org/2000/svg":"math"===y?g="http://www.w3.org/1998/Math/MathML":g||(g="http://www.w3.org/1999/xhtml"),null!=h){for(l=0;l<h.length;l++)if((r=h[l])&&"setAttribute"in r==!!y&&(y?r.localName===y:3===r.nodeType)){a=r,h[l]=null;break}}if(null==a){if(null===y)return document.createTextNode(w);a=document.createElementNS(g,y,w.is&&w),j&&(d.__m&&d.__m(b,h),j=!1),h=null}if(null===y)v===w||j&&a.data===w||(a.data=w);else{if(h=h&&c.call(a.childNodes),v=e.props||n,!j&&null!=h)for(v={},l=0;l<a.attributes.length;l++)v[(r=a.attributes[l]).name]=r.value;for(l in v)if(r=v[l],"children"==l);else if("dangerouslySetInnerHTML"==l)o=r;else if(!(l in w)){if("value"==l&&"defaultValue"in w||"checked"==l&&"defaultChecked"in w)continue;C(a,l,null,r,g)}for(l in w)r=w[l],"children"==l?p=r:"dangerouslySetInnerHTML"==l?m=r:"value"==l?t=r:"checked"==l?u=r:j&&"function"!=typeof r||v[l]===r||C(a,l,r,v[l],g);if(m)j||o&&(m.__html===o.__html||m.__html===a.innerHTML)||(a.innerHTML=m.__html),b.__k=[];else if(o&&(a.innerHTML=""),A(a,q(p)?p:[p],b,e,f,"foreignObject"===y?"http://www.w3.org/1999/xhtml":g,h,i,h?h[0]:e.__k&&x(e,0),j,k),null!=h)for(l=h.length;l--;)s(h[l]);j||(l="value","progress"===y&&null==t?a.removeAttribute("value"):void 0===t||t===a[l]&&("progress"!==y||t)&&("option"!==y||t===v[l])||C(a,l,t,v[l],g),l="checked",void 0!==u&&u!==a[l]&&C(a,l,u,v[l],g))}return a}(e.__e,b,e,f,g,h,i,k,l);(m=d.diffed)&&m(b)}function F(a,b,c){b.__d=void 0;for(var e=0;e<c.length;e++)G(c[e],c[++e],c[++e]);d.__c&&d.__c(b,a),a.some(function(b){try{a=b.__h,b.__h=[],a.some(function(a){a.call(b)})}catch(a){d.__e(a,b.__v)}})}function G(a,b,c){try{if("function"==typeof a){var e="function"==typeof a.__u;e&&a.__u(),e&&null==b||(a.__u=a(b))}else a.current=b}catch(a){d.__e(a,c)}}function H(a,b,c){return this.constructor(a,c)}function I(a,b,e){var f,g,h,i;d.__&&d.__(a,b),g=(f="function"==typeof e)?null:e&&e.__k||b.__k,h=[],i=[],E(b,a=(!f&&e||b).__k=t(v,null,[a]),g||n,n,b.namespaceURI,!f&&e?[e]:g?null:b.firstChild?c.call(b.childNodes):null,h,!f&&e?e:g?g.__e:b.firstChild,f,i),F(h,a,i)}c=o.slice,d={__e:function(a,b,c,d){for(var e,f,g;b=b.__;)if((e=b.__c)&&!e.__)try{if((f=e.constructor)&&null!=f.getDerivedStateFromError&&(e.setState(f.getDerivedStateFromError(a)),g=e.__d),null!=e.componentDidCatch&&(e.componentDidCatch(a,d||{}),g=e.__d),g)return e.__E=e}catch(b){a=b}throw a}},e=0,w.prototype.setState=function(a,b){var c;c=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=r({},this.state),"function"==typeof a&&(a=a(r({},c),this.props)),a&&r(c,a),null!=a&&this.__v&&(b&&this._sb.push(b),y(this))},w.prototype.forceUpdate=function(a){this.__v&&(this.__e=!0,a&&this.__h.push(a),y(this))},w.prototype.render=v,f=[],h="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,i=function(a,b){return a.__v.__b-b.__v.__b},z.__r=0,j=0,k=D(!1),l=D(!0),m=0,b.Component=w,b.Fragment=v,b.cloneElement=function(a,b,d){var e,f,g,h,i=r({},a.props);for(g in a.type&&a.type.defaultProps&&(h=a.type.defaultProps),b)"key"==g?e=b[g]:"ref"==g?f=b[g]:i[g]=void 0===b[g]&&void 0!==h?h[g]:b[g];return arguments.length>2&&(i.children=arguments.length>3?c.call(arguments,2):d),u(a.type,i,e||a.key,f||a.ref,null)},b.createContext=function(a,b){var c={__c:b="__cC"+m++,__:a,Consumer:function(a,b){return a.children(b)},Provider:function(a){var c,d;return this.getChildContext||(c=new Set,(d={})[b]=this,this.getChildContext=function(){return d},this.componentWillUnmount=function(){c=null},this.shouldComponentUpdate=function(a){this.props.value!==a.value&&c.forEach(function(a){a.__e=!0,y(a)})},this.sub=function(a){c.add(a);var b=a.componentWillUnmount;a.componentWillUnmount=function(){c&&c.delete(a),b&&b.call(a)}}),a.children}};return c.Provider.__=c.Consumer.contextType=c},b.createElement=t,b.createRef=function(){return{current:null}},b.h=t,b.hydrate=function a(b,c){I(b,c,a)},b.isValidElement=function(a){return null!=a&&null==a.constructor},b.options=d,b.render=I,b.toChildArray=function a(b,c){return c=c||[],null==b||"boolean"==typeof b||(q(b)?b.some(function(b){a(b,c)}):c.push(b)),c}},1889:function(a,b,c){(function(a,b){var c=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,d=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,e=/[\s\n\\/='"\0<>]/,f=/^xlink:?./,g=/["&<]/;function h(a){if(!1===g.test(a+=""))return a;for(var b=0,c=0,d="",e="";c<a.length;c++){switch(a.charCodeAt(c)){case 34:e="&quot;";break;case 38:e="&amp;";break;case 60:e="&lt;";break;default:continue}c!==b&&(d+=a.slice(b,c)),d+=e,b=c+1}return c!==b&&(d+=a.slice(b,c)),d}var i=function(a,b){return String(a).replace(/(\n+)/g,"$1"+(b||"	"))},j=function(a,b,c){return String(a).length>(b||40)||!c&&-1!==String(a).indexOf("\n")||-1!==String(a).indexOf("<")},k={},l=/([A-Z])/g;function m(a){var b="";for(var d in a){var e=a[d];null!=e&&""!==e&&(b&&(b+=" "),b+="-"==d[0]?d:k[d]||(k[d]=d.replace(l,"-$1").toLowerCase()),b="number"==typeof e&&!1===c.test(d)?b+": "+e+"px;":b+": "+e+";")}return b||void 0}function n(a,b){return Array.isArray(b)?b.reduce(n,a):null!=b&&!1!==b&&a.push(b),a}function o(){this.__d=!0}function p(a,b){return{__v:a,context:b,props:a.props,setState:o,forceUpdate:o,__d:!0,__h:[]}}function q(a,b){var c=a.contextType,d=c&&b[c.__c];return null!=c?d?d.props.value:c.__:b}var r=[],s={shallow:!0};v.render=v;var t=function(a,b){return v(a,b,s)},u=[];function v(a,c,g){c=c||{};var k=b.options.__s;b.options.__s=!0;var l,o=b.h(b.Fragment,null);return o.__k=[a],l=g&&(g.pretty||g.voidElements||g.sortAttributes||g.shallow||g.allAttributes||g.xml||g.attributeHook)?function a(c,g,k,l,o,s){if(null==c||"boolean"==typeof c)return"";if("object"!=typeof c)return"function"==typeof c?"":h(c);var t=k.pretty,u=t&&"string"==typeof t?t:"	";if(Array.isArray(c)){for(var v="",w=0;w<c.length;w++)t&&w>0&&(v+="\n"),v+=a(c[w],g,k,l,o,s);return v}if(void 0!==c.constructor)return"";var x,y=c.type,z=c.props,A=!1;if("function"==typeof y){if(A=!0,!k.shallow||!l&&!1!==k.renderRootComponent){if(y===b.Fragment){var B=[];return n(B,c.props.children),a(B,g,k,!1!==k.shallowHighOrder,o,s)}var C,D=c.__c=p(c,g);b.options.__b&&b.options.__b(c);var E=b.options.__r;if(y.prototype&&"function"==typeof y.prototype.render){var F=q(y,g);(D=c.__c=new y(z,F)).__v=c,D._dirty=D.__d=!0,D.props=z,null==D.state&&(D.state={}),null==D._nextState&&null==D.__s&&(D._nextState=D.__s=D.state),D.context=F,y.getDerivedStateFromProps?D.state=Object.assign({},D.state,y.getDerivedStateFromProps(D.props,D.state)):D.componentWillMount&&(D.componentWillMount(),D.state=D._nextState!==D.state?D._nextState:D.__s!==D.state?D.__s:D.state),E&&E(c),C=D.render(D.props,D.state,D.context)}else for(var G=q(y,g),H=0;D.__d&&H++<25;)D.__d=!1,E&&E(c),C=y.call(c.__c,z,G);return D.getChildContext&&(g=Object.assign({},g,D.getChildContext())),b.options.diffed&&b.options.diffed(c),a(C,g,k,!1!==k.shallowHighOrder,o,s)}y=(x=y).displayName||x!==Function&&x.name||function(a){var b=(Function.prototype.toString.call(a).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!b){for(var c=-1,d=r.length;d--;)if(r[d]===a){c=d;break}c<0&&(c=r.push(a)-1),b="UnnamedComponent"+c}return b}(x)}var I,J,K="<"+y;if(z){var L=Object.keys(z);k&&!0===k.sortAttributes&&L.sort();for(var M=0;M<L.length;M++){var N=L[M],O=z[N];if("children"!==N){if(!e.test(N)&&(k&&k.allAttributes||"key"!==N&&"ref"!==N&&"__self"!==N&&"__source"!==N)){if("defaultValue"===N)N="value";else if("defaultChecked"===N)N="checked";else if("defaultSelected"===N)N="selected";else if("className"===N){if(void 0!==z.class)continue;N="class"}else o&&f.test(N)&&(N=N.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===N){if(z.for)continue;N="for"}"style"===N&&O&&"object"==typeof O&&(O=m(O)),"a"===N[0]&&"r"===N[1]&&"boolean"==typeof O&&(O=String(O));var P=k.attributeHook&&k.attributeHook(N,O,g,k,A);if(P||""===P)K+=P;else if("dangerouslySetInnerHTML"===N)J=O&&O.__html;else if("textarea"===y&&"value"===N)I=O;else if((O||0===O||""===O)&&"function"!=typeof O){if(!(!0!==O&&""!==O||(O=N,k&&k.xml))){K=K+" "+N;continue}if("value"===N){if("select"===y){s=O;continue}"option"===y&&s==O&&void 0===z.selected&&(K+=" selected")}K=K+" "+N+'="'+h(O)+'"'}}}else I=O}}if(t){var Q=K.replace(/\n\s*/," ");Q===K||~Q.indexOf("\n")?t&&~K.indexOf("\n")&&(K+="\n"):K=Q}if(K+=">",e.test(y))throw Error(y+" is not a valid HTML tag name in "+K);var R,S=d.test(y)||k.voidElements&&k.voidElements.test(y),T=[];if(J)t&&j(J)&&(J="\n"+u+i(J,u)),K+=J;else if(null!=I&&n(R=[],I).length){for(var U=t&&~K.indexOf("\n"),V=!1,W=0;W<R.length;W++){var X=R[W];if(null!=X&&!1!==X){var Y=a(X,g,k,!0,"svg"===y||"foreignObject"!==y&&o,s);if(t&&!U&&j(Y)&&(U=!0),Y)if(t){var Z=Y.length>0&&"<"!=Y[0];V&&Z?T[T.length-1]+=Y:T.push(Y),V=Z}else T.push(Y)}}if(t&&U)for(var $=T.length;$--;)T[$]="\n"+u+i(T[$],u)}if(T.length||J)K+=T.join("");else if(k&&k.xml)return K.substring(0,K.length-1)+" />";return!S||R||J?(t&&~K.indexOf("\n")&&(K+="\n"),K=K+"</"+y+">"):K=K.replace(/>$/," />"),K}(a,c,g):function a(c,g,i,j,k){if(null==c||!0===c||!1===c||""===c)return"";if("object"!=typeof c)return"function"==typeof c?"":h(c);if(x(c)){var l="";k.__k=c;for(var n=0;n<c.length;n++)l+=a(c[n],g,i,j,k),c[n]=w(c[n]);return l}if(void 0!==c.constructor)return"";c.__=k,b.options.__b&&b.options.__b(c);var o=c.type,r=c.props;if("function"==typeof o){if(o===b.Fragment)A=r.children;else{var s,t,u,v,z,A=o.prototype&&"function"==typeof o.prototype.render?(s=g,u=q(t=c.type,s),v=new t(c.props,u),c.__c=v,v.__v=c,v.__d=!0,v.props=c.props,null==v.state&&(v.state={}),null==v.__s&&(v.__s=v.state),v.context=u,t.getDerivedStateFromProps?v.state=y({},v.state,t.getDerivedStateFromProps(v.props,v.state)):v.componentWillMount&&(v.componentWillMount(),v.state=v.__s!==v.state?v.__s:v.state),(z=b.options.__r)&&z(c),v.render(v.props,v.state,v.context)):function(a,c){var d,e=p(a,c),f=q(a.type,c);a.__c=e;for(var g=b.options.__r,h=0;e.__d&&h++<25;)e.__d=!1,g&&g(a),d=a.type.call(e,a.props,f);return d}(c,g),B=c.__c;B.getChildContext&&(g=y({},g,B.getChildContext()))}var C=a(A=null!=A&&A.type===b.Fragment&&null==A.key?A.props.children:A,g,i,j,c);return b.options.diffed&&b.options.diffed(c),c.__=void 0,b.options.unmount&&b.options.unmount(c),C}var D,E,F="<";if(F+=o,r)for(var G in D=r.children,r){var H,I,J,K=r[G];if(!("key"===G||"ref"===G||"__self"===G||"__source"===G||"children"===G||"className"===G&&"class"in r||"htmlFor"===G&&"for"in r||e.test(G))){if(I=G="className"===(H=G)?"class":"htmlFor"===H?"for":"defaultValue"===H?"value":"defaultChecked"===H?"checked":"defaultSelected"===H?"selected":i&&f.test(H)?H.toLowerCase().replace(/^xlink:?/,"xlink:"):H,J=K,K="style"===I&&null!=J&&"object"==typeof J?m(J):"a"===I[0]&&"r"===I[1]&&"boolean"==typeof J?String(J):J,"dangerouslySetInnerHTML"===G)E=K&&K.__html;else if("textarea"===o&&"value"===G)D=K;else if((K||0===K||""===K)&&"function"!=typeof K){if(!0===K||""===K){K=G,F=F+" "+G;continue}if("value"===G){if("select"===o){j=K;continue}"option"!==o||j!=K||"selected"in r||(F+=" selected")}F=F+" "+G+'="'+h(K)+'"'}}}var L=F;if(F+=">",e.test(o))throw Error(o+" is not a valid HTML tag name in "+F);var M="",N=!1;if(E)M+=E,N=!0;else if("string"==typeof D)M+=h(D),N=!0;else if(x(D)){c.__k=D;for(var O=0;O<D.length;O++){var P=D[O];if(D[O]=w(P),null!=P&&!1!==P){var Q=a(P,g,"svg"===o||"foreignObject"!==o&&i,j,c);Q&&(M+=Q,N=!0)}}}else if(null!=D&&!1!==D&&!0!==D){c.__k=[w(D)];var R=a(D,g,"svg"===o||"foreignObject"!==o&&i,j,c);R&&(M+=R,N=!0)}if(b.options.diffed&&b.options.diffed(c),c.__=void 0,b.options.unmount&&b.options.unmount(c),N)F+=M;else if(d.test(o))return L+" />";return F+"</"+o+">"}(a,c,!1,void 0,o),b.options.__c&&b.options.__c(a,u),b.options.__s=k,u.length=0,l}function w(a){return null==a||"boolean"==typeof a?null:"string"==typeof a||"number"==typeof a||"bigint"==typeof a?b.h(null,null,a):a}var x=Array.isArray,y=Object.assign;v.shallowRender=t,a.default=v,a.render=v,a.renderToStaticMarkup=v,a.renderToString=v,a.shallowRender=t})(b,c(1441))},2055:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.createLocalJWKSet=b.LocalJWKSet=b.isJWKSLike=void 0;let d=c(97920),e=c(99938),f=c(25110);function g(a){return a&&"object"==typeof a&&Array.isArray(a.keys)&&a.keys.every(h)}function h(a){return(0,f.default)(a)}b.isJWKSLike=g;class i{constructor(a){if(this._cached=new WeakMap,!g(a))throw new e.JWKSInvalid("JSON Web Key Set malformed");this._jwks=function(a){return"function"==typeof structuredClone?structuredClone(a):JSON.parse(JSON.stringify(a))}(a)}async getKey(a,b){let{alg:c,kid:d}={...a,...null==b?void 0:b.header},f=function(a){switch("string"==typeof a&&a.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new e.JOSENotSupported('Unsupported "alg" value for a JSON Web Key Set')}}(c),g=this._jwks.keys.filter(a=>{let b=f===a.kty;if(b&&"string"==typeof d&&(b=d===a.kid),b&&"string"==typeof a.alg&&(b=c===a.alg),b&&"string"==typeof a.use&&(b="sig"===a.use),b&&Array.isArray(a.key_ops)&&(b=a.key_ops.includes("verify")),b&&"EdDSA"===c&&(b="Ed25519"===a.crv||"Ed448"===a.crv),b)switch(c){case"ES256":b="P-256"===a.crv;break;case"ES256K":b="secp256k1"===a.crv;break;case"ES384":b="P-384"===a.crv;break;case"ES512":b="P-521"===a.crv}return b}),{0:h,length:i}=g;if(0===i)throw new e.JWKSNoMatchingKey;if(1!==i){let a=new e.JWKSMultipleMatchingKeys,{_cached:b}=this;throw a[Symbol.asyncIterator]=async function*(){for(let a of g)try{yield await j(b,a,c)}catch{continue}},a}return j(this._cached,h,c)}}async function j(a,b,c){let f=a.get(b)||a.set(b,{}).get(b);if(void 0===f[c]){let a=await (0,d.importJWK)({...b,ext:!0},c);if(a instanceof Uint8Array||"public"!==a.type)throw new e.JWKSInvalid("JSON Web Key Set members must be public keys");f[c]=a}return f[c]}b.LocalJWKSet=i,b.createLocalJWKSet=function(a){let b=new i(a);return async function(a,c){return b.getKey(a,c)}}},2279:a=>{a.exports={assertSigningAlgValuesSupport:function(a,b,c){if(!b[`${a}_endpoint`])return;let d=`${a}_endpoint_auth_method`,e=`${a}_endpoint_auth_signing_alg`,f=`${a}_endpoint_auth_signing_alg_values_supported`;if(c[d]&&c[d].endsWith("_jwt")&&!c[e]&&!b[f])throw TypeError(`${f} must be configured on the issuer if ${e} is not defined on a client`)},assertIssuerConfiguration:function(a,b){if(!a[b])throw TypeError(`${b} must be configured on the issuer`)}}},2594:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.concatKdf=b.lengthAndInput=b.uint32be=b.uint64be=b.p2s=b.concat=b.decoder=b.encoder=void 0;let d=c(37375);function e(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;return a.forEach(a=>{b.set(a,c),c+=a.length}),b}function f(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function g(a){let b=new Uint8Array(4);return f(b,a),b}b.encoder=new TextEncoder,b.decoder=new TextDecoder,b.concat=e,b.p2s=function(a,c){return e(b.encoder.encode(a),new Uint8Array([0]),c)},b.uint64be=function(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return f(c,b,0),f(c,a%0x100000000,4),c},b.uint32be=g,b.lengthAndInput=function(a){return e(g(a.length),a)},b.concatKdf=async function(a,b,c){let e=Math.ceil((b>>3)/32),f=new Uint8Array(32*e);for(let b=0;b<e;b++){let e=new Uint8Array(4+a.length+c.length);e.set(g(b+1)),e.set(a,4),e.set(c,4+a.length),f.set(await (0,d.default)("sha256",e),32*b)}return f.slice(0,b>>3)}},2754:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.ProduceJWT=void 0;let d=c(69717),e=c(25110),f=c(86852);class g{constructor(a){if(!(0,e.default)(a))throw TypeError("JWT Claims Set MUST be an object");this._payload=a}setIssuer(a){return this._payload={...this._payload,iss:a},this}setSubject(a){return this._payload={...this._payload,sub:a},this}setAudience(a){return this._payload={...this._payload,aud:a},this}setJti(a){return this._payload={...this._payload,jti:a},this}setNotBefore(a){return"number"==typeof a?this._payload={...this._payload,nbf:a}:this._payload={...this._payload,nbf:(0,d.default)(new Date)+(0,f.default)(a)},this}setExpirationTime(a){return"number"==typeof a?this._payload={...this._payload,exp:a}:this._payload={...this._payload,exp:(0,d.default)(new Date)+(0,f.default)(a)},this}setIssuedAt(a){return void 0===a?this._payload={...this._payload,iat:(0,d.default)(new Date)}:this._payload={...this._payload,iat:a},this}}b.ProduceJWT=g},3038:(a,b,c)=>{var d=c(24956).default,e=c(7251);a.exports=function(a){var b=e(a,"string");return"symbol"==d(b)?b:b+""},a.exports.__esModule=!0,a.exports.default=a.exports},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3399:(a,b,c)=>{var d=c(3038);a.exports=function(a,b,c){return(b=d(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a},a.exports.__esModule=!0,a.exports.default=a.exports},4275:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(79428),e=c(99938),f=d.Buffer.from([0]),g=d.Buffer.from([2]),h=d.Buffer.from([3]),i=d.Buffer.from([48]),j=d.Buffer.from([4]),k=a=>{if(a<128)return d.Buffer.from([a]);let b=d.Buffer.alloc(5);b.writeUInt32BE(a,1);let c=1;for(;0===b[c];)c++;return b[c-1]=128|5-c,b.slice(c-1)},l=new Map([["P-256",d.Buffer.from("06 08 2A 86 48 CE 3D 03 01 07".replace(/ /g,""),"hex")],["secp256k1",d.Buffer.from("06 05 2B 81 04 00 0A".replace(/ /g,""),"hex")],["P-384",d.Buffer.from("06 05 2B 81 04 00 22".replace(/ /g,""),"hex")],["P-521",d.Buffer.from("06 05 2B 81 04 00 23".replace(/ /g,""),"hex")],["ecPublicKey",d.Buffer.from("06 07 2A 86 48 CE 3D 02 01".replace(/ /g,""),"hex")],["X25519",d.Buffer.from("06 03 2B 65 6E".replace(/ /g,""),"hex")],["X448",d.Buffer.from("06 03 2B 65 6F".replace(/ /g,""),"hex")],["Ed25519",d.Buffer.from("06 03 2B 65 70".replace(/ /g,""),"hex")],["Ed448",d.Buffer.from("06 03 2B 65 71".replace(/ /g,""),"hex")]]);class m{constructor(){this.length=0,this.elements=[]}oidFor(a){let b=l.get(a);if(!b)throw new e.JOSENotSupported("Invalid or unsupported OID");this.elements.push(b),this.length+=b.length}zero(){this.elements.push(g,d.Buffer.from([1]),f),this.length+=3}one(){this.elements.push(g,d.Buffer.from([1]),d.Buffer.from([1])),this.length+=3}unsignedInteger(a){if(128&a[0]){let b=k(a.length+1);this.elements.push(g,b,f,a),this.length+=2+b.length+a.length}else{let b=0;for(;0===a[b]&&(128&a[b+1])==0;)b++;let c=k(a.length-b);this.elements.push(g,k(a.length-b),a.slice(b)),this.length+=1+c.length+a.length-b}}octStr(a){let b=k(a.length);this.elements.push(j,k(a.length),a),this.length+=1+b.length+a.length}bitStr(a){let b=k(a.length+1);this.elements.push(h,k(a.length+1),f,a),this.length+=1+b.length+a.length+1}add(a){this.elements.push(a),this.length+=a.length}end(a=i){let b=k(this.length);return d.Buffer.concat([a,b,...this.elements],1+b.length+this.length)}}b.default=m},6680:(a,b)=>{"use strict";function c(a){return a&&"object"==typeof a&&!Array.isArray(a)}Object.defineProperty(b,"__esModule",{value:!0}),b.merge=function a(b,...d){if(!d.length)return b;let e=d.shift();if(c(b)&&c(e))for(let d in e)c(e[d])?(b[d]||Object.assign(b,{[d]:{}}),a(b[d],e[d])):Object.assign(b,{[d]:e[d]});return a(b,...d)}},6702:(a,b)=>{function c(a){for(var b,c,d="",e=-1;++e<a.length;)b=a.charCodeAt(e),c=e+1<a.length?a.charCodeAt(e+1):0,55296<=b&&b<=56319&&56320<=c&&c<=57343&&(b=65536+((1023&b)<<10)+(1023&c),e++),b<=127?d+=String.fromCharCode(b):b<=2047?d+=String.fromCharCode(192|b>>>6&31,128|63&b):b<=65535?d+=String.fromCharCode(224|b>>>12&15,128|b>>>6&63,128|63&b):b<=2097151&&(d+=String.fromCharCode(240|b>>>18&7,128|b>>>12&63,128|b>>>6&63,128|63&b));return d}function d(a){for(var b=Array(a.length>>2),c=0;c<b.length;c++)b[c]=0;for(var c=0;c<8*a.length;c+=8)b[c>>5]|=(255&a.charCodeAt(c/8))<<24-c%32;return b}function e(a,b){a[b>>5]|=128<<24-b%32,a[(b+64>>9<<4)+15]=b;for(var c=Array(80),d=0x67452301,e=-0x10325477,h=-0x67452302,i=0x10325476,j=-0x3c2d1e10,k=0;k<a.length;k+=16){for(var l=d,m=e,n=h,o=i,p=j,q=0;q<80;q++){q<16?c[q]=a[k+q]:c[q]=g(c[q-3]^c[q-8]^c[q-14]^c[q-16],1);var r,s,t,u,v,w=f(f(g(d,5),(r=q,s=e,t=h,u=i,r<20?s&t|~s&u:r<40?s^t^u:r<60?s&t|s&u|t&u:s^t^u)),f(f(j,c[q]),(v=q)<20?0x5a827999:v<40?0x6ed9eba1:v<60?-0x70e44324:-0x359d3e2a));j=i,i=h,h=g(e,30),e=d,d=w}d=f(d,l),e=f(e,m),h=f(h,n),i=f(i,o),j=f(j,p)}return[d,e,h,i,j]}function f(a,b){var c=(65535&a)+(65535&b);return(a>>16)+(b>>16)+(c>>16)<<16|65535&c}function g(a,b){return a<<b|a>>>32-b}b.HMACSHA1=function(a,b){return function(a){for(var b="",c=a.length,d=0;d<c;d+=3)for(var e=a.charCodeAt(d)<<16|(d+1<c?a.charCodeAt(d+1)<<8:0)|(d+2<c?a.charCodeAt(d+2):0),f=0;f<4;f++)8*d+6*f>8*a.length?b+="=":b+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e>>>6*(3-f)&63);return b}(function(a,b){var c=d(a);c.length>16&&(c=e(c,8*a.length));for(var f=Array(16),g=Array(16),h=0;h<16;h++)f[h]=0x36363636^c[h],g[h]=0x5c5c5c5c^c[h];for(var i=e(f.concat(d(b)),512+8*b.length),j=e(g.concat(i),672),k="",l=0;l<32*j.length;l+=8)k+=String.fromCharCode(j[l>>5]>>>24-l%32&255);return k}(c(a),c(b)))}},7161:(a,b,c)=>{let d=c(55511),[e,f]=process.version.substring(1).split(".").map(a=>parseInt(a,10));a.exports=(e>12||12===e&&f>=8)&&d.getHashes().includes("shake256")},7251:(a,b,c)=>{var d=c(24956).default;a.exports=function(a,b){if("object"!=d(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var e=c.call(a,b||"default");if("object"!=d(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},a.exports.__esModule=!0,a.exports.default=a.exports},7552:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=void 0;var d=c(55511);Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d.randomFillSync}})},8157:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=c(67131).default},8365:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.oAuth1Client=function(a){var b,c;let e=a.provider,f=new d.OAuth(e.requestTokenUrl,e.accessTokenUrl,e.clientId,e.clientSecret,null!=(b=e.version)?b:"1.0",e.callbackUrl,null!=(c=e.encoding)?c:"HMAC-SHA1"),g=f.get.bind(f);f.get=async(...a)=>await new Promise((b,c)=>{g(...a,(a,d)=>{if(a)return c(a);b(d)})});let h=f.getOAuthAccessToken.bind(f);f.getOAuthAccessToken=async(...a)=>await new Promise((b,c)=>{h(...a,(a,d,e)=>{if(a)return c(a);b({oauth_token:d,oauth_token_secret:e})})});let i=f.getOAuthRequestToken.bind(f);return f.getOAuthRequestToken=async(a={})=>await new Promise((b,c)=>{i(a,(a,d,e,f)=>{if(a)return c(a);b({oauth_token:d,oauth_token_secret:e,params:f})})}),f},b.oAuth1TokenStore=void 0;var d=c(11819);b.oAuth1TokenStore=new Map},8494:(a,b,c)=>{"use strict";var d=c(55511);function e(a,b){return b=h(a,b),function(a,b){if(void 0===(c="passthrough"!==b.algorithm?d.createHash(b.algorithm):new k).write&&(c.write=c.update,c.end=c.update),j(b,c).dispatch(a),c.update||c.end(""),c.digest)return c.digest("buffer"===b.encoding?void 0:b.encoding);var c,e=c.read();return"buffer"===b.encoding?e:e.toString(b.encoding)}(a,b)}(b=a.exports=e).sha1=function(a){return e(a)},b.keys=function(a){return e(a,{excludeValues:!0,algorithm:"sha1",encoding:"hex"})},b.MD5=function(a){return e(a,{algorithm:"md5",encoding:"hex"})},b.keysMD5=function(a){return e(a,{algorithm:"md5",encoding:"hex",excludeValues:!0})};var f=d.getHashes?d.getHashes().slice():["sha1","md5"];f.push("passthrough");var g=["buffer","hex","binary","base64"];function h(a,b){var c={};if(c.algorithm=(b=b||{}).algorithm||"sha1",c.encoding=b.encoding||"hex",c.excludeValues=!!b.excludeValues,c.algorithm=c.algorithm.toLowerCase(),c.encoding=c.encoding.toLowerCase(),c.ignoreUnknown=!0===b.ignoreUnknown,c.respectType=!1!==b.respectType,c.respectFunctionNames=!1!==b.respectFunctionNames,c.respectFunctionProperties=!1!==b.respectFunctionProperties,c.unorderedArrays=!0===b.unorderedArrays,c.unorderedSets=!1!==b.unorderedSets,c.unorderedObjects=!1!==b.unorderedObjects,c.replacer=b.replacer||void 0,c.excludeKeys=b.excludeKeys||void 0,void 0===a)throw Error("Object argument required.");for(var d=0;d<f.length;++d)f[d].toLowerCase()===c.algorithm.toLowerCase()&&(c.algorithm=f[d]);if(-1===f.indexOf(c.algorithm))throw Error('Algorithm "'+c.algorithm+'"  not supported. supported values: '+f.join(", "));if(-1===g.indexOf(c.encoding)&&"passthrough"!==c.algorithm)throw Error('Encoding "'+c.encoding+'"  not supported. supported values: '+g.join(", "));return c}function i(a){return"function"==typeof a&&null!=/^function\s+\w*\s*\(\s*\)\s*{\s+\[native code\]\s+}$/i.exec(Function.prototype.toString.call(a))}function j(a,b,c){c=c||[];var d=function(a){return b.update?b.update(a,"utf8"):b.write(a,"utf8")};return{dispatch:function(b){a.replacer&&(b=a.replacer(b));var c=typeof b;return null===b&&(c="null"),this["_"+c](b)},_object:function(b){var e=Object.prototype.toString.call(b),f=/\[object (.*)\]/i.exec(e);f=(f=f?f[1]:"unknown:["+e+"]").toLowerCase();var g=null;if((g=c.indexOf(b))>=0)return this.dispatch("[CIRCULAR:"+g+"]");if(c.push(b),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(b))return d("buffer:"),d(b);if("object"!==f&&"function"!==f&&"asyncfunction"!==f)if(this["_"+f])this["_"+f](b);else if(a.ignoreUnknown)return d("["+f+"]");else throw Error('Unknown object type "'+f+'"');else{var h=Object.keys(b);a.unorderedObjects&&(h=h.sort()),!1===a.respectType||i(b)||h.splice(0,0,"prototype","__proto__","constructor"),a.excludeKeys&&(h=h.filter(function(b){return!a.excludeKeys(b)})),d("object:"+h.length+":");var j=this;return h.forEach(function(c){j.dispatch(c),d(":"),a.excludeValues||j.dispatch(b[c]),d(",")})}},_array:function(b,e){e=void 0!==e?e:!1!==a.unorderedArrays;var f=this;if(d("array:"+b.length+":"),!e||b.length<=1)return b.forEach(function(a){return f.dispatch(a)});var g=[],h=b.map(function(b){var d=new k,e=c.slice();return j(a,d,e).dispatch(b),g=g.concat(e.slice(c.length)),d.read().toString()});return c=c.concat(g),h.sort(),this._array(h,!1)},_date:function(a){return d("date:"+a.toJSON())},_symbol:function(a){return d("symbol:"+a.toString())},_error:function(a){return d("error:"+a.toString())},_boolean:function(a){return d("bool:"+a.toString())},_string:function(a){d("string:"+a.length+":"),d(a.toString())},_function:function(b){d("fn:"),i(b)?this.dispatch("[native]"):this.dispatch(b.toString()),!1!==a.respectFunctionNames&&this.dispatch("function-name:"+String(b.name)),a.respectFunctionProperties&&this._object(b)},_number:function(a){return d("number:"+a.toString())},_xml:function(a){return d("xml:"+a.toString())},_null:function(){return d("Null")},_undefined:function(){return d("Undefined")},_regexp:function(a){return d("regex:"+a.toString())},_uint8array:function(a){return d("uint8array:"),this.dispatch(Array.prototype.slice.call(a))},_uint8clampedarray:function(a){return d("uint8clampedarray:"),this.dispatch(Array.prototype.slice.call(a))},_int8array:function(a){return d("uint8array:"),this.dispatch(Array.prototype.slice.call(a))},_uint16array:function(a){return d("uint16array:"),this.dispatch(Array.prototype.slice.call(a))},_int16array:function(a){return d("uint16array:"),this.dispatch(Array.prototype.slice.call(a))},_uint32array:function(a){return d("uint32array:"),this.dispatch(Array.prototype.slice.call(a))},_int32array:function(a){return d("uint32array:"),this.dispatch(Array.prototype.slice.call(a))},_float32array:function(a){return d("float32array:"),this.dispatch(Array.prototype.slice.call(a))},_float64array:function(a){return d("float64array:"),this.dispatch(Array.prototype.slice.call(a))},_arraybuffer:function(a){return d("arraybuffer:"),this.dispatch(new Uint8Array(a))},_url:function(a){return d("url:"+a.toString(),"utf8")},_map:function(b){d("map:");var c=Array.from(b);return this._array(c,!1!==a.unorderedSets)},_set:function(b){d("set:");var c=Array.from(b);return this._array(c,!1!==a.unorderedSets)},_file:function(a){return d("file:"),this.dispatch([a.name,a.size,a.type,a.lastModfied])},_blob:function(){if(a.ignoreUnknown)return d("[blob]");throw Error('Hashing Blob objects is currently not supported\n(see https://github.com/puleos/object-hash/issues/26)\nUse "options.replacer" or "options.ignoreUnknown"\n')},_domwindow:function(){return d("domwindow")},_bigint:function(a){return d("bigint:"+a.toString())},_process:function(){return d("process")},_timer:function(){return d("timer")},_pipe:function(){return d("pipe")},_tcp:function(){return d("tcp")},_udp:function(){return d("udp")},_tty:function(){return d("tty")},_statwatcher:function(){return d("statwatcher")},_securecontext:function(){return d("securecontext")},_connection:function(){return d("connection")},_zlib:function(){return d("zlib")},_context:function(){return d("context")},_nodescript:function(){return d("nodescript")},_httpparser:function(){return d("httpparser")},_dataview:function(){return d("dataview")},_signal:function(){return d("signal")},_fsevent:function(){return d("fsevent")},_tlswrap:function(){return d("tlswrap")}}}function k(){return{buf:"",write:function(a){this.buf+=a},end:function(a){this.buf+=a},read:function(){return this.buf}}}b.writeToStream=function(a,b,c){return void 0===c&&(c=b,b={}),j(b=h(a,b),c).dispatch(a)}},8535:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938);b.default=function(a,b,c,e,f){let g;if(void 0!==f.crit&&void 0===e.crit)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!e||void 0===e.crit)return new Set;if(!Array.isArray(e.crit)||0===e.crit.length||e.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let h of(g=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,e.crit)){if(!g.has(h))throw new d.JOSENotSupported(`Extension Header Parameter "${h}" is not recognized`);if(void 0===f[h])throw new a(`Extension Header Parameter "${h}" is missing`);if(g.get(h)&&void 0===e[h])throw new a(`Extension Header Parameter "${h}" MUST be integrity protected`)}return new Set(e.crit)}},8557:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){var b;let{csrfToken:c,providers:d,callbackUrl:h,theme:i,email:j,error:k}=a,l=d.filter(a=>"oauth"===a.type||"email"===a.type||"credentials"===a.type&&!!a.credentials);"undefined"!=typeof document&&i.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText),"undefined"!=typeof document&&i.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor);let m={Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallback:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page.",default:"Unable to sign in."},n=k&&(null!=(b=m[k])?b:m.default),o="https://authjs.dev/img/providers";return(0,e.h)("div",{className:"signin"},i.brandColor&&(0,e.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${i.brandColor}
        }
      `}}),i.buttonText&&(0,e.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),(0,e.h)("div",{className:"card"},i.logo&&(0,e.h)("img",{src:i.logo,alt:"Logo",className:"logo"}),n&&(0,e.h)("div",{className:"error"},(0,e.h)("p",null,n)),l.map((a,b)=>{let d,i,k,m,n,p;if("oauth"===a.type){var q;({bg:d="",text:i="",logo:k="",bgDark:n=d,textDark:p=i,logoDark:m=""}=null!=(q=a.style)?q:{}),k=k.startsWith("/")?`${o}${k}`:k,(m=m.startsWith("/")?`${o}${m}`:m||k)||(m=k)}return(0,e.h)("div",{key:a.id,className:"provider"},"oauth"===a.type&&(0,e.h)("form",{action:a.signinUrl,method:"POST"},(0,e.h)("input",{type:"hidden",name:"csrfToken",value:c}),h&&(0,e.h)("input",{type:"hidden",name:"callbackUrl",value:h}),(0,e.h)("button",{type:"submit",className:"button",style:{"--provider-bg":d,"--provider-dark-bg":n,"--provider-color":i,"--provider-dark-color":p,"--provider-bg-hover":g(d,.8),"--provider-dark-bg-hover":g(n,.8)}},k&&(0,e.h)("img",{loading:"lazy",height:24,width:24,id:"provider-logo",src:`${k.startsWith("/")?o:""}${k}`}),m&&(0,e.h)("img",{loading:"lazy",height:24,width:24,id:"provider-logo-dark",src:`${k.startsWith("/")?o:""}${m}`}),(0,e.h)("span",null,"Sign in with ",a.name))),("email"===a.type||"credentials"===a.type)&&b>0&&"email"!==l[b-1].type&&"credentials"!==l[b-1].type&&(0,e.h)("hr",null),"email"===a.type&&(0,e.h)("form",{action:a.signinUrl,method:"POST"},(0,e.h)("input",{type:"hidden",name:"csrfToken",value:c}),(0,e.h)("label",{className:"section-header",htmlFor:`input-email-for-${a.id}-provider`},"Email"),(0,e.h)("input",{id:`input-email-for-${a.id}-provider`,autoFocus:!0,type:"email",name:"email",value:j,placeholder:"<EMAIL>",required:!0}),(0,e.h)("button",{id:"submitButton",type:"submit"},"Sign in with ",a.name)),"credentials"===a.type&&(0,e.h)("form",{action:a.callbackUrl,method:"POST"},(0,e.h)("input",{type:"hidden",name:"csrfToken",value:c}),Object.keys(a.credentials).map(b=>{var c,d,g;return(0,e.h)("div",{key:`input-group-${a.id}`},(0,e.h)("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`},null!=(c=a.credentials[b].label)?c:b),(0,e.h)("input",(0,f.default)({name:b,id:`input-${b}-for-${a.id}-provider`,type:null!=(d=a.credentials[b].type)?d:"text",placeholder:null!=(g=a.credentials[b].placeholder)?g:""},a.credentials[b])))}),(0,e.h)("button",{type:"submit"},"Sign in with ",a.name)),("email"===a.type||"credentials"===a.type)&&b+1<l.length&&(0,e.h)("hr",null))})))};var e=c(1441),f=d(c(17696));function g(a,b=1){if(!a)return;3===(a=a.replace(/^#/,"")).length&&(a=a[0]+a[0]+a[1]+a[1]+a[2]+a[2]);let c=parseInt(a,16);return b=Math.min(Math.max(b,0),1),`rgba(${c>>16&255}, ${c>>8&255}, ${255&c}, ${b})`}},9168:a=>{a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports},9534:(a,b,c)=>{"use strict";let d;Object.defineProperty(b,"__esModule",{value:!0});let e=c(55511),f=c(28354),g=c(63898),h=c(42567),i=c(92562),j=c(15055),k=c(80028);d=e.verify.length>4&&k.oneShotCallback?(0,f.promisify)(e.verify):e.verify,b.default=async(a,b,c,f)=>{let k=(0,j.default)(a,b,"verify");if(a.startsWith("HS")){let b=await (0,i.default)(a,k,f);try{return e.timingSafeEqual(c,b)}catch{return!1}}let l=(0,g.default)(a),m=(0,h.default)(a,k);try{return await d(l,f,m,c)}catch{return!1}}},9545:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.openidClient=e;var d=c(12107);async function e(a){let b,c=a.provider;if(c.httpOptions&&d.custom.setHttpOptionsDefaults(c.httpOptions),c.wellKnown)b=await d.Issuer.discover(c.wellKnown);else{var e,f,g;b=new d.Issuer({issuer:c.issuer,authorization_endpoint:null==(e=c.authorization)?void 0:e.url,token_endpoint:null==(f=c.token)?void 0:f.url,userinfo_endpoint:null==(g=c.userinfo)?void 0:g.url,jwks_uri:c.jwks_endpoint})}let h=new b.Client({client_id:c.clientId,client_secret:c.clientSecret,redirect_uris:[c.callbackUrl],...c.client},c.jwks);return h[d.custom.clock_tolerance]=10,h}},9552:(a,b,c)=>{let d,e=c(12412),f=c(11723),g=c(81630),h=c(55591),{once:i}=c(94735),{URL:j}=c(79551),k=c(42142),l=c(63667),{RPError:m}=c(61408),n=c(24740),{deep:o}=c(72115),{HTTP_OPTIONS:p}=c(43325),q=/^[\x21\x23-\x5B\x5D-\x7E]+$/,r=["agent","ca","cert","crl","headers","key","lookup","passphrase","pfx","timeout"],s=(a,b)=>{d=o({},a.length?n(b,...a):b,d)};function t(a,b,c){c&&(a.removeHeader("content-type"),a.setHeader("content-type",c)),b&&(a.removeHeader("content-length"),a.setHeader("content-length",Buffer.byteLength(b)),a.write(b)),a.end()}s([],{headers:{"User-Agent":`${l.name}/${l.version} (${l.homepage})`,"Accept-Encoding":"identity"},timeout:3500});let u=new k({max:100});a.exports=async function(a,{accessToken:b,mTLS:c=!1,DPoP:k}={}){let l,s,v,w,x,y,z;try{l=new j(a.url),delete a.url,e(/^(https?:)$/.test(l.protocol))}catch(a){throw TypeError("only valid absolute URLs can be requested")}let A=this[p],B=a,C=`${l.origin}${l.pathname}`;if(k&&"dpopProof"in this&&(B.headers=B.headers||{},B.headers.DPoP=await this.dpopProof({htu:`${l.origin}${l.pathname}`,htm:a.method||"GET",nonce:u.get(C)},k,b)),A&&(s=n(A.call(this,l,o({},B,d)),...r)),B=o({},s,B,d),c&&!B.pfx&&!(B.key&&B.cert))throw TypeError("mutual-TLS certificate and key not set");if(B.searchParams)for(let[a,b]of Object.entries(B.searchParams))l.searchParams.delete(a),l.searchParams.set(a,b);for(let[a,b]of({form:w,responseType:v,json:x,body:y,...B}=B,Object.entries(B.headers||{})))void 0===b&&delete B.headers[a];let D=("https:"===l.protocol?h.request:g.request)(l.href,B);return(async()=>{if(x?t(D,JSON.stringify(x),"application/json"):w?t(D,f.stringify(w),"application/x-www-form-urlencoded"):y?t(D,y):t(D),[z]=await Promise.race([i(D,"response"),i(D,"timeout")]),!z)throw D.destroy(),new m(`outgoing request timed out after ${B.timeout}ms`);let a=[];for await(let b of z)a.push(b);if(a.length)switch(v){case"json":Object.defineProperty(z,"body",{get(){let b=Buffer.concat(a);try{b=JSON.parse(b)}catch(a){throw Object.defineProperty(a,"response",{value:z}),a}finally{Object.defineProperty(z,"body",{value:b,configurable:!0})}return b},configurable:!0});break;case void 0:case"buffer":Object.defineProperty(z,"body",{get(){let b=Buffer.concat(a);return Object.defineProperty(z,"body",{value:b,configurable:!0}),b},configurable:!0});break;default:throw TypeError("unsupported responseType request option")}return z})().catch(a=>{throw z&&Object.defineProperty(a,"response",{value:z}),a}).finally(()=>{let a=z&&z.headers["dpop-nonce"];a&&q.test(a)&&u.set(C,a)})},a.exports.setDefaults=s.bind(void 0,r)},9684:(a,b,c)=>{var d=c(96376),e=c(30994);a.exports=function(a,b,c){if(d())return Reflect.construct.apply(null,arguments);var f=[null];f.push.apply(f,b);var g=new(a.bind.apply(a,f));return c&&e(g,c.prototype),g},a.exports.__esModule=!0,a.exports.default=a.exports},10028:(a,b,c)=>{a.exports=new(c(42142))({max:100})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11714:(a,b)=>{"use strict";async function c({email:a,adapter:b}){let{getUserByEmail:c}=b,d=a?await c(a):null;return d||{id:a,email:a,emailVerified:null}}Object.defineProperty(b,"__esModule",{value:!0}),b.default=c},11723:a=>{"use strict";a.exports=require("querystring")},11819:(a,b,c)=>{b.OAuth=c(28328).OAuth,b.OAuthEcho=c(28328).OAuthEcho,b.OAuth2=c(36720).OAuth2},12107:(a,b,c)=>{let d=c(23884),{OPError:e,RPError:f}=c(61408),g=c(58513),h=c(24453),{CLOCK_TOLERANCE:i,HTTP_OPTIONS:j}=c(43325),k=c(35571),{setDefaults:l}=c(9552);a.exports={Issuer:d,Strategy:g,TokenSet:h,errors:{OPError:e,RPError:f},custom:{setHttpOptionsDefaults:l,http_options:j,clock_tolerance:i},generators:k}},12269:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0})},12412:a=>{"use strict";a.exports=require("assert")},12909:(a,b,c)=>{"use strict";c.d(b,{Nh:()=>j,ht:()=>m,kg:()=>l});var d=c(16467),e=c(36344),f=c(66699),g=c(13581),h=c(85663),i=c(94747);let j={adapter:(0,d.y)(i.zR),providers:[(0,e.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}}),(0,f.A)({clientId:process.env.FACEBOOK_CLIENT_ID,clientSecret:process.env.FACEBOOK_CLIENT_SECRET}),(0,g.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(a){if(!a?.email||!a?.password)throw Error("Email and password are required");let b=await i.zR.user.findUnique({where:{email:a.email}});if(!b||!b.password||!await h.Ay.compare(a.password,b.password))throw Error("Invalid email or password");return{id:b.id,email:b.email,name:b.name,avatar:b.avatar,role:b.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:a,user:b,account:c})=>(b&&(a.role=b.role,a.avatar=b.avatar),c&&(a.accessToken=c.access_token),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub,a.user.role=b.role,a.user.avatar=b.avatar,a.accessToken=b.accessToken),a),async signIn({user:a,account:b,profile:c}){if(b?.provider==="google"||b?.provider==="facebook")try{await i.zR.user.findUnique({where:{email:a.email}})||await i.zR.user.create({data:{email:a.email,name:a.name,avatar:a.image,emailVerified:new Date,role:"USER"}})}catch(a){return console.error("Error during social sign in:",a),!1}return!0},redirect:async({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b},events:{async signIn({user:a,account:b,profile:c,isNewUser:d}){console.log(`User ${a.email} signed in with ${b?.provider}`),a.id&&await i.zR.notification.create({data:{userId:a.id,type:"SYSTEM",title:"Welcome back!",message:"You have successfully signed in to your account.",data:{provider:b?.provider,timestamp:new Date().toISOString()}}}).catch(console.error)},async signOut({session:a,token:b}){console.log("User signed out")},async createUser({user:a}){console.log(`New user created: ${a.email}`),a.id&&await i.zR.notification.create({data:{userId:a.id,type:"SYSTEM",title:"Welcome to AIDEVCOMMERCE!",message:"Thank you for joining our platform. Explore our products and services.",data:{isWelcome:!0,timestamp:new Date().toISOString()}}}).catch(console.error)}},debug:!1};async function k(a){return await h.Ay.hash(a,12)}async function l(a){let b=await k(a.password);return await i.zR.user.create({data:{email:a.email,name:a.name,password:b,phone:a.phone,role:"USER"}})}async function m(a){return await i.zR.user.findUnique({where:{email:a},include:{addresses:!0,orders:{include:{items:{include:{product:!0,variant:!0}}}},wishlistItems:{include:{product:!0,variant:!0}},reviews:{include:{product:!0}},notifications:{orderBy:{createdAt:"desc"}}}})}},13668:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.generateKeyPair=void 0;let d=c(51688);b.generateKeyPair=async function(a,b){return(0,d.generateKeyPair)(a,b)}},14988:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.FlattenedEncrypt=b.unprotected=void 0;let d=c(68327),e=c(22122),f=c(66318),g=c(35749),h=c(22599),i=c(99938),j=c(65597),k=c(2594),l=c(8535);b.unprotected=Symbol();class m{constructor(a){if(!(a instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=a}setKeyManagementParameters(a){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=a,this}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setSharedUnprotectedHeader(a){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=a,this}setUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}setAdditionalAuthenticatedData(a){return this._aad=a,this}setContentEncryptionKey(a){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=a,this}setInitializationVector(a){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=a,this}async encrypt(a,c){let m,n,o,p,q,r,s;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new i.JWEInvalid("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!(0,j.default)(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new i.JWEInvalid("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let t={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if((0,l.default)(i.JWEInvalid,new Map,null==c?void 0:c.crit,this._protectedHeader,t),void 0!==t.zip){if(!this._protectedHeader||!this._protectedHeader.zip)throw new i.JWEInvalid('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==t.zip)throw new i.JOSENotSupported('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:u,enc:v}=t;if("string"!=typeof u||!u)throw new i.JWEInvalid('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof v||!v)throw new i.JWEInvalid('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if("dir"===u){if(this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Encryption")}else if("ECDH-ES"===u&&this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Key Agreement");{let d;({cek:n,encryptedKey:m,parameters:d}=await (0,h.default)(u,v,a,this._cek,this._keyManagementParameters)),d&&(c&&b.unprotected in c?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...d}:this.setUnprotectedHeader(d):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...d}:this.setProtectedHeader(d))}if(this._iv||(this._iv=(0,g.default)(v)),p=this._protectedHeader?k.encoder.encode((0,d.encode)(JSON.stringify(this._protectedHeader))):k.encoder.encode(""),this._aad?(q=(0,d.encode)(this._aad),o=(0,k.concat)(p,k.encoder.encode("."),k.encoder.encode(q))):o=p,"DEF"===t.zip){let a=await ((null==c?void 0:c.deflateRaw)||f.deflate)(this._plaintext);({ciphertext:r,tag:s}=await (0,e.default)(v,a,n,this._iv,o))}else({ciphertext:r,tag:s}=await (0,e.default)(v,this._plaintext,n,this._iv,o));let w={ciphertext:(0,d.encode)(r),iv:(0,d.encode)(this._iv),tag:(0,d.encode)(s)};return m&&(w.encrypted_key=(0,d.encode)(m)),q&&(w.aad=q),this._protectedHeader&&(w.protected=k.decoder.decode(p)),this._sharedUnprotectedHeader&&(w.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(w.header=this._unprotectedHeader),w}}b.FlattenedEncrypt=m},15055:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(17996),f=c(50003),g=c(78680),h=c(37265);b.default=function(a,b,c){if(b instanceof Uint8Array){if(!a.startsWith("HS"))throw TypeError((0,g.default)(b,...h.types));return(0,d.createSecretKey)(b)}if(b instanceof d.KeyObject)return b;if((0,e.isCryptoKey)(b))return(0,f.checkSigCryptoKey)(b,a,c),d.KeyObject.from(b);throw TypeError((0,g.default)(b,...h.types,"Uint8Array"))}},15912:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0});var e={encode:!0,decode:!0,getToken:!0};b.decode=l,b.encode=k,b.getToken=m;var f=c(37544),g=d(c(94387)),h=c(72921),i=c(99632),j=c(90253);async function k(a){let{token:b={},secret:c,maxAge:d=2592e3,salt:e=""}=a,g=await n(c,e);return await new f.EncryptJWT(b).setProtectedHeader({alg:"dir",enc:"A256GCM"}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+d).setJti((0,h.v4)()).encrypt(g)}async function l(a){let{token:b,secret:c,salt:d=""}=a;if(!b)return null;let e=await n(c,d),{payload:g}=await (0,f.jwtDecrypt)(b,e,{clockTolerance:15});return g}async function m(a){var b,c,d,e;let{req:f,secureCookie:g=null!=(b=null==(c=process.env.NEXTAUTH_URL)?void 0:c.startsWith("https://"))?b:!!process.env.VERCEL,cookieName:h=g?"__Secure-next-auth.session-token":"next-auth.session-token",raw:j,decode:k=l,logger:m=console,secret:n=null!=(d=process.env.NEXTAUTH_SECRET)?d:process.env.AUTH_SECRET}=a;if(!f)throw Error("Must pass `req` to JWT getToken()");let o=new i.SessionStore({name:h,options:{secure:g}},{cookies:f.cookies,headers:f.headers},m).value,p=f.headers instanceof Headers?f.headers.get("authorization"):null==(e=f.headers)?void 0:e.authorization;if(o||(null==p?void 0:p.split(" ")[0])!=="Bearer"||(o=decodeURIComponent(p.split(" ")[1])),!o)return null;if(j)return o;try{return await k({token:o,secret:n})}catch(a){return null}}async function n(a,b){return await (0,g.default)("sha256",a,b,`NextAuth.js Generated Encryption Key${b?` (${b})`:""}`,32)}Object.keys(j).forEach(function(a){!("default"===a||"__esModule"===a||Object.prototype.hasOwnProperty.call(e,a))&&(a in b&&b[a]===j[a]||Object.defineProperty(b,a,{enumerable:!0,get:function(){return j[a]}}))})},16156:a=>{a.exports=()=>Math.floor(Date.now()/1e3)},16851:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){var b;let c=new URL("http://localhost:3000/api/auth");a&&!a.startsWith("http")&&(a=`https://${a}`);let d=new URL(null!=(b=a)?b:c),e=("/"===d.pathname?c.pathname:d.pathname).replace(/\/$/,""),f=`${d.origin}${e}`;return{origin:d.origin,host:d.host,path:e,base:f,toString:()=>f}}},16974:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){let{url:b,theme:c}=a;return(0,d.h)("div",{className:"verify-request"},c.brandColor&&(0,d.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${c.brandColor}
        }
      `}}),(0,d.h)("div",{className:"card"},c.logo&&(0,d.h)("img",{src:c.logo,alt:"Logo",className:"logo"}),(0,d.h)("h1",null,"Check your email"),(0,d.h)("p",null,"A sign in link has been sent to your email address."),(0,d.h)("p",null,(0,d.h)("a",{className:"site",href:b.origin},b.host))))};var d=c(1441)},17696:a=>{function b(){return a.exports=b=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a},a.exports.__esModule=!0,a.exports.default=a.exports,b.apply(null,arguments)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},17996:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.isCryptoKey=void 0;let d=c(55511),e=c(28354);b.default=d.webcrypto,b.isCryptoKey=e.types.isCryptoKey?a=>e.types.isCryptoKey(a):a=>!1},18040:a=>{function b(c,d,e,f){var g=Object.defineProperty;try{g({},"",{})}catch(a){g=0}a.exports=b=function(a,c,d,e){function f(c,d){b(a,c,function(a){return this._invoke(c,d,a)})}c?g?g(a,c,{value:d,enumerable:!e,configurable:!e,writable:!e}):a[c]=d:(f("next",0),f("throw",1),f("return",2))},a.exports.__esModule=!0,a.exports.default=a.exports,b(c,d,e,f)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},18490:(a,b,c)=>{var d=c(99607)();a.exports=d;try{regeneratorRuntime=d}catch(a){"object"==typeof globalThis?globalThis.regeneratorRuntime=d:Function("r","regeneratorRuntime = r")(d)}},18652:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.calculateJwkThumbprintUri=b.calculateJwkThumbprint=void 0;let d=c(37375),e=c(68327),f=c(99938),g=c(2594),h=c(25110),i=(a,b)=>{if("string"!=typeof a||!a)throw new f.JWKInvalid(`${b} missing or invalid`)};async function j(a,b){let c;if(!(0,h.default)(a))throw TypeError("JWK must be an object");if(null!=b||(b="sha256"),"sha256"!==b&&"sha384"!==b&&"sha512"!==b)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(a.kty){case"EC":i(a.crv,'"crv" (Curve) Parameter'),i(a.x,'"x" (X Coordinate) Parameter'),i(a.y,'"y" (Y Coordinate) Parameter'),c={crv:a.crv,kty:a.kty,x:a.x,y:a.y};break;case"OKP":i(a.crv,'"crv" (Subtype of Key Pair) Parameter'),i(a.x,'"x" (Public Key) Parameter'),c={crv:a.crv,kty:a.kty,x:a.x};break;case"RSA":i(a.e,'"e" (Exponent) Parameter'),i(a.n,'"n" (Modulus) Parameter'),c={e:a.e,kty:a.kty,n:a.n};break;case"oct":i(a.k,'"k" (Key Value) Parameter'),c={k:a.k,kty:a.kty};break;default:throw new f.JOSENotSupported('"kty" (Key Type) Parameter missing or unsupported')}let j=g.encoder.encode(JSON.stringify(c));return(0,e.encode)(await (0,d.default)(b,j))}b.calculateJwkThumbprint=j,b.calculateJwkThumbprintUri=async function(a,b){null!=b||(b="sha256");let c=await j(a,b);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${b.slice(-3)}:${c}`}},19854:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d={};Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f.default}});var e=c(12269);Object.keys(e).forEach(function(a){!("default"===a||"__esModule"===a||Object.prototype.hasOwnProperty.call(d,a))&&(a in b&&b[a]===e[a]||Object.defineProperty(b,a,{enumerable:!0,get:function(){return e[a]}}))});var f=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=g(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var f in a)if("default"!==f&&({}).hasOwnProperty.call(a,f)){var h=e?Object.getOwnPropertyDescriptor(a,f):null;h&&(h.get||h.set)?Object.defineProperty(d,f,h):d[f]=a[f]}return d.default=a,c&&c.set(a,d),d}(c(35426));function g(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(g=function(a){return a?c:b})(a)}Object.keys(f).forEach(function(a){!("default"===a||"__esModule"===a||Object.prototype.hasOwnProperty.call(d,a))&&(a in b&&b[a]===f[a]||Object.defineProperty(b,a,{enumerable:!0,get:function(){return f[a]}}))})},20113:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.UnsupportedStrategy=b.UnknownError=b.OAuthCallbackError=b.MissingSecret=b.MissingAuthorize=b.MissingAdapterMethods=b.MissingAdapter=b.MissingAPIRoute=b.InvalidCallbackUrl=b.AccountNotLinkedError=void 0,b.adapterErrorHandler=function(a,b){if(a)return Object.keys(a).reduce(function(c,d){return c[d]=(0,f.default)(e.default.mark(function c(){var f,g,h,i,j,k=arguments;return e.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:for(c.prev=0,g=Array(f=k.length),h=0;h<f;h++)g[h]=k[h];return b.debug("adapter_".concat(d),{args:g}),i=a[d],c.next=6,i.apply(void 0,g);case 6:return c.abrupt("return",c.sent);case 9:throw c.prev=9,c.t0=c.catch(0),b.error("adapter_error_".concat(d),c.t0),(j=new o(c.t0)).name="".concat(q(d),"Error"),j;case 15:case"end":return c.stop()}},c,null,[[0,9]])})),c},{})},b.capitalize=q,b.eventsErrorHandler=function(a,b){return Object.keys(a).reduce(function(c,d){return c[d]=(0,f.default)(e.default.mark(function c(){var f,g=arguments;return e.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.prev=0,f=a[d],c.next=4,f.apply(void 0,g);case 4:return c.abrupt("return",c.sent);case 7:c.prev=7,c.t0=c.catch(0),b.error("".concat(p(d),"_EVENT_ERROR"),c.t0);case 10:case"end":return c.stop()}},c,null,[[0,7]])})),c},{})},b.upperSnake=p;var e=d(c(18490)),f=d(c(48807)),g=d(c(3399)),h=d(c(55437)),i=d(c(24365)),j=d(c(75722)),k=d(c(57502)),l=d(c(70997));function m(a,b,c){return b=(0,k.default)(b),(0,j.default)(a,n()?Reflect.construct(b,c||[],(0,k.default)(a).constructor):b.apply(a,c))}function n(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(n=function(){return!!a})()}var o=b.UnknownError=function(a){function b(a){var c,d;return(0,h.default)(this,b),(d=m(this,b,[null!=(c=null==a?void 0:a.message)?c:a])).name="UnknownError",d.code=a.code,a instanceof Error&&(d.stack=a.stack),d}return(0,l.default)(b,a),(0,i.default)(b,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,d(c(767)).default)(Error));function p(a){return a.replace(/([A-Z])/g,"_$1").toUpperCase()}function q(a){return"".concat(a[0].toUpperCase()).concat(a.slice(1))}b.OAuthCallbackError=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","OAuthCallbackError"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.AccountNotLinkedError=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","AccountNotLinkedError"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.MissingAPIRoute=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","MissingAPIRouteError"),(0,g.default)(a,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.MissingSecret=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","MissingSecretError"),(0,g.default)(a,"code","NO_SECRET"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.MissingAuthorize=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","MissingAuthorizeError"),(0,g.default)(a,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.MissingAdapter=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","MissingAdapterError"),(0,g.default)(a,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.MissingAdapterMethods=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","MissingAdapterMethodsError"),(0,g.default)(a,"code","MISSING_ADAPTER_METHODS_ERROR"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.UnsupportedStrategy=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","UnsupportedStrategyError"),(0,g.default)(a,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o),b.InvalidCallbackUrl=function(a){function b(){var a;(0,h.default)(this,b);for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];return a=m(this,b,[].concat(d)),(0,g.default)(a,"name","InvalidCallbackUrl"),(0,g.default)(a,"code","INVALID_CALLBACK_URL_ERROR"),a}return(0,l.default)(b,a),(0,i.default)(b)}(o)},21606:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=c(55511).timingSafeEqual},22122:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(42891),f=c(51800),g=c(2594),h=c(34010),i=c(17996),j=c(50003),k=c(90061),l=c(78680),m=c(99938),n=c(79963),o=c(37265);b.default=(a,b,c,p,q)=>{let r;if((0,i.isCryptoKey)(c))(0,j.checkEncCryptoKey)(c,a,"encrypt"),r=d.KeyObject.from(c);else if(c instanceof Uint8Array||(0,k.default)(c))r=c;else throw TypeError((0,l.default)(c,...o.types,"Uint8Array"));switch((0,f.default)(a,r),(0,e.default)(a,p),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(a,b,c,e,f){let i=parseInt(a.slice(1,4),10);(0,k.default)(c)&&(c=c.export());let j=c.subarray(i>>3),l=c.subarray(0,i>>3),o=`aes-${i}-cbc`;if(!(0,n.default)(o))throw new m.JOSENotSupported(`alg ${a} is not supported by your javascript runtime`);let p=(0,d.createCipheriv)(o,j,e),q=(0,g.concat)(p.update(b),p.final()),r=parseInt(a.slice(-3),10),s=(0,h.default)(f,e,q,r,l,i);return{ciphertext:q,tag:s}}(a,b,r,p,q);case"A128GCM":case"A192GCM":case"A256GCM":return function(a,b,c,e,f){let g=parseInt(a.slice(1,4),10),h=`aes-${g}-gcm`;if(!(0,n.default)(h))throw new m.JOSENotSupported(`alg ${a} is not supported by your javascript runtime`);let i=(0,d.createCipheriv)(h,c,e,{authTagLength:16});f.byteLength&&i.setAAD(f,{plaintextLength:b.length});let j=i.update(b);return i.final(),{ciphertext:j,tag:i.getAuthTag()}}(a,b,r,p,q);default:throw new m.JOSENotSupported("Unsupported JWE Content Encryption Algorithm")}}},22165:a=>{a.exports.isAnEarlyCloseHost=function(a){return a&&a.match(".*google(apis)?.com$")}},22429:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.default=h;var e=d(c(66837)),f=d(c(58985)),g=d(c(11714));async function h(a){let{options:b,query:c,body:d}=a,{url:h,callbacks:i,logger:j,provider:k}=b;if(!k.type)return{status:500,text:`Error: Type not specified for ${k.name}`};if("oauth"===k.type)try{return await (0,e.default)({options:b,query:c})}catch(a){return j.error("SIGNIN_OAUTH_ERROR",{error:a,providerId:k.id}),{redirect:`${h}/error?error=OAuthSignin`}}if("email"===k.type){var l;let a=null==d?void 0:d.email;if(!a)return{redirect:`${h}/error?error=EmailSignin`};let c=null!=(l=k.normalizeIdentifier)?l:a=>{let[b,c]=a.toLowerCase().trim().split("@");return c=c.split(",")[0],`${b}@${c}`};try{a=c(null==d?void 0:d.email)}catch(a){return j.error("SIGNIN_EMAIL_ERROR",{error:a,providerId:k.id}),{redirect:`${h}/error?error=EmailSignin`}}let e=await (0,g.default)({email:a,adapter:b.adapter}),m={providerAccountId:a,userId:a,type:"email",provider:k.id};try{let a=await i.signIn({user:e,account:m,email:{verificationRequest:!0}});if(!a)return{redirect:`${h}/error?error=AccessDenied`};if("string"==typeof a)return{redirect:a}}catch(a){return{redirect:`${h}/error?${new URLSearchParams({error:a})}`}}try{return{redirect:await (0,f.default)(a,b)}}catch(a){return j.error("SIGNIN_EMAIL_ERROR",{error:a,providerId:k.id}),{redirect:`${h}/error?error=EmailSignin`}}}return{redirect:`${h}/signin`}}},22599:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(88062),e=c(52131),f=c(62197),g=c(56913),h=c(68327),i=c(61689),j=c(99938),k=c(65764),l=c(80335),m=c(97528);b.default=async function(a,b,c,n,o={}){let p,q,r;switch((0,l.default)(a,c,"encrypt"),a){case"dir":r=c;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!e.ecdhAllowed(c))throw new j.JOSENotSupported("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:f,apv:g}=o,{epk:l}=o;l||(l=(await e.generateEpk(c)).privateKey);let{x:m,y:s,crv:t,kty:u}=await (0,k.exportJWK)(l),v=await e.deriveKey(c,l,"ECDH-ES"===a?b:a,"ECDH-ES"===a?(0,i.bitLength)(b):parseInt(a.slice(-5,-2),10),f,g);if(q={epk:{x:m,crv:t,kty:u}},"EC"===u&&(q.epk.y=s),f&&(q.apu=(0,h.encode)(f)),g&&(q.apv=(0,h.encode)(g)),"ECDH-ES"===a){r=v;break}r=n||(0,i.default)(b);let w=a.slice(-6);p=await (0,d.wrap)(w,v,r);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":r=n||(0,i.default)(b),p=await (0,g.encrypt)(a,c,r);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{r=n||(0,i.default)(b);let{p2c:d,p2s:e}=o;({encryptedKey:p,...q}=await (0,f.encrypt)(a,c,r,d,e));break}case"A128KW":case"A192KW":case"A256KW":r=n||(0,i.default)(b),p=await (0,d.wrap)(a,c,r);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{r=n||(0,i.default)(b);let{iv:d}=o;({encryptedKey:p,...q}=await (0,m.wrap)(a,c,r,d));break}default:throw new j.JOSENotSupported('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:r,encryptedKey:p,parameters:q}}},23884:(a,b,c)=>{let{inspect:d}=c(28354),e=c(79551),{RPError:f}=c(61408),g=c(83872),h=c(10028),i=c(48160),j=c(64150),k=c(9552),l=c(93929),{keystore:m}=c(73602),n=["https://login.microsoftonline.com/common/.well-known/openid-configuration","https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration","https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration","https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration"],o=Symbol(),p={claim_types_supported:["normal"],claims_parameter_supported:!1,grant_types_supported:["authorization_code","implicit"],request_parameter_supported:!1,request_uri_parameter_supported:!0,require_request_uri_registration:!1,response_modes_supported:["query","fragment"],token_endpoint_auth_methods_supported:["client_secret_basic"]};class q{#a;constructor(a={}){let b=a[o];delete a[o],["introspection","revocation"].forEach(b=>{a[`${b}_endpoint`]&&void 0===a[`${b}_endpoint_auth_methods_supported`]&&void 0===a[`${b}_endpoint_auth_signing_alg_values_supported`]&&(a.token_endpoint_auth_methods_supported&&(a[`${b}_endpoint_auth_methods_supported`]=a.token_endpoint_auth_methods_supported),a.token_endpoint_auth_signing_alg_values_supported&&(a[`${b}_endpoint_auth_signing_alg_values_supported`]=a.token_endpoint_auth_signing_alg_values_supported))}),this.#a=new Map,Object.entries(a).forEach(([a,b])=>{this.#a.set(a,b),this[a]||Object.defineProperty(this,a,{get(){return this.#a.get(a)},enumerable:!0})}),h.set(this.issuer,this);let c=g(this,b);Object.defineProperties(this,{Client:{value:c,enumerable:!0},FAPI1Client:{value:class extends c{},enumerable:!0},FAPI2Client:{value:class extends c{},enumerable:!0}})}get metadata(){return l(Object.fromEntries(this.#a.entries()))}static async webfinger(a){let b=j(a),{host:c}=e.parse(b),d=`https://${c}/.well-known/webfinger`,g=i(await k.call(this,{method:"GET",url:d,responseType:"json",searchParams:{resource:b,rel:"http://openid.net/specs/connect/1.0/issuer"},headers:{Accept:"application/json"}})),l=Array.isArray(g.links)&&g.links.find(a=>"object"==typeof a&&"http://openid.net/specs/connect/1.0/issuer"===a.rel&&a.href);if(!l)throw new f({message:"no issuer found in webfinger response",body:g});if("string"!=typeof l.href||!l.href.startsWith("https://"))throw new f({printf:["invalid issuer location %s",l.href],body:g});let m=l.href;if(h.has(m))return h.get(m);let n=await this.discover(m);if(n.issuer!==m)throw h.del(n.issuer),new f("discovered issuer mismatch, expected %s, got: %s",m,n.issuer);return n}static async discover(a){let b=function(a){let b=e.parse(a);if(b.pathname.includes("/.well-known/"))return a;{let a;return a=b.pathname.endsWith("/")?`${b.pathname}.well-known/openid-configuration`:`${b.pathname}/.well-known/openid-configuration`,e.format({...b,pathname:a})}}(a),c=i(await k.call(this,{method:"GET",responseType:"json",url:b,headers:{Accept:"application/json"}}));return new q({...p,...c,[o]:!!n.find(a=>b.startsWith(a))})}async reloadJwksUri(){await m.call(this,!0)}[d.custom](){return`${this.constructor.name} ${d(this.metadata,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}}a.exports=q},24365:(a,b,c)=>{var d=c(3038);function e(a,b){for(var c=0;c<b.length;c++){var e=b[c];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,d(e.key),e)}}a.exports=function(a,b,c){return b&&e(a.prototype,b),c&&e(a,c),Object.defineProperty(a,"prototype",{writable:!1}),a},a.exports.__esModule=!0,a.exports.default=a.exports},24453:(a,b,c)=>{let d=c(70885),e=c(16156);class f{constructor(a){Object.assign(this,a);let{constructor:b,...c}=Object.getOwnPropertyDescriptors(this.constructor.prototype);Object.defineProperties(this,c)}set expires_in(a){this.expires_at=e()+Number(a)}get expires_in(){return Math.max.apply(null,[this.expires_at-e(),0])}expired(){return 0===this.expires_in}claims(){if(!this.id_token)throw TypeError("id_token not present in TokenSet");return JSON.parse(d.decode(this.id_token.split(".")[1]))}}a.exports=f},24575:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.generalVerify=void 0;let d=c(71746),e=c(99938),f=c(25110);b.generalVerify=async function(a,b,c){if(!(0,f.default)(a))throw new e.JWSInvalid("General JWS must be an object");if(!Array.isArray(a.signatures)||!a.signatures.every(f.default))throw new e.JWSInvalid("JWS Signatures missing or incorrect type");for(let e of a.signatures)try{return await (0,d.flattenedVerify)({header:e.header,payload:a.payload,protected:e.protected,signature:e.signature},b,c)}catch{}throw new e.JWSSignatureVerificationFailed}},24740:a=>{a.exports=function(a,...b){let c={};for(let d of b)void 0!==a[d]&&(c[d]=a[d]);return c}},24956:a=>{function b(c){return a.exports=b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},a.exports.__esModule=!0,a.exports.default=a.exports,b(c)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},25110:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){if("object"!=typeof a||null===a||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},25538:(a,b)=>{"use strict";b.parse=function(a,b){if("string"!=typeof a)throw TypeError("argument str must be a string");var c={},e=a.length;if(e<2)return c;var f=b&&b.decode||k,g=0,h=0,l=0;do{if(-1===(h=a.indexOf("=",g)))break;if(-1===(l=a.indexOf(";",g)))l=e;else if(h>l){g=a.lastIndexOf(";",h-1)+1;continue}var m=i(a,g,h),n=j(a,h,m),o=a.slice(m,n);if(!d.call(c,o)){var p=i(a,h+1,l),q=j(a,l,p);34===a.charCodeAt(p)&&34===a.charCodeAt(q-1)&&(p++,q--);var r=a.slice(p,q);c[o]=function(a,b){try{return b(a)}catch(b){return a}}(r,f)}g=l+1}while(g<e);return c},b.serialize=function(a,b,d){var i=d&&d.encode||encodeURIComponent;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!e.test(a))throw TypeError("argument name is invalid");var j=i(b);if(!f.test(j))throw TypeError("argument val is invalid");var k=a+"="+j;if(!d)return k;if(null!=d.maxAge){var l=Math.floor(d.maxAge);if(!isFinite(l))throw TypeError("option maxAge is invalid");k+="; Max-Age="+l}if(d.domain){if(!g.test(d.domain))throw TypeError("option domain is invalid");k+="; Domain="+d.domain}if(d.path){if(!h.test(d.path))throw TypeError("option path is invalid");k+="; Path="+d.path}if(d.expires){var m,n=d.expires;if(m=n,"[object Date]"!==c.call(m)||isNaN(n.valueOf()))throw TypeError("option expires is invalid");k+="; Expires="+n.toUTCString()}if(d.httpOnly&&(k+="; HttpOnly"),d.secure&&(k+="; Secure"),d.partitioned&&(k+="; Partitioned"),d.priority)switch("string"==typeof d.priority?d.priority.toLowerCase():d.priority){case"low":k+="; Priority=Low";break;case"medium":k+="; Priority=Medium";break;case"high":k+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(d.sameSite)switch("string"==typeof d.sameSite?d.sameSite.toLowerCase():d.sameSite){case!0:case"strict":k+="; SameSite=Strict";break;case"lax":k+="; SameSite=Lax";break;case"none":k+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return k};var c=Object.prototype.toString,d=Object.prototype.hasOwnProperty,e=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,f=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,g=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,h=/^[\u0020-\u003A\u003D-\u007E]*$/;function i(a,b,c){do{var d=a.charCodeAt(b);if(32!==d&&9!==d)return b}while(++b<c);return c}function j(a,b,c){for(;b>c;){var d=a.charCodeAt(--b);if(32!==d&&9!==d)return b+1}return c}function k(a){return -1!==a.indexOf("%")?decodeURIComponent(a):a}},27969:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){var b;let{url:c,error:e="default",theme:f}=a,g=`${c}/signin`,h={default:{status:200,heading:"Error",message:(0,d.h)("p",null,(0,d.h)("a",{className:"site",href:null==c?void 0:c.origin},null==c?void 0:c.host))},configuration:{status:500,heading:"Server error",message:(0,d.h)("div",null,(0,d.h)("p",null,"There is a problem with the server configuration."),(0,d.h)("p",null,"Check the server logs for more information."))},accessdenied:{status:403,heading:"Access Denied",message:(0,d.h)("div",null,(0,d.h)("p",null,"You do not have permission to sign in."),(0,d.h)("p",null,(0,d.h)("a",{className:"button",href:g},"Sign in")))},verification:{status:403,heading:"Unable to sign in",message:(0,d.h)("div",null,(0,d.h)("p",null,"The sign in link is no longer valid."),(0,d.h)("p",null,"It may have been used already or it may have expired.")),signin:(0,d.h)("a",{className:"button",href:g},"Sign in")}},{status:i,heading:j,message:k,signin:l}=null!=(b=h[e.toLowerCase()])?b:h.default;return{status:i,html:(0,d.h)("div",{className:"error"},(null==f?void 0:f.brandColor)&&(0,d.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${null==f?void 0:f.brandColor}
        }
      `}}),(0,d.h)("div",{className:"card"},(null==f?void 0:f.logo)&&(0,d.h)("img",{src:f.logo,alt:"Logo",className:"logo"}),(0,d.h)("h1",null,j),(0,d.h)("div",{className:"message"},k),l))}};var d=c(1441)},28247:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.jwtDecrypt=void 0;let d=c(81454),e=c(81822),f=c(99938);b.jwtDecrypt=async function(a,b,c){let g=await (0,d.compactDecrypt)(a,b,c),h=(0,e.default)(g.protectedHeader,g.plaintext,c),{protectedHeader:i}=g;if(void 0!==i.iss&&i.iss!==h.iss)throw new f.JWTClaimValidationFailed('replicated "iss" claim header parameter mismatch',"iss","mismatch");if(void 0!==i.sub&&i.sub!==h.sub)throw new f.JWTClaimValidationFailed('replicated "sub" claim header parameter mismatch',"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(h.aud))throw new f.JWTClaimValidationFailed('replicated "aud" claim header parameter mismatch',"aud","mismatch");let j={payload:h,protectedHeader:i};return"function"==typeof b?{...j,key:g.key}:j}},28328:(a,b,c)=>{var d=c(55511),e=c(6702),f=c(81630),g=c(55591),h=c(79551),i=c(11723),j=c(22165);b.OAuth=function(a,b,c,d,e,f,g,h,i){if(this._isEcho=!1,this._requestUrl=a,this._accessUrl=b,this._consumerKey=c,this._consumerSecret=this._encodeData(d),"RSA-SHA1"==g&&(this._privateKey=d),this._version=e,void 0===f?this._authorize_callback="oob":this._authorize_callback=f,"PLAINTEXT"!=g&&"HMAC-SHA1"!=g&&"RSA-SHA1"!=g)throw Error("Un-supported signature method: "+g);this._signatureMethod=g,this._nonceSize=h||32,this._headers=i||{Accept:"*/*",Connection:"close","User-Agent":"Node authentication"},this._clientOptions=this._defaultClientOptions={requestTokenHttpMethod:"POST",accessTokenHttpMethod:"POST",followRedirects:!0},this._oauthParameterSeperator=","},b.OAuthEcho=function(a,b,c,d,e,f,g,h){if(this._isEcho=!0,this._realm=a,this._verifyCredentials=b,this._consumerKey=c,this._consumerSecret=this._encodeData(d),"RSA-SHA1"==f&&(this._privateKey=d),this._version=e,"PLAINTEXT"!=f&&"HMAC-SHA1"!=f&&"RSA-SHA1"!=f)throw Error("Un-supported signature method: "+f);this._signatureMethod=f,this._nonceSize=g||32,this._headers=h||{Accept:"*/*",Connection:"close","User-Agent":"Node authentication"},this._oauthParameterSeperator=","},b.OAuthEcho.prototype=b.OAuth.prototype,b.OAuth.prototype._getTimestamp=function(){return Math.floor(new Date().getTime()/1e3)},b.OAuth.prototype._encodeData=function(a){return null==a||""==a?"":encodeURIComponent(a).replace(/\!/g,"%21").replace(/\'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")},b.OAuth.prototype._decodeData=function(a){return null!=a&&(a=a.replace(/\+/g," ")),decodeURIComponent(a)},b.OAuth.prototype._getSignature=function(a,b,c,d){var e=this._createSignatureBase(a,b,c);return this._createSignature(e,d)},b.OAuth.prototype._normalizeUrl=function(a){var b=h.parse(a,!0),c="";return b.port&&("http:"==b.protocol&&"80"!=b.port||"https:"==b.protocol&&"443"!=b.port)&&(c=":"+b.port),b.pathname&&""!=b.pathname||(b.pathname="/"),b.protocol+"//"+b.hostname+c+b.pathname},b.OAuth.prototype._isParameterNameAnOAuthParameter=function(a){var b=a.match("^oauth_");return!!b&&"oauth_"===b[0]},b.OAuth.prototype._buildAuthorizationHeaders=function(a){var b="OAuth ";this._isEcho&&(b+='realm="'+this._realm+'",');for(var c=0;c<a.length;c++)this._isParameterNameAnOAuthParameter(a[c][0])&&(b+=""+this._encodeData(a[c][0])+'="'+this._encodeData(a[c][1])+'"'+this._oauthParameterSeperator);return b.substring(0,b.length-this._oauthParameterSeperator.length)},b.OAuth.prototype._makeArrayOfArgumentsHash=function(a){var b=[];for(var c in a)if(a.hasOwnProperty(c)){var d=a[c];if(Array.isArray(d))for(var e=0;e<d.length;e++)b[b.length]=[c,d[e]];else b[b.length]=[c,d]}return b},b.OAuth.prototype._sortRequestParams=function(a){return a.sort(function(a,b){return a[0]==b[0]?a[1]<b[1]?-1:1:a[0]<b[0]?-1:1}),a},b.OAuth.prototype._normaliseRequestParams=function(a){for(var b=this._makeArrayOfArgumentsHash(a),c=0;c<b.length;c++)b[c][0]=this._encodeData(b[c][0]),b[c][1]=this._encodeData(b[c][1]);b=this._sortRequestParams(b);for(var a="",c=0;c<b.length;c++)a+=b[c][0],a+="=",a+=b[c][1],c<b.length-1&&(a+="&");return a},b.OAuth.prototype._createSignatureBase=function(a,b,c){return b=this._encodeData(this._normalizeUrl(b)),c=this._encodeData(c),a.toUpperCase()+"&"+b+"&"+c},b.OAuth.prototype._createSignature=function(a,b){if(void 0===b)var b="";else b=this._encodeData(b);var c=this._consumerSecret+"&"+b,f="";return"PLAINTEXT"==this._signatureMethod?f=c:"RSA-SHA1"==this._signatureMethod?(c=this._privateKey||"",f=d.createSign("RSA-SHA1").update(a).sign(c,"base64")):f=d.Hmac?d.createHmac("sha1",c).update(a).digest("base64"):e.HMACSHA1(c,a),f},b.OAuth.prototype.NONCE_CHARS=["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","0","1","2","3","4","5","6","7","8","9"],b.OAuth.prototype._getNonce=function(a){for(var b,c=[],d=this.NONCE_CHARS,e=d.length,f=0;f<a;f++)b=Math.floor(Math.random()*e),c[f]=d[b];return c.join("")},b.OAuth.prototype._createClient=function(a,b,c,d,e,h){return(h?g:f).request({host:b,port:a,path:d,method:c,headers:e})},b.OAuth.prototype._prepareParameters=function(a,b,c,d,e){var f={oauth_timestamp:this._getTimestamp(),oauth_nonce:this._getNonce(this._nonceSize),oauth_version:this._version,oauth_signature_method:this._signatureMethod,oauth_consumer_key:this._consumerKey};if(a&&(f.oauth_token=a),this._isEcho)k=this._getSignature("GET",this._verifyCredentials,this._normaliseRequestParams(f),b);else{if(e)for(var g in e)e.hasOwnProperty(g)&&(f[g]=e[g]);var j=h.parse(d,!1);if(j.query){var k,l,m=i.parse(j.query);for(var g in m){var n=m[g];if("object"==typeof n)for(l in n)f[g+"["+l+"]"]=n[l];else f[g]=n}}k=this._getSignature(c,d,this._normaliseRequestParams(f),b)}var o=this._sortRequestParams(this._makeArrayOfArgumentsHash(f));return o[o.length]=["oauth_signature",k],o},b.OAuth.prototype._performSecureRequest=function(a,b,c,d,e,f,g,k){var l,m,n=this._prepareParameters(a,b,c,d,e);g||(g="application/x-www-form-urlencoded");var o=h.parse(d,!1);"http:"!=o.protocol||o.port||(o.port=80),"https:"!=o.protocol||o.port||(o.port=443);var p={},q=this._buildAuthorizationHeaders(n);for(var r in this._isEcho?p["X-Verify-Credentials-Authorization"]=q:p.Authorization=q,p.Host=o.host,this._headers)this._headers.hasOwnProperty(r)&&(p[r]=this._headers[r]);for(var r in e)this._isParameterNameAnOAuthParameter(r)&&delete e[r];("POST"==c||"PUT"==c)&&null==f&&null!=e&&(f=i.stringify(e).replace(/\!/g,"%21").replace(/\'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")),f?Buffer.isBuffer(f)?p["Content-length"]=f.length:p["Content-length"]=Buffer.byteLength(f):p["Content-length"]=0,p["Content-Type"]=g,o.pathname&&""!=o.pathname||(o.pathname="/"),l=o.query?o.pathname+"?"+o.query:o.pathname,m="https:"==o.protocol?this._createClient(o.port,o.hostname,c,l,p,!0):this._createClient(o.port,o.hostname,c,l,p);var s=this._clientOptions;if(!k)return("POST"==c||"PUT"==c)&&null!=f&&""!=f&&m.write(f),m;var t="",u=this,v=j.isAnEarlyCloseHost(o.hostname),w=!1,x=function(d){w||(w=!0,d.statusCode>=200&&d.statusCode<=299?k(null,t,d):(301==d.statusCode||302==d.statusCode)&&s.followRedirects&&d.headers&&d.headers.location?u._performSecureRequest(a,b,c,d.headers.location,e,f,g,k):k({statusCode:d.statusCode,data:t},t,d))};m.on("response",function(a){a.setEncoding("utf8"),a.on("data",function(a){t+=a}),a.on("end",function(){x(a)}),a.on("close",function(){v&&x(a)})}),m.on("error",function(a){w||(w=!0,k(a))}),("POST"==c||"PUT"==c)&&null!=f&&""!=f&&m.write(f),m.end()},b.OAuth.prototype.setClientOptions=function(a){var b,c={},d=Object.prototype.hasOwnProperty;for(b in this._defaultClientOptions)d.call(a,b)?c[b]=a[b]:c[b]=this._defaultClientOptions[b];this._clientOptions=c},b.OAuth.prototype.getOAuthAccessToken=function(a,b,c,d){var e={};"function"==typeof c?d=c:e.oauth_verifier=c,this._performSecureRequest(a,b,this._clientOptions.accessTokenHttpMethod,this._accessUrl,e,null,null,function(a,b,c){if(a)d(a);else{var e=i.parse(b),f=e.oauth_token;delete e.oauth_token;var g=e.oauth_token_secret;delete e.oauth_token_secret,d(null,f,g,e)}})},b.OAuth.prototype.getProtectedResource=function(a,b,c,d,e){this._performSecureRequest(c,d,b,a,null,"",null,e)},b.OAuth.prototype.delete=function(a,b,c,d){return this._performSecureRequest(b,c,"DELETE",a,null,"",null,d)},b.OAuth.prototype.get=function(a,b,c,d){return this._performSecureRequest(b,c,"GET",a,null,"",null,d)},b.OAuth.prototype._putOrPost=function(a,b,c,d,e,f,g){var h=null;return"function"==typeof f&&(g=f,f=null),"string"==typeof e||Buffer.isBuffer(e)||(f="application/x-www-form-urlencoded",h=e,e=null),this._performSecureRequest(c,d,a,b,h,e,f,g)},b.OAuth.prototype.put=function(a,b,c,d,e,f){return this._putOrPost("PUT",a,b,c,d,e,f)},b.OAuth.prototype.post=function(a,b,c,d,e,f){return this._putOrPost("POST",a,b,c,d,e,f)},b.OAuth.prototype.getOAuthRequestToken=function(a,b){"function"==typeof a&&(b=a,a={}),this._authorize_callback&&(a.oauth_callback=this._authorize_callback),this._performSecureRequest(null,null,this._clientOptions.requestTokenHttpMethod,this._requestUrl,a,null,null,function(a,c,d){if(a)b(a);else{var e=i.parse(c),f=e.oauth_token,g=e.oauth_token_secret;delete e.oauth_token,delete e.oauth_token_secret,b(null,f,g,e)}})},b.OAuth.prototype.signUrl=function(a,b,c,d){if(void 0===d)var d="GET";for(var e=this._prepareParameters(b,c,d,a,{}),f=h.parse(a,!1),g="",i=0;i<e.length;i++)g+=e[i][0]+"="+this._encodeData(e[i][1])+"&";return g=g.substring(0,g.length-1),f.protocol+"//"+f.host+f.pathname+"?"+g},b.OAuth.prototype.authHeader=function(a,b,c,d){if(void 0===d)var d="GET";var e=this._prepareParameters(b,c,d,a,{});return this._buildAuthorizationHeaders(e)}},28339:(a,b,c)=>{var d=c(18040);function e(){var b,c,f="function"==typeof Symbol?Symbol:{},g=f.iterator||"@@iterator",h=f.toStringTag||"@@toStringTag";function i(a,e,f,g){var h=Object.create((e&&e.prototype instanceof k?e:k).prototype);return d(h,"_invoke",function(a,d,e){var f,g,h,i=0,k=e||[],l=!1,m={p:0,n:0,v:b,a:n,f:n.bind(b,4),d:function(a,c){return f=a,g=0,h=b,m.n=c,j}};function n(a,d){for(g=a,h=d,c=0;!l&&i&&!e&&c<k.length;c++){var e,f=k[c],n=m.p,o=f[2];a>3?(e=o===d)&&(h=f[(g=f[4])?5:(g=3,3)],f[4]=f[5]=b):f[0]<=n&&((e=a<2&&n<f[1])?(g=0,m.v=d,m.n=f[1]):n<o&&(e=a<3||f[0]>d||d>o)&&(f[4]=a,f[5]=d,m.n=o,g=0))}if(e||a>1)return j;throw l=!0,d}return function(e,k,o){if(i>1)throw TypeError("Generator is already running");for(l&&1===k&&n(k,o),g=k,h=o;(c=g<2?b:h)||!l;){f||(g?g<3?(g>1&&(m.n=-1),n(g,h)):m.n=h:m.v=h);try{if(i=2,f){if(g||(e="next"),c=f[e]){if(!(c=c.call(f,h)))throw TypeError("iterator result is not an object");if(!c.done)return c;h=c.value,g<2&&(g=0)}else 1===g&&(c=f.return)&&c.call(f),g<2&&(h=TypeError("The iterator does not provide a '"+e+"' method"),g=1);f=b}else if((c=(l=m.n<0)?h:a.call(d,m))!==j)break}catch(a){f=b,g=1,h=a}finally{i=1}}return{value:c,done:l}}}(a,f,g),!0),h}var j={};function k(){}function l(){}function m(){}c=Object.getPrototypeOf;var n=m.prototype=k.prototype=Object.create([][g]?c(c([][g]())):(d(c={},g,function(){return this}),c));function o(a){return Object.setPrototypeOf?Object.setPrototypeOf(a,m):(a.__proto__=m,d(a,h,"GeneratorFunction")),a.prototype=Object.create(n),a}return l.prototype=m,d(n,"constructor",m),d(m,"constructor",l),l.displayName="GeneratorFunction",d(m,h,"GeneratorFunction"),d(n),d(n,h,"Generator"),d(n,g,function(){return this}),d(n,"toString",function(){return"[object Generator]"}),(a.exports=e=function(){return{w:i,m:o}},a.exports.__esModule=!0,a.exports.default=a.exports)()}a.exports=e,a.exports.__esModule=!0,a.exports.default=a.exports},28354:a=>{"use strict";a.exports=require("util")},28510:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.default=void 0,b.proxyLogger=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:k,b=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return a;var c={},d=function(a){var d;c[a]=(d=(0,g.default)(e.default.mark(function c(d,g){var h,l;return e.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(k[a](d,g),"error"===a&&(g=j(g)),g.client=!0,h="".concat(b,"/_log"),l=new URLSearchParams(function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?i(Object(c),!0).forEach(function(b){(0,f.default)(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):i(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({level:a,code:d},g)),!navigator.sendBeacon){c.next=8;break}return c.abrupt("return",navigator.sendBeacon(h,l));case 8:return c.next=10,fetch(h,{method:"POST",body:l,keepalive:!0});case 10:return c.abrupt("return",c.sent);case 11:case"end":return c.stop()}},c)})),function(a,b){return d.apply(this,arguments)})};for(var h in a)d(h);return c}catch(a){return k}},b.setLogger=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},b=arguments.length>1?arguments[1]:void 0;b||(k.debug=function(){}),a.error&&(k.error=a.error),a.warn&&(k.warn=a.warn),a.debug&&(k.debug=a.debug)};var e=d(c(18490)),f=d(c(3399)),g=d(c(48807)),h=c(20113);function i(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function j(a){var b,c;if(a instanceof Error&&!(a instanceof h.UnknownError))return{message:a.message,stack:a.stack,name:a.name};if(null!=(b=a)&&b.error){a.error=j(a.error),a.message=null!=(c=a.message)?c:a.error.message}return a}var k={error:function(a,b){b=j(b),console.error("[next-auth][error][".concat(a,"]"),"\nhttps://next-auth.js.org/errors#".concat(a.toLowerCase()),b.message,b)},warn:function(a){console.warn("[next-auth][warn][".concat(a,"]"),"\nhttps://next-auth.js.org/warnings#".concat(a.toLowerCase()))},debug:function(a,b){console.log("[next-auth][debug][".concat(a,"]"),b)}};b.default=k},28577:(a,b,c)=>{let d=c(37544),e=c(93929),f=c(78830),g=Symbol(),h=(a,{alg:b,use:c})=>{let d=0;return b&&a.alg&&d++,c&&a.use&&d++,d};a.exports=class{#b;constructor(a,b){if(a!==g)throw Error("invalid constructor call");this.#b=b}toJWKS(){return{keys:this.map(({jwk:{d:a,p:b,q:c,dp:d,dq:e,qi:f,...g}})=>g)}}all({alg:a,kid:b,use:c}={}){if(!c||!a)throw Error();let d=function(a){switch("string"==typeof a&&a.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:return}}(a),e={alg:a,use:c};return this.filter(e=>{let f=!0;return void 0!==d&&e.jwk.kty!==d&&(f=!1),f&&void 0!==b&&e.jwk.kid!==b&&(f=!1),f&&void 0!==c&&void 0!==e.jwk.use&&e.jwk.use!==c&&(f=!1),f&&e.jwk.alg&&e.jwk.alg!==a?f=!1:e.algorithms.has(a)||(f=!1),f}).sort((a,b)=>h(b,e)-h(a,e))}get(...a){return this.all(...a)[0]}static async fromJWKS(a,{onlyPublic:b=!1,onlyPrivate:c=!1}={}){if(!f(a)||!Array.isArray(a.keys)||a.keys.some(a=>!f(a)||!("kty"in a)))throw TypeError("jwks must be a JSON Web Key Set formatted object");let h=[];for(let f of a.keys){let{kty:a,kid:g,crv:i}=f=e(f),{alg:j,use:k}=f;if("string"==typeof a&&a&&(void 0===k||"sig"===k||"enc"===k)&&("string"==typeof j||void 0===j)&&("string"==typeof g||void 0===g)){if("EC"===a&&"sig"===k)switch(i){case"P-256":j="ES256";break;case"P-384":j="ES384";break;case"P-521":j="ES512"}if("secp256k1"===i&&(k="sig",j="ES256K"),"OKP"===a)switch(i){case"Ed25519":case"Ed448":k="sig",j="EdDSA";break;case"X25519":case"X448":k="enc"}if(j&&!k)switch(!0){case j.startsWith("ECDH"):case j.startsWith("RSA"):k="enc"}if(c&&("oct"===f.kty||!f.d))throw Error("jwks must only contain private keys");b&&(f.d||f.k)||h.push({jwk:{...f,alg:j,use:k},async keyObject(a){if(this[a])return this[a];let b=await d.importJWK(this.jwk,a);return this[a]=b,b},get algorithms(){return Object.defineProperty(this,"algorithms",{value:function(a,b,c,e){if(b)return new Set([b]);switch(c){case"EC":{let b=[];if(("enc"===a||void 0===a)&&(b=b.concat(["ECDH-ES","ECDH-ES+A128KW","ECDH-ES+A192KW","ECDH-ES+A256KW"])),"sig"===a||void 0===a)switch(e){case"P-256":case"P-384":b=b.concat([`ES${e.slice(-3)}`]);break;case"P-521":b=b.concat(["ES512"]);break;case"secp256k1":"node:crypto"===d.cryptoRuntime&&(b=b.concat(["ES256K"]))}return new Set(b)}case"OKP":return new Set(["ECDH-ES","ECDH-ES+A128KW","ECDH-ES+A192KW","ECDH-ES+A256KW"]);case"RSA":{let b=[];return("enc"===a||void 0===a)&&(b=b.concat(["RSA-OAEP","RSA-OAEP-256","RSA-OAEP-384","RSA-OAEP-512"]),"node:crypto"===d.cryptoRuntime&&(b=b.concat(["RSA1_5"]))),("sig"===a||void 0===a)&&(b=b.concat(["PS256","PS384","PS512","RS256","RS384","RS512"])),new Set(b)}default:throw Error("unreachable")}}(this.jwk.use,this.jwk.alg,this.jwk.kty,this.jwk.crv),enumerable:!0,configurable:!1}),this.algorithms}})}}return new this(g,h)}filter(...a){return this.#b.filter(...a)}find(...a){return this.#b.find(...a)}every(...a){return this.#b.every(...a)}some(...a){return this.#b.some(...a)}map(...a){return this.#b.map(...a)}forEach(...a){return this.#b.forEach(...a)}reduce(...a){return this.#b.reduce(...a)}sort(...a){return this.#b.sort(...a)}*[Symbol.iterator](){for(let a of this.#b)yield a}}},29132:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.compactVerify=void 0;let d=c(71746),e=c(99938),f=c(2594);b.compactVerify=async function(a,b,c){if(a instanceof Uint8Array&&(a=f.decoder.decode(a)),"string"!=typeof a)throw new e.JWSInvalid("Compact JWS must be a string or Uint8Array");let{0:g,1:h,2:i,length:j}=a.split(".");if(3!==j)throw new e.JWSInvalid("Invalid Compact JWS");let k=await (0,d.flattenedVerify)({payload:h,protected:g,signature:i},b,c),l={payload:k.payload,protectedHeader:k.protectedHeader};return"function"==typeof b?{...l,key:k.key}:l}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29837:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.createSecret=function(a){var b;let{authOptions:c,url:e}=a;return null!=(b=c.secret)?b:(0,d.createHash)("sha256").update(JSON.stringify({...e,...c})).digest("hex")},b.fromDate=function(a,b=Date.now()){return new Date(b+1e3*a)},b.hashToken=function(a,b){var c;let{provider:e,secret:f}=b;return(0,d.createHash)("sha256").update(`${a}${null!=(c=e.secret)?c:f}`).digest("hex")};var d=c(55511)},30641:(a,b,c)=>{let{inspect:d}=c(28354),{RPError:e,OPError:f}=c(61408),g=c(16156);class h{#c;#d;#e;#f;#g;#h;#i;#j;#k;constructor({client:a,exchangeBody:b,clientAssertionPayload:c,response:d,maxAge:f,DPoP:h}){if(["verification_uri","user_code","device_code"].forEach(a=>{if("string"!=typeof d[a]||!d[a])throw new e(`expected ${a} string to be returned by Device Authorization Response, got %j`,d[a])}),!Number.isSafeInteger(d.expires_in))throw new e("expected expires_in number to be returned by Device Authorization Response, got %j",d.expires_in);this.#h=g()+d.expires_in,this.#d=a,this.#f=h,this.#j=f,this.#g=b,this.#e=c,this.#k=d,this.#i=1e3*d.interval||5e3}abort(){this.#c=!0}async poll({signal:a}={}){let b;if(a&&a.aborted||this.#c)throw new e("polling aborted");if(this.expired())throw new e("the device code %j has expired and the device authorization session has concluded",this.device_code);await new Promise(a=>setTimeout(a,this.#i));try{b=await this.#d.grant({...this.#g,grant_type:"urn:ietf:params:oauth:grant-type:device_code",device_code:this.device_code},{clientAssertionPayload:this.#e,DPoP:this.#f})}catch(b){switch(b instanceof f&&b.error){case"slow_down":this.#i+=5e3;case"authorization_pending":return this.poll({signal:a});default:throw b}}return"id_token"in b&&(await this.#d.decryptIdToken(b),await this.#d.validateIdToken(b,void 0,"token",this.#j)),b}get device_code(){return this.#k.device_code}get user_code(){return this.#k.user_code}get verification_uri(){return this.#k.verification_uri}get verification_uri_complete(){return this.#k.verification_uri_complete}get expires_in(){return Math.max.apply(null,[this.#h-g(),0])}expired(){return 0===this.expires_in}[d.custom](){return`${this.constructor.name} ${d(this.#k,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}}a.exports=h},30994:a=>{function b(c,d){return a.exports=b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a},a.exports.__esModule=!0,a.exports.default=a.exports,b(c,d)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},33648:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.generateSecret=void 0;let d=c(51688);b.generateSecret=async function(a,b){return(0,d.generateSecret)(a,b)}},34010:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(2594);b.default=function(a,b,c,f,g,h){let i=(0,e.concat)(a,b,c,(0,e.uint64be)(a.length<<3)),j=(0,d.createHmac)(`sha${f}`,g);return j.update(i),j.digest().slice(0,h>>3)}},35426:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=void 0,b.getServerSession=h,b.unstable_getServerSession=i;var d=c(64872),e=c(87451);async function f(a,b,c){var f,g,h,i,j,k,l,m;let{nextauth:n,...o}=a.query;null!=c.secret||(c.secret=null!=(f=null!=(g=null==(h=c.jwt)?void 0:h.secret)?g:process.env.NEXTAUTH_SECRET)?f:process.env.AUTH_SECRET);let p=await (0,d.AuthHandler)({req:{body:a.body,query:o,cookies:a.cookies,headers:a.headers,method:a.method,action:null==n?void 0:n[0],providerId:null==n?void 0:n[1],error:null!=(i=a.query.error)?i:null==n?void 0:n[1]},options:c});if(b.status(null!=(j=p.status)?j:200),null==(k=p.cookies)||k.forEach(a=>(0,e.setCookie)(b,a)),null==(l=p.headers)||l.forEach(a=>b.setHeader(a.key,a.value)),p.redirect){if((null==(m=a.body)?void 0:m.json)!=="true"){b.status(302).setHeader("Location",p.redirect),b.end();return}return b.json({url:p.redirect})}return b.send(p.body)}async function g(a,b,f){var g,h,i;null!=f.secret||(f.secret=null!=(g=process.env.NEXTAUTH_SECRET)?g:process.env.AUTH_SECRET);let{headers:j,cookies:k}=c(44999),l=null==(h=await b.params)?void 0:h.nextauth,m=Object.fromEntries(a.nextUrl.searchParams),n=await (0,e.getBody)(a),o=await (0,d.AuthHandler)({req:{body:n,query:m,cookies:Object.fromEntries((await k()).getAll().map(a=>[a.name,a.value])),headers:Object.fromEntries(await j()),method:a.method,action:null==l?void 0:l[0],providerId:null==l?void 0:l[1],error:null!=(i=m.error)?i:null==l?void 0:l[1]},options:f}),p=(0,e.toResponse)(o),q=p.headers.get("Location");return(null==n?void 0:n.json)==="true"&&q?(p.headers.delete("Location"),p.headers.set("Content-Type","application/json"),new Response(JSON.stringify({url:q}),{status:o.status,headers:p.headers})):p}async function h(...a){var b,f;let g,i,j,k=0===a.length||1===a.length;if(k){j=Object.assign({},a[0],{providers:[]});let{headers:b,cookies:d}=c(44999);g={headers:Object.fromEntries(await b()),cookies:Object.fromEntries((await d()).getAll().map(a=>[a.name,a.value]))},i={getHeader(){},setCookie(){},setHeader(){}}}else g=a[0],i=a[1],j=Object.assign({},a[2],{providers:[]});null!=(b=j).secret||(b.secret=null!=(f=process.env.NEXTAUTH_SECRET)?f:process.env.AUTH_SECRET);let{body:l,cookies:m,status:n=200}=await (0,d.AuthHandler)({options:j,req:{action:"session",method:"GET",cookies:g.cookies,headers:g.headers}});if(null==m||m.forEach(a=>(0,e.setCookie)(i,a)),l&&"string"!=typeof l&&Object.keys(l).length){if(200===n)return k&&delete l.expires,l;throw Error(l.message)}return null}async function i(...a){return await h(...a)}b.default=function(...a){var b;return 1===a.length?async(b,c)=>null!=c&&c.params?await g(b,c,a[0]):await f(b,c,a[0]):null!=(b=a[1])&&b.params?g(...a):f(...a)}},35571:(a,b,c)=>{let{createHash:d,randomBytes:e}=c(55511),f=c(70885),g=(a=32)=>f.encode(e(a));a.exports={random:g,state:g,nonce:g,codeVerifier:g,codeChallenge:a=>f.encode(d("sha256").update(a).digest())}},35749:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.bitLength=void 0;let d=c(99938),e=c(7552);function f(a){switch(a){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new d.JOSENotSupported(`Unsupported JWE Algorithm: ${a}`)}}b.bitLength=f,b.default=a=>(0,e.default)(new Uint8Array(f(a)>>3))},36720:(a,b,c)=>{var d=c(11723),e=(c(55511),c(55591)),f=c(81630),g=c(79551),h=c(22165);b.OAuth2=function(a,b,c,d,e,f){this._clientId=a,this._clientSecret=b,this._baseSite=c,this._authorizeUrl=d||"/oauth/authorize",this._accessTokenUrl=e||"/oauth/access_token",this._accessTokenName="access_token",this._authMethod="Bearer",this._customHeaders=f||{},this._useAuthorizationHeaderForGET=!1,this._agent=void 0},b.OAuth2.prototype.setAgent=function(a){this._agent=a},b.OAuth2.prototype.setAccessTokenName=function(a){this._accessTokenName=a},b.OAuth2.prototype.setAuthMethod=function(a){this._authMethod=a},b.OAuth2.prototype.useAuthorizationHeaderforGET=function(a){this._useAuthorizationHeaderForGET=a},b.OAuth2.prototype._getAccessTokenUrl=function(){return this._baseSite+this._accessTokenUrl},b.OAuth2.prototype.buildAuthHeader=function(a){return this._authMethod+" "+a},b.OAuth2.prototype._chooseHttpLibrary=function(a){var b=e;return"https:"!=a.protocol&&(b=f),b},b.OAuth2.prototype._request=function(a,b,c,e,f,h){var i=g.parse(b,!0);"https:"!=i.protocol||i.port||(i.port=443);var j=this._chooseHttpLibrary(i),k={};for(var l in this._customHeaders)k[l]=this._customHeaders[l];if(c)for(var l in c)k[l]=c[l];k.Host=i.host,k["User-Agent"]||(k["User-Agent"]="Node-oauth"),e?Buffer.isBuffer(e)?k["Content-Length"]=e.length:k["Content-Length"]=Buffer.byteLength(e):k["Content-length"]=0,!f||"Authorization"in k||(i.query||(i.query={}),i.query[this._accessTokenName]=f);var m=d.stringify(i.query);m&&(m="?"+m);var n={host:i.hostname,port:i.port,path:i.pathname+m,method:a,headers:k};this._executeRequest(j,n,e,h)},b.OAuth2.prototype._executeRequest=function(a,b,c,d){var e=h.isAnEarlyCloseHost(b.host),f=!1;function g(a,b){f||(f=!0,a.statusCode>=200&&a.statusCode<=299||301==a.statusCode||302==a.statusCode?d(null,b,a):d({statusCode:a.statusCode,data:b}))}var i="";this._agent&&(b.agent=this._agent);var j=a.request(b);j.on("response",function(a){a.on("data",function(a){i+=a}),a.on("close",function(b){e&&g(a,i)}),a.addListener("end",function(){g(a,i)})}),j.on("error",function(a){f=!0,d(a)}),("POST"==b.method||"PUT"==b.method)&&c&&j.write(c),j.end()},b.OAuth2.prototype.getAuthorizeUrl=function(a){var a=a||{};return a.client_id=this._clientId,this._baseSite+this._authorizeUrl+"?"+d.stringify(a)},b.OAuth2.prototype.getOAuthAccessToken=function(a,b,c){var b=b||{};b.client_id=this._clientId,b.client_secret=this._clientSecret;var e="refresh_token"===b.grant_type?"refresh_token":"code";b[e]=a;var f=d.stringify(b);this._request("POST",this._getAccessTokenUrl(),{"Content-Type":"application/x-www-form-urlencoded"},f,null,function(a,b,e){if(a)c(a);else{try{f=JSON.parse(b)}catch(a){f=d.parse(b)}var f,g=f.access_token,h=f.refresh_token;delete f.refresh_token,c(null,g,h,f)}})},b.OAuth2.prototype.getProtectedResource=function(a,b,c){this._request("GET",a,{},"",b,c)},b.OAuth2.prototype.get=function(a,b,c){if(this._useAuthorizationHeaderForGET){var d={Authorization:this.buildAuthHeader(b)};b=null}else d={};this._request("GET",a,d,"",b,c)}},36736:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.flattenedDecrypt=void 0;let d=c(68327),e=c(85634),f=c(66318),g=c(99938),h=c(65597),i=c(25110),j=c(43343),k=c(2594),l=c(61689),m=c(8535),n=c(93069);b.flattenedDecrypt=async function(a,b,c){var o;let p,q,r,s,t,u,v;if(!(0,i.default)(a))throw new g.JWEInvalid("Flattened JWE must be an object");if(void 0===a.protected&&void 0===a.header&&void 0===a.unprotected)throw new g.JWEInvalid("JOSE Header missing");if("string"!=typeof a.iv)throw new g.JWEInvalid("JWE Initialization Vector missing or incorrect type");if("string"!=typeof a.ciphertext)throw new g.JWEInvalid("JWE Ciphertext missing or incorrect type");if("string"!=typeof a.tag)throw new g.JWEInvalid("JWE Authentication Tag missing or incorrect type");if(void 0!==a.protected&&"string"!=typeof a.protected)throw new g.JWEInvalid("JWE Protected Header incorrect type");if(void 0!==a.encrypted_key&&"string"!=typeof a.encrypted_key)throw new g.JWEInvalid("JWE Encrypted Key incorrect type");if(void 0!==a.aad&&"string"!=typeof a.aad)throw new g.JWEInvalid("JWE AAD incorrect type");if(void 0!==a.header&&!(0,i.default)(a.header))throw new g.JWEInvalid("JWE Shared Unprotected Header incorrect type");if(void 0!==a.unprotected&&!(0,i.default)(a.unprotected))throw new g.JWEInvalid("JWE Per-Recipient Unprotected Header incorrect type");if(a.protected)try{let b=(0,d.decode)(a.protected);p=JSON.parse(k.decoder.decode(b))}catch{throw new g.JWEInvalid("JWE Protected Header is invalid")}if(!(0,h.default)(p,a.header,a.unprotected))throw new g.JWEInvalid("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let w={...p,...a.header,...a.unprotected};if((0,m.default)(g.JWEInvalid,new Map,null==c?void 0:c.crit,p,w),void 0!==w.zip){if(!p||!p.zip)throw new g.JWEInvalid('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==w.zip)throw new g.JOSENotSupported('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:x,enc:y}=w;if("string"!=typeof x||!x)throw new g.JWEInvalid("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof y||!y)throw new g.JWEInvalid("missing JWE Encryption Algorithm (enc) in JWE Header");let z=c&&(0,n.default)("keyManagementAlgorithms",c.keyManagementAlgorithms),A=c&&(0,n.default)("contentEncryptionAlgorithms",c.contentEncryptionAlgorithms);if(z&&!z.has(x))throw new g.JOSEAlgNotAllowed('"alg" (Algorithm) Header Parameter not allowed');if(A&&!A.has(y))throw new g.JOSEAlgNotAllowed('"enc" (Encryption Algorithm) Header Parameter not allowed');if(void 0!==a.encrypted_key)try{q=(0,d.decode)(a.encrypted_key)}catch{throw new g.JWEInvalid("Failed to base64url decode the encrypted_key")}let B=!1;"function"==typeof b&&(b=await b(p,a),B=!0);try{r=await (0,j.default)(x,b,q,w,c)}catch(a){if(a instanceof TypeError||a instanceof g.JWEInvalid||a instanceof g.JOSENotSupported)throw a;r=(0,l.default)(y)}try{s=(0,d.decode)(a.iv)}catch{throw new g.JWEInvalid("Failed to base64url decode the iv")}try{t=(0,d.decode)(a.tag)}catch{throw new g.JWEInvalid("Failed to base64url decode the tag")}let C=k.encoder.encode(null!=(o=a.protected)?o:"");u=void 0!==a.aad?(0,k.concat)(C,k.encoder.encode("."),k.encoder.encode(a.aad)):C;try{v=(0,d.decode)(a.ciphertext)}catch{throw new g.JWEInvalid("Failed to base64url decode the ciphertext")}let D=await (0,e.default)(y,r,v,s,t,u);"DEF"===w.zip&&(D=await ((null==c?void 0:c.inflateRaw)||f.inflate)(D));let E={plaintext:D};if(void 0!==a.protected&&(E.protectedHeader=p),void 0!==a.aad)try{E.additionalAuthenticatedData=(0,d.decode)(a.aad)}catch{throw new g.JWEInvalid("Failed to base64url decode the aad")}return(void 0!==a.unprotected&&(E.sharedUnprotectedHeader=a.unprotected),void 0!==a.header&&(E.unprotectedHeader=a.header),B)?{...E,key:b}:E}},37265:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.types=void 0;let d=c(17996),e=c(90061);b.default=a=>(0,e.default)(a)||(0,d.isCryptoKey)(a);let f=["KeyObject"];b.types=f,(globalThis.CryptoKey||(null===d.default||void 0===d.default?void 0:d.default.CryptoKey))&&f.push("CryptoKey")},37375:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511);b.default=(a,b)=>(0,d.createHash)(a).update(b).digest()},37544:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.cryptoRuntime=b.base64url=b.generateSecret=b.generateKeyPair=b.errors=b.decodeJwt=b.decodeProtectedHeader=b.importJWK=b.importX509=b.importPKCS8=b.importSPKI=b.exportJWK=b.exportSPKI=b.exportPKCS8=b.UnsecuredJWT=b.createRemoteJWKSet=b.createLocalJWKSet=b.EmbeddedJWK=b.calculateJwkThumbprintUri=b.calculateJwkThumbprint=b.EncryptJWT=b.SignJWT=b.GeneralSign=b.FlattenedSign=b.CompactSign=b.FlattenedEncrypt=b.CompactEncrypt=b.jwtDecrypt=b.jwtVerify=b.generalVerify=b.flattenedVerify=b.compactVerify=b.GeneralEncrypt=b.generalDecrypt=b.flattenedDecrypt=b.compactDecrypt=void 0;var d=c(81454);Object.defineProperty(b,"compactDecrypt",{enumerable:!0,get:function(){return d.compactDecrypt}});var e=c(36736);Object.defineProperty(b,"flattenedDecrypt",{enumerable:!0,get:function(){return e.flattenedDecrypt}});var f=c(68907);Object.defineProperty(b,"generalDecrypt",{enumerable:!0,get:function(){return f.generalDecrypt}});var g=c(64127);Object.defineProperty(b,"GeneralEncrypt",{enumerable:!0,get:function(){return g.GeneralEncrypt}});var h=c(29132);Object.defineProperty(b,"compactVerify",{enumerable:!0,get:function(){return h.compactVerify}});var i=c(71746);Object.defineProperty(b,"flattenedVerify",{enumerable:!0,get:function(){return i.flattenedVerify}});var j=c(24575);Object.defineProperty(b,"generalVerify",{enumerable:!0,get:function(){return j.generalVerify}});var k=c(98905);Object.defineProperty(b,"jwtVerify",{enumerable:!0,get:function(){return k.jwtVerify}});var l=c(28247);Object.defineProperty(b,"jwtDecrypt",{enumerable:!0,get:function(){return l.jwtDecrypt}});var m=c(52118);Object.defineProperty(b,"CompactEncrypt",{enumerable:!0,get:function(){return m.CompactEncrypt}});var n=c(14988);Object.defineProperty(b,"FlattenedEncrypt",{enumerable:!0,get:function(){return n.FlattenedEncrypt}});var o=c(99312);Object.defineProperty(b,"CompactSign",{enumerable:!0,get:function(){return o.CompactSign}});var p=c(59310);Object.defineProperty(b,"FlattenedSign",{enumerable:!0,get:function(){return p.FlattenedSign}});var q=c(45495);Object.defineProperty(b,"GeneralSign",{enumerable:!0,get:function(){return q.GeneralSign}});var r=c(48513);Object.defineProperty(b,"SignJWT",{enumerable:!0,get:function(){return r.SignJWT}});var s=c(45787);Object.defineProperty(b,"EncryptJWT",{enumerable:!0,get:function(){return s.EncryptJWT}});var t=c(18652);Object.defineProperty(b,"calculateJwkThumbprint",{enumerable:!0,get:function(){return t.calculateJwkThumbprint}}),Object.defineProperty(b,"calculateJwkThumbprintUri",{enumerable:!0,get:function(){return t.calculateJwkThumbprintUri}});var u=c(1149);Object.defineProperty(b,"EmbeddedJWK",{enumerable:!0,get:function(){return u.EmbeddedJWK}});var v=c(2055);Object.defineProperty(b,"createLocalJWKSet",{enumerable:!0,get:function(){return v.createLocalJWKSet}});var w=c(91428);Object.defineProperty(b,"createRemoteJWKSet",{enumerable:!0,get:function(){return w.createRemoteJWKSet}});var x=c(71872);Object.defineProperty(b,"UnsecuredJWT",{enumerable:!0,get:function(){return x.UnsecuredJWT}});var y=c(65764);Object.defineProperty(b,"exportPKCS8",{enumerable:!0,get:function(){return y.exportPKCS8}}),Object.defineProperty(b,"exportSPKI",{enumerable:!0,get:function(){return y.exportSPKI}}),Object.defineProperty(b,"exportJWK",{enumerable:!0,get:function(){return y.exportJWK}});var z=c(97920);Object.defineProperty(b,"importSPKI",{enumerable:!0,get:function(){return z.importSPKI}}),Object.defineProperty(b,"importPKCS8",{enumerable:!0,get:function(){return z.importPKCS8}}),Object.defineProperty(b,"importX509",{enumerable:!0,get:function(){return z.importX509}}),Object.defineProperty(b,"importJWK",{enumerable:!0,get:function(){return z.importJWK}});var A=c(69184);Object.defineProperty(b,"decodeProtectedHeader",{enumerable:!0,get:function(){return A.decodeProtectedHeader}});var B=c(52645);Object.defineProperty(b,"decodeJwt",{enumerable:!0,get:function(){return B.decodeJwt}}),b.errors=c(99938);var C=c(13668);Object.defineProperty(b,"generateKeyPair",{enumerable:!0,get:function(){return C.generateKeyPair}});var D=c(33648);Object.defineProperty(b,"generateSecret",{enumerable:!0,get:function(){return D.generateSecret}}),b.base64url=c(67929);var E=c(8157);Object.defineProperty(b,"cryptoRuntime",{enumerable:!0,get:function(){return E.default}})},38700:a=>{let b=/(\w+)=("[^"]*")/g;a.exports=a=>{let c={};try{for(;null!==b.exec(a);)RegExp.$1&&RegExp.$2&&(c[RegExp.$1]=RegExp.$2.slice(1,-1))}catch(a){}return c}},41193:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(68327),f=c(72443),g=c(99938),h=c(62909),i=c(17996),j=c(90061),k=c(78680),l=c(37265),m=c(80028),n=a=>{let b;if((0,i.isCryptoKey)(a)){if(!a.extractable)throw TypeError("CryptoKey is not extractable");b=d.KeyObject.from(a)}else if((0,j.default)(a))b=a;else if(a instanceof Uint8Array)return{kty:"oct",k:(0,e.encode)(a)};else throw TypeError((0,k.default)(a,...l.types,"Uint8Array"));if(m.jwkExport){if("secret"!==b.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(b.asymmetricKeyType))throw new g.JOSENotSupported("Unsupported key asymmetricKeyType");return b.export({format:"jwk"})}switch(b.type){case"secret":return{kty:"oct",k:(0,e.encode)(b.export())};case"private":case"public":switch(b.asymmetricKeyType){case"rsa":{let a,c=b.export({format:"der",type:"pkcs1"}),d=new f.default(c);"private"===b.type&&d.unsignedInteger();let g=(0,e.encode)(d.unsignedInteger()),h=(0,e.encode)(d.unsignedInteger());return"private"===b.type&&(a={d:(0,e.encode)(d.unsignedInteger()),p:(0,e.encode)(d.unsignedInteger()),q:(0,e.encode)(d.unsignedInteger()),dp:(0,e.encode)(d.unsignedInteger()),dq:(0,e.encode)(d.unsignedInteger()),qi:(0,e.encode)(d.unsignedInteger())}),d.end(),{kty:"RSA",n:g,e:h,...a}}case"ec":{let a,c,f,i=(0,h.default)(b);switch(i){case"secp256k1":a=64,c=33,f=-1;break;case"P-256":a=64,c=36,f=-1;break;case"P-384":a=96,c=35,f=-3;break;case"P-521":a=132,c=35,f=-3;break;default:throw new g.JOSENotSupported("Unsupported curve")}if("public"===b.type){let c=b.export({type:"spki",format:"der"});return{kty:"EC",crv:i,x:(0,e.encode)(c.subarray(-a,-a/2)),y:(0,e.encode)(c.subarray(-a/2))}}let j=b.export({type:"pkcs8",format:"der"});return j.length<100&&(c+=f),{...n((0,d.createPublicKey)(b)),d:(0,e.encode)(j.subarray(c,c+a/2))}}case"ed25519":case"x25519":{let a=(0,h.default)(b);if("public"===b.type){let c=b.export({type:"spki",format:"der"});return{kty:"OKP",crv:a,x:(0,e.encode)(c.subarray(-32))}}let c=b.export({type:"pkcs8",format:"der"});return{...n((0,d.createPublicKey)(b)),d:(0,e.encode)(c.subarray(-32))}}case"ed448":case"x448":{let a=(0,h.default)(b);if("public"===b.type){let c=b.export({type:"spki",format:"der"});return{kty:"OKP",crv:a,x:(0,e.encode)(c.subarray("Ed448"===a?-57:-56))}}let c=b.export({type:"pkcs8",format:"der"});return{...n((0,d.createPublicKey)(b)),d:(0,e.encode)(c.subarray("Ed448"===a?-57:-56))}}default:throw new g.JOSENotSupported("Unsupported key asymmetricKeyType")}default:throw new g.JOSENotSupported("Unsupported key type")}};b.default=n},42142:(a,b,c)=>{"use strict";let d=c(93466),e=Symbol("max"),f=Symbol("length"),g=Symbol("lengthCalculator"),h=Symbol("allowStale"),i=Symbol("maxAge"),j=Symbol("dispose"),k=Symbol("noDisposeOnSet"),l=Symbol("lruList"),m=Symbol("cache"),n=Symbol("updateAgeOnGet"),o=()=>1;class p{constructor(a){if("number"==typeof a&&(a={max:a}),a||(a={}),a.max&&("number"!=typeof a.max||a.max<0))throw TypeError("max must be a non-negative number");this[e]=a.max||1/0;let b=a.length||o;if(this[g]="function"!=typeof b?o:b,this[h]=a.stale||!1,a.maxAge&&"number"!=typeof a.maxAge)throw TypeError("maxAge must be a number");this[i]=a.maxAge||0,this[j]=a.dispose,this[k]=a.noDisposeOnSet||!1,this[n]=a.updateAgeOnGet||!1,this.reset()}set max(a){if("number"!=typeof a||a<0)throw TypeError("max must be a non-negative number");this[e]=a||1/0,s(this)}get max(){return this[e]}set allowStale(a){this[h]=!!a}get allowStale(){return this[h]}set maxAge(a){if("number"!=typeof a)throw TypeError("maxAge must be a non-negative number");this[i]=a,s(this)}get maxAge(){return this[i]}set lengthCalculator(a){"function"!=typeof a&&(a=o),a!==this[g]&&(this[g]=a,this[f]=0,this[l].forEach(a=>{a.length=this[g](a.value,a.key),this[f]+=a.length})),s(this)}get lengthCalculator(){return this[g]}get length(){return this[f]}get itemCount(){return this[l].length}rforEach(a,b){b=b||this;for(let c=this[l].tail;null!==c;){let d=c.prev;v(this,a,c,b),c=d}}forEach(a,b){b=b||this;for(let c=this[l].head;null!==c;){let d=c.next;v(this,a,c,b),c=d}}keys(){return this[l].toArray().map(a=>a.key)}values(){return this[l].toArray().map(a=>a.value)}reset(){this[j]&&this[l]&&this[l].length&&this[l].forEach(a=>this[j](a.key,a.value)),this[m]=new Map,this[l]=new d,this[f]=0}dump(){return this[l].map(a=>!r(this,a)&&{k:a.key,v:a.value,e:a.now+(a.maxAge||0)}).toArray().filter(a=>a)}dumpLru(){return this[l]}set(a,b,c){if((c=c||this[i])&&"number"!=typeof c)throw TypeError("maxAge must be a number");let d=c?Date.now():0,h=this[g](b,a);if(this[m].has(a)){if(h>this[e])return t(this,this[m].get(a)),!1;let g=this[m].get(a).value;return this[j]&&!this[k]&&this[j](a,g.value),g.now=d,g.maxAge=c,g.value=b,this[f]+=h-g.length,g.length=h,this.get(a),s(this),!0}let n=new u(a,b,h,d,c);return n.length>this[e]?(this[j]&&this[j](a,b),!1):(this[f]+=n.length,this[l].unshift(n),this[m].set(a,this[l].head),s(this),!0)}has(a){return!!this[m].has(a)&&!r(this,this[m].get(a).value)}get(a){return q(this,a,!0)}peek(a){return q(this,a,!1)}pop(){let a=this[l].tail;return a?(t(this,a),a.value):null}del(a){t(this,this[m].get(a))}load(a){this.reset();let b=Date.now();for(let c=a.length-1;c>=0;c--){let d=a[c],e=d.e||0;if(0===e)this.set(d.k,d.v);else{let a=e-b;a>0&&this.set(d.k,d.v,a)}}}prune(){this[m].forEach((a,b)=>q(this,b,!1))}}let q=(a,b,c)=>{let d=a[m].get(b);if(d){let b=d.value;if(r(a,b)){if(t(a,d),!a[h])return}else c&&(a[n]&&(d.value.now=Date.now()),a[l].unshiftNode(d));return b.value}},r=(a,b)=>{if(!b||!b.maxAge&&!a[i])return!1;let c=Date.now()-b.now;return b.maxAge?c>b.maxAge:a[i]&&c>a[i]},s=a=>{if(a[f]>a[e])for(let b=a[l].tail;a[f]>a[e]&&null!==b;){let c=b.prev;t(a,b),b=c}},t=(a,b)=>{if(b){let c=b.value;a[j]&&a[j](c.key,c.value),a[f]-=c.length,a[m].delete(c.key),a[l].removeNode(b)}};class u{constructor(a,b,c,d,e){this.key=a,this.value=b,this.length=c,this.now=d,this.maxAge=e||0}}let v=(a,b,c,d)=>{let e=c.value;r(a,e)&&(t(a,c),a[h]||(e=void 0)),e&&b.call(d,e.value,e.key,a)};a.exports=p},42567:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(62909),f=c(99938),g=c(78328),h=c(80028),i={padding:d.constants.RSA_PKCS1_PSS_PADDING,saltLength:d.constants.RSA_PSS_SALTLEN_DIGEST},j=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);b.default=function(a,b){switch(a){case"EdDSA":if(!["ed25519","ed448"].includes(b.asymmetricKeyType))throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");return b;case"RS256":case"RS384":case"RS512":if("rsa"!==b.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");return(0,g.default)(b,a),b;case h.rsaPssParams&&"PS256":case h.rsaPssParams&&"PS384":case h.rsaPssParams&&"PS512":if("rsa-pss"===b.asymmetricKeyType){let{hashAlgorithm:c,mgf1HashAlgorithm:d,saltLength:e}=b.asymmetricKeyDetails,f=parseInt(a.slice(-3),10);if(void 0!==c&&(c!==`sha${f}`||d!==c))throw TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${a}`);if(void 0!==e&&e>f>>3)throw TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${a}`)}else if("rsa"!==b.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");return(0,g.default)(b,a),{key:b,...i};case!h.rsaPssParams&&"PS256":case!h.rsaPssParams&&"PS384":case!h.rsaPssParams&&"PS512":if("rsa"!==b.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");return(0,g.default)(b,a),{key:b,...i};case"ES256":case"ES256K":case"ES384":case"ES512":{if("ec"!==b.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let c=(0,e.default)(b),d=j.get(a);if(c!==d)throw TypeError(`Invalid key curve for the algorithm, its curve must be ${d}, got ${c}`);return{dsaEncoding:"ieee-p1363",key:b}}default:throw new f.JOSENotSupported(`alg ${a} is not supported either by JOSE or your javascript runtime`)}}},42856:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.detectOrigin=function(a,b){var c;return(null!=(c=process.env.VERCEL)?c:process.env.AUTH_TRUST_HOST)?`${"http"===b?"http":"https"}://${a}`:process.env.NEXTAUTH_URL}},42891:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938),e=c(35749);b.default=(a,b)=>{if(b.length<<3!==(0,e.bitLength)(a))throw new d.JWEInvalid("Invalid Initialization Vector length")}},43325:a=>{let b=Symbol();a.exports={CLOCK_TOLERANCE:Symbol(),HTTP_OPTIONS:b}},43343:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(88062),e=c(52131),f=c(62197),g=c(56913),h=c(68327),i=c(99938),j=c(61689),k=c(97920),l=c(80335),m=c(25110),n=c(97528);b.default=async function(a,b,c,o,p){switch((0,l.default)(a,b,"decrypt"),a){case"dir":if(void 0!==c)throw new i.JWEInvalid("Encountered unexpected JWE Encrypted Key");return b;case"ECDH-ES":if(void 0!==c)throw new i.JWEInvalid("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let f,g;if(!(0,m.default)(o.epk))throw new i.JWEInvalid('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!e.ecdhAllowed(b))throw new i.JOSENotSupported("ECDH with the provided key is not allowed or not supported by your javascript runtime");let l=await (0,k.importJWK)(o.epk,a);if(void 0!==o.apu){if("string"!=typeof o.apu)throw new i.JWEInvalid('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{f=(0,h.decode)(o.apu)}catch{throw new i.JWEInvalid("Failed to base64url decode the apu")}}if(void 0!==o.apv){if("string"!=typeof o.apv)throw new i.JWEInvalid('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{g=(0,h.decode)(o.apv)}catch{throw new i.JWEInvalid("Failed to base64url decode the apv")}}let n=await e.deriveKey(l,b,"ECDH-ES"===a?o.enc:a,"ECDH-ES"===a?(0,j.bitLength)(o.enc):parseInt(a.slice(-5,-2),10),f,g);if("ECDH-ES"===a)return n;if(void 0===c)throw new i.JWEInvalid("JWE Encrypted Key missing");return(0,d.unwrap)(a.slice(-6),n,c)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===c)throw new i.JWEInvalid("JWE Encrypted Key missing");return(0,g.decrypt)(a,b,c);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let d;if(void 0===c)throw new i.JWEInvalid("JWE Encrypted Key missing");if("number"!=typeof o.p2c)throw new i.JWEInvalid('JOSE Header "p2c" (PBES2 Count) missing or invalid');let e=(null==p?void 0:p.maxPBES2Count)||1e4;if(o.p2c>e)throw new i.JWEInvalid('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof o.p2s)throw new i.JWEInvalid('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{d=(0,h.decode)(o.p2s)}catch{throw new i.JWEInvalid("Failed to base64url decode the p2s")}return(0,f.decrypt)(a,b,c,o.p2c,d)}case"A128KW":case"A192KW":case"A256KW":if(void 0===c)throw new i.JWEInvalid("JWE Encrypted Key missing");return(0,d.unwrap)(a,b,c);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let d,e;if(void 0===c)throw new i.JWEInvalid("JWE Encrypted Key missing");if("string"!=typeof o.iv)throw new i.JWEInvalid('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof o.tag)throw new i.JWEInvalid('JOSE Header "tag" (Authentication Tag) missing or invalid');try{d=(0,h.decode)(o.iv)}catch{throw new i.JWEInvalid("Failed to base64url decode the iv")}try{e=(0,h.decode)(o.tag)}catch{throw new i.JWEInvalid("Failed to base64url decode the tag")}return(0,n.unwrap)(a,b,c,d,e)}default:throw new i.JOSENotSupported('Invalid or unsupported "alg" (JWE Algorithm) header value')}}},44726:a=>{a.exports=function(a){try{return -1!==Function.toString.call(a).indexOf("[native code]")}catch(b){return"function"==typeof a}},a.exports.__esModule=!0,a.exports.default=a.exports},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44999:(a,b,c)=>{"use strict";c.r(b),c.d(b,{__esModule:()=>d.B,cookies:()=>d.U,draftMode:()=>f.r,headers:()=>e.b});var d=c(99933),e=c(86280),f=c(73913)},45495:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.GeneralSign=void 0;let d=c(59310),e=c(99938);class f{constructor(a,b,c){this.parent=a,this.key=b,this.options=c}setProtectedHeader(a){if(this.protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this.protectedHeader=a,this}setUnprotectedHeader(a){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=a,this}addSignature(...a){return this.parent.addSignature(...a)}sign(...a){return this.parent.sign(...a)}done(){return this.parent}}class g{constructor(a){this._signatures=[],this._payload=a}addSignature(a,b){let c=new f(this,a,b);return this._signatures.push(c),c}async sign(){if(!this._signatures.length)throw new e.JWSInvalid("at least one signature must be added");let a={signatures:[],payload:""};for(let b=0;b<this._signatures.length;b++){let c=this._signatures[b],f=new d.FlattenedSign(this._payload);f.setProtectedHeader(c.protectedHeader),f.setUnprotectedHeader(c.unprotectedHeader);let{payload:g,...h}=await f.sign(c.key,c.options);if(0===b)a.payload=g;else if(a.payload!==g)throw new e.JWSInvalid("inconsistent use of JWS Unencoded Payload (RFC7797)");a.signatures.push(h)}return a}}b.GeneralSign=g},45787:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.EncryptJWT=void 0;let d=c(52118),e=c(2594),f=c(2754);class g extends f.ProduceJWT{setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setKeyManagementParameters(a){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=a,this}setContentEncryptionKey(a){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=a,this}setInitializationVector(a){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=a,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(a,b){let c=new d.CompactEncrypt(e.encoder.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),c.setProtectedHeader(this._protectedHeader),this._iv&&c.setInitializationVector(this._iv),this._cek&&c.setContentEncryptionKey(this._cek),this._keyManagementParameters&&c.setKeyManagementParameters(this._keyManagementParameters),c.encrypt(a,b)}}b.EncryptJWT=g},46821:(a,b,c)=>{"use strict";let d;Object.defineProperty(b,"__esModule",{value:!0});let e=c(55511),f=c(71800);"function"!=typeof e.hkdf||process.versions.electron||(d=async(...a)=>new Promise((b,c)=>{e.hkdf(...a,(a,d)=>{a?c(a):b(new Uint8Array(d))})})),b.default=async(a,b,c,e,g)=>(d||f.default)(a,b,c,e,g)},47024:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){let{url:b,csrfToken:c,theme:e}=a;return(0,d.h)("div",{className:"signout"},e.brandColor&&(0,d.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${e.brandColor}
        }
      `}}),e.buttonText&&(0,d.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${e.buttonText}
        }
      `}}),(0,d.h)("div",{className:"card"},e.logo&&(0,d.h)("img",{src:e.logo,alt:"Logo",className:"logo"}),(0,d.h)("h1",null,"Signout"),(0,d.h)("p",null,"Are you sure you want to sign out?"),(0,d.h)("form",{action:`${b}/signout`,method:"POST"},(0,d.h)("input",{type:"hidden",name:"csrfToken",value:c}),(0,d.h)("button",{id:"submitButton",type:"submit"},"Sign out"))))};var d=c(1441)},48160:(a,b,c)=>{let{STATUS_CODES:d}=c(81630),{format:e}=c(28354),{OPError:f}=c(61408),g=c(38700);a.exports=function(a,{statusCode:b=200,body:c=!0,bearer:h=!1}={}){if(a.statusCode!==b){if(h&&(a=>{let b=g(a.headers["www-authenticate"]);if(b.error)throw new f(b,a)})(a),(a=>{let b=!1;try{let c;c="object"!=typeof a.body||Buffer.isBuffer(a.body)?JSON.parse(a.body):a.body,(b="string"==typeof c.error&&c.error.length)&&Object.defineProperty(a,"body",{value:c,configurable:!0})}catch(a){}return b})(a))throw new f(a.body,a);throw new f({error:e("expected %i %s, got: %i %s",b,d[b],a.statusCode,d[a.statusCode])},a)}if(c&&!a.body)throw new f({error:e("expected %i %s with body but no body was returned",b,d[b])},a);return a.body}},48513:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.SignJWT=void 0;let d=c(99312),e=c(99938),f=c(2594),g=c(2754);class h extends g.ProduceJWT{setProtectedHeader(a){return this._protectedHeader=a,this}async sign(a,b){var c;let g=new d.CompactSign(f.encoder.encode(JSON.stringify(this._payload)));if(g.setProtectedHeader(this._protectedHeader),Array.isArray(null==(c=this._protectedHeader)?void 0:c.crit)&&this._protectedHeader.crit.includes("b64")&&!1===this._protectedHeader.b64)throw new e.JWTInvalid("JWTs MUST NOT use unencoded payload");return g.sign(a,b)}}b.SignJWT=h},48807:a=>{function b(a,b,c,d,e,f,g){try{var h=a[f](g),i=h.value}catch(a){return void c(a)}h.done?b(i):Promise.resolve(i).then(d,e)}a.exports=function(a){return function(){var c=this,d=arguments;return new Promise(function(e,f){var g=a.apply(c,d);function h(a){b(g,e,f,h,i,"next",a)}function i(a){b(g,e,f,h,i,"throw",a)}h(void 0)})}},a.exports.__esModule=!0,a.exports.default=a.exports},49305:(a,b,c)=>{var d=c(24956).default;a.exports=function(a){if(null!=a){var b=a["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],c=0;if(b)return b.call(a);if("function"==typeof a.next)return a;if(!isNaN(a.length))return{next:function(){return a&&c>=a.length&&(a=void 0),{value:a&&a[c++],done:!a}}}}throw TypeError(d(a)+" is not iterable")},a.exports.__esModule=!0,a.exports.default=a.exports},49450:(a,b,c)=>{let d=c(37544),{RPError:e}=c(61408),{assertIssuerConfiguration:f}=c(2279),{random:g}=c(35571),h=c(16156),i=c(9552),{keystores:j}=c(55132),k=c(86505),l=a=>encodeURIComponent(a).replace(/%20/g,"+");async function m(a,b){let c=this[`${a}_endpoint_auth_signing_alg`];if(c||f(this.issuer,`${a}_endpoint_auth_signing_alg_values_supported`),"client_secret_jwt"===this[`${a}_endpoint_auth_method`]){if(!c){let b=this.issuer[`${a}_endpoint_auth_signing_alg_values_supported`];c=Array.isArray(b)&&b.find(a=>/^HS(?:256|384|512)/.test(a))}if(!c)throw new e(`failed to determine a JWS Algorithm to use for ${this[`${a}_endpoint_auth_method`]} Client Assertion`);return new d.CompactSign(Buffer.from(JSON.stringify(b))).setProtectedHeader({alg:c}).sign(this.secretForAlg(c))}let g=await j.get(this);if(!g)throw TypeError("no client jwks provided for signing a client assertion with");if(!c){let b=this.issuer[`${a}_endpoint_auth_signing_alg_values_supported`];c=Array.isArray(b)&&b.find(a=>g.get({alg:a,use:"sig"}))}if(!c)throw new e(`failed to determine a JWS Algorithm to use for ${this[`${a}_endpoint_auth_method`]} Client Assertion`);let h=g.get({alg:c,use:"sig"});if(!h)throw new e(`no key found in client jwks to sign a client assertion with using alg ${c}`);return new d.CompactSign(Buffer.from(JSON.stringify(b))).setProtectedHeader({alg:c,kid:h.jwk&&h.jwk.kid}).sign(await h.keyObject(c))}async function n(a,{clientAssertionPayload:b}={}){switch(this[`${a}_endpoint_auth_method`]){case"self_signed_tls_client_auth":case"tls_client_auth":case"none":return{form:{client_id:this.client_id}};case"client_secret_post":if("string"!=typeof this.client_secret)throw TypeError("client_secret_post client authentication method requires a client_secret");return{form:{client_id:this.client_id,client_secret:this.client_secret}};case"private_key_jwt":case"client_secret_jwt":{let c=h(),d=await m.call(this,a,{iat:c,exp:c+60,jti:g(),iss:this.client_id,sub:this.client_id,aud:this.issuer.issuer,...b});return{form:{client_id:this.client_id,client_assertion:d,client_assertion_type:"urn:ietf:params:oauth:client-assertion-type:jwt-bearer"}}}case"client_secret_basic":{if("string"!=typeof this.client_secret)throw TypeError("client_secret_basic client authentication method requires a client_secret");let a=`${l(this.client_id)}:${l(this.client_secret)}`,b=Buffer.from(a).toString("base64");return{headers:{Authorization:`Basic ${b}`}}}default:throw TypeError(`missing, or unsupported, ${a}_endpoint_auth_method`)}}async function o(a,b,{clientAssertionPayload:c,endpointAuthMethod:d=a,DPoP:e}={}){let f,g=k(b,await n.call(this,d,{clientAssertionPayload:c})),h=this[`${d}_endpoint_auth_method`].includes("tls_client_auth")||"token"===a&&this.tls_client_certificate_bound_access_tokens;if(h&&this.issuer.mtls_endpoint_aliases&&(f=this.issuer.mtls_endpoint_aliases[`${a}_endpoint`]),f=f||this.issuer[`${a}_endpoint`],"form"in g)for(let[a,b]of Object.entries(g.form))void 0===b&&delete g.form[a];return i.call(this,{...g,method:"POST",url:f,headers:{..."revocation"!==a?{Accept:"application/json"}:void 0,...g.headers}},{mTLS:h,DPoP:e})}a.exports={resolveResponseType:function(){let{length:a,0:b}=this.response_types;if(1===a)return b},resolveRedirectUri:function(){let{length:a,0:b}=this.redirect_uris||[];if(1===a)return b},authFor:n,authenticatedPost:o}},49701:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){return{headers:[{key:"Content-Type",value:"application/json"}],body:a.reduce((a,{id:b,name:c,type:d,signinUrl:e,callbackUrl:f})=>(a[b]={id:b,name:c,type:d,signinUrl:e,callbackUrl:f},a),{})}}},50003:(a,b)=>{"use strict";function c(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function d(a,b){return a.name===b}function e(a){return parseInt(a.name.slice(4),10)}function f(a,b){if(b.length&&!b.some(b=>a.usages.includes(b))){let a="CryptoKey does not support this operation, its usages must include ";if(b.length>2){let c=b.pop();a+=`one of ${b.join(", ")}, or ${c}.`}else 2===b.length?a+=`one of ${b[0]} or ${b[1]}.`:a+=`${b[0]}.`;throw TypeError(a)}}Object.defineProperty(b,"__esModule",{value:!0}),b.checkEncCryptoKey=b.checkSigCryptoKey=void 0,b.checkSigCryptoKey=function(a,b,...g){switch(b){case"HS256":case"HS384":case"HS512":{if(!d(a.algorithm,"HMAC"))throw c("HMAC");let f=parseInt(b.slice(2),10);if(e(a.algorithm.hash)!==f)throw c(`SHA-${f}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!d(a.algorithm,"RSASSA-PKCS1-v1_5"))throw c("RSASSA-PKCS1-v1_5");let f=parseInt(b.slice(2),10);if(e(a.algorithm.hash)!==f)throw c(`SHA-${f}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!d(a.algorithm,"RSA-PSS"))throw c("RSA-PSS");let f=parseInt(b.slice(2),10);if(e(a.algorithm.hash)!==f)throw c(`SHA-${f}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==a.algorithm.name&&"Ed448"!==a.algorithm.name)throw c("Ed25519 or Ed448");break;case"ES256":case"ES384":case"ES512":{if(!d(a.algorithm,"ECDSA"))throw c("ECDSA");let e=function(a){switch(a){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(b);if(a.algorithm.namedCurve!==e)throw c(e,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}f(a,g)},b.checkEncCryptoKey=function(a,b,...g){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!d(a.algorithm,"AES-GCM"))throw c("AES-GCM");let e=parseInt(b.slice(1,4),10);if(a.algorithm.length!==e)throw c(e,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!d(a.algorithm,"AES-KW"))throw c("AES-KW");let e=parseInt(b.slice(1,4),10);if(a.algorithm.length!==e)throw c(e,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw c("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!d(a.algorithm,"PBKDF2"))throw c("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!d(a.algorithm,"RSA-OAEP"))throw c("RSA-OAEP");let f=parseInt(b.slice(9),10)||1;if(e(a.algorithm.hash)!==f)throw c(`SHA-${f}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}f(a,g)}},50671:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.pkce=b.nonce=b.PKCE_CODE_CHALLENGE_METHOD=void 0,b.signCookie=g,b.state=void 0;var d=c(12107),e=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=f(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&({}).hasOwnProperty.call(a,g)){var h=e?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(15912));function f(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(f=function(a){return a?c:b})(a)}async function g(a,b,c,d){let{cookies:f,logger:g}=d;g.debug(`CREATE_${a.toUpperCase()}`,{value:b,maxAge:c});let{name:h}=f[a],i=new Date;return i.setTime(i.getTime()+1e3*c),{name:h,value:await e.encode({...d.jwt,maxAge:c,token:{value:b},salt:h}),options:{...f[a].options,expires:i}}}let h=b.PKCE_CODE_CHALLENGE_METHOD="S256";b.pkce={async create(a,b,c){var e,f;if(!(null!=(e=a.provider)&&null!=(e=e.checks)&&e.includes("pkce")))return;let i=d.generators.codeVerifier();c.code_challenge=d.generators.codeChallenge(i),c.code_challenge_method=h;let j=null!=(f=a.cookies.pkceCodeVerifier.options.maxAge)?f:900;b.push(await g("pkceCodeVerifier",i,j,a))},async use(a,b,c,d){var f;if(!(null!=(f=c.provider)&&null!=(f=f.checks)&&f.includes("pkce")))return;let g=null==a?void 0:a[c.cookies.pkceCodeVerifier.name];if(!g)throw TypeError("PKCE code_verifier cookie was missing.");let{name:h}=c.cookies.pkceCodeVerifier,i=await e.decode({...c.jwt,token:g,salt:h});if(!(null!=i&&i.value))throw TypeError("PKCE code_verifier value could not be parsed.");b.push({name:h,value:"",options:{...c.cookies.pkceCodeVerifier.options,maxAge:0}}),d.code_verifier=i.value}},b.state={async create(a,b,c){var e,f;if(!(null!=(e=a.provider.checks)&&e.includes("state")))return;let h=d.generators.state();c.state=h;let i=null!=(f=a.cookies.state.options.maxAge)?f:900;b.push(await g("state",h,i,a))},async use(a,b,c,d){var f;if(!(null!=(f=c.provider.checks)&&f.includes("state")))return;let g=null==a?void 0:a[c.cookies.state.name];if(!g)throw TypeError("State cookie was missing.");let{name:h}=c.cookies.state,i=await e.decode({...c.jwt,token:g,salt:h});if(!(null!=i&&i.value))throw TypeError("State value could not be parsed.");b.push({name:h,value:"",options:{...c.cookies.state.options,maxAge:0}}),d.state=i.value}},b.nonce={async create(a,b,c){var e,f;if(!(null!=(e=a.provider.checks)&&e.includes("nonce")))return;let h=d.generators.nonce();c.nonce=h;let i=null!=(f=a.cookies.nonce.options.maxAge)?f:900;b.push(await g("nonce",h,i,a))},async use(a,b,c,d){var f;if(!(null!=(f=c.provider)&&null!=(f=f.checks)&&f.includes("nonce")))return;let g=null==a?void 0:a[c.cookies.nonce.name];if(!g)throw TypeError("Nonce cookie was missing.");let{name:h}=c.cookies.nonce,i=await e.decode({...c.jwt,token:g,salt:h});if(!(null!=i&&i.value))throw TypeError("Nonce value could not be parsed.");b.push({name:h,value:"",options:{...c.cookies.nonce.options,maxAge:0}}),d.nonce=i.value}}},51688:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.generateKeyPair=b.generateSecret=void 0;let d=c(55511),e=c(28354),f=c(7552),g=c(78328),h=c(99938),i=(0,e.promisify)(d.generateKeyPair);b.generateSecret=async function(a,b){let c;switch(a){case"HS256":case"HS384":case"HS512":case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":c=parseInt(a.slice(-3),10);break;case"A128KW":case"A192KW":case"A256KW":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":case"A128GCM":case"A192GCM":case"A256GCM":c=parseInt(a.slice(1,4),10);break;default:throw new h.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return(0,d.createSecretKey)((0,f.default)(new Uint8Array(c>>3)))},b.generateKeyPair=async function(a,b){var c,d;switch(a){case"RS256":case"RS384":case"RS512":case"PS256":case"PS384":case"PS512":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":case"RSA1_5":{let a=null!=(c=null==b?void 0:b.modulusLength)?c:2048;if("number"!=typeof a||a<2048)throw new h.JOSENotSupported("Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used");let d=await i("rsa",{modulusLength:a,publicExponent:65537});return(0,g.setModulusLength)(d.privateKey,a),(0,g.setModulusLength)(d.publicKey,a),d}case"ES256":return i("ec",{namedCurve:"P-256"});case"ES256K":return i("ec",{namedCurve:"secp256k1"});case"ES384":return i("ec",{namedCurve:"P-384"});case"ES512":return i("ec",{namedCurve:"P-521"});case"EdDSA":switch(null==b?void 0:b.crv){case void 0:case"Ed25519":return i("ed25519");case"Ed448":return i("ed448");default:throw new h.JOSENotSupported("Invalid or unsupported crv option provided, supported values are Ed25519 and Ed448")}case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":let e=null!=(d=null==b?void 0:b.crv)?d:"P-256";switch(e){case void 0:case"P-256":case"P-384":case"P-521":return i("ec",{namedCurve:e});case"X25519":return i("x25519");case"X448":return i("x448");default:throw new h.JOSENotSupported("Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448")}default:throw new h.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}}},51800:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938),e=c(90061);b.default=(a,b)=>{let c;switch(a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":c=parseInt(a.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":c=parseInt(a.slice(1,4),10);break;default:throw new d.JOSENotSupported(`Content Encryption Algorithm ${a} is not supported either by JOSE or your javascript runtime`)}if(b instanceof Uint8Array){let a=b.byteLength<<3;if(a!==c)throw new d.JWEInvalid(`Invalid Content Encryption Key length. Expected ${c} bits, got ${a} bits`);return}if((0,e.default)(b)&&"secret"===b.type){let a=b.symmetricKeySize<<3;if(a!==c)throw new d.JWEInvalid(`Invalid Content Encryption Key length. Expected ${c} bits, got ${a} bits`);return}throw TypeError("Invalid Content Encryption Key type")}},52118:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.CompactEncrypt=void 0;let d=c(14988);class e{constructor(a){this._flattened=new d.FlattenedEncrypt(a)}setContentEncryptionKey(a){return this._flattened.setContentEncryptionKey(a),this}setInitializationVector(a){return this._flattened.setInitializationVector(a),this}setProtectedHeader(a){return this._flattened.setProtectedHeader(a),this}setKeyManagementParameters(a){return this._flattened.setKeyManagementParameters(a),this}async encrypt(a,b){let c=await this._flattened.encrypt(a,b);return[c.protected,c.encrypted_key,c.iv,c.ciphertext,c.tag].join(".")}}b.CompactEncrypt=e},52131:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.ecdhAllowed=b.generateEpk=b.deriveKey=void 0;let d=c(55511),e=c(28354),f=c(62909),g=c(2594),h=c(99938),i=c(17996),j=c(50003),k=c(90061),l=c(78680),m=c(37265),n=(0,e.promisify)(d.generateKeyPair);b.deriveKey=async function(a,b,c,e,f=new Uint8Array(0),h=new Uint8Array(0)){let n,o;if((0,i.isCryptoKey)(a))(0,j.checkEncCryptoKey)(a,"ECDH"),n=d.KeyObject.from(a);else if((0,k.default)(a))n=a;else throw TypeError((0,l.default)(a,...m.types));if((0,i.isCryptoKey)(b))(0,j.checkEncCryptoKey)(b,"ECDH","deriveBits"),o=d.KeyObject.from(b);else if((0,k.default)(b))o=b;else throw TypeError((0,l.default)(b,...m.types));let p=(0,g.concat)((0,g.lengthAndInput)(g.encoder.encode(c)),(0,g.lengthAndInput)(f),(0,g.lengthAndInput)(h),(0,g.uint32be)(e)),q=(0,d.diffieHellman)({privateKey:o,publicKey:n});return(0,g.concatKdf)(q,e,p)},b.generateEpk=async function(a){let b;if((0,i.isCryptoKey)(a))b=d.KeyObject.from(a);else if((0,k.default)(a))b=a;else throw TypeError((0,l.default)(a,...m.types));switch(b.asymmetricKeyType){case"x25519":return n("x25519");case"x448":return n("x448");case"ec":return n("ec",{namedCurve:(0,f.default)(b)});default:throw new h.JOSENotSupported("Invalid or unsupported EPK")}},b.ecdhAllowed=a=>["P-256","P-384","P-521","X25519","X448"].includes((0,f.default)(a))},52645:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.decodeJwt=void 0;let d=c(67929),e=c(2594),f=c(25110),g=c(99938);b.decodeJwt=function(a){let b,c;if("string"!=typeof a)throw new g.JWTInvalid("JWTs must use Compact JWS serialization, JWT must be a string");let{1:h,length:i}=a.split(".");if(5===i)throw new g.JWTInvalid("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new g.JWTInvalid("Invalid JWT");if(!h)throw new g.JWTInvalid("JWTs must contain a payload");try{b=(0,d.decode)(h)}catch{throw new g.JWTInvalid("Failed to base64url decode the payload")}try{c=JSON.parse(e.decoder.decode(b))}catch{throw new g.JWTInvalid("Failed to parse the decoded payload as JSON")}if(!(0,f.default)(c))throw new g.JWTInvalid("Invalid JWT Claims Set");return c}},53687:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(79428),e=c(55511),f=c(68327),g=c(99938),h=c(62909),i=c(78328),j=c(4275),k=c(80028);b.default=a=>{if(k.jwkImport&&"oct"!==a.kty)return a.d?(0,e.createPrivateKey)({format:"jwk",key:a}):(0,e.createPublicKey)({format:"jwk",key:a});switch(a.kty){case"oct":return(0,e.createSecretKey)((0,f.decode)(a.k));case"RSA":{let b=new j.default,c=void 0!==a.d,f=d.Buffer.from(a.n,"base64"),g=d.Buffer.from(a.e,"base64");c?(b.zero(),b.unsignedInteger(f),b.unsignedInteger(g),b.unsignedInteger(d.Buffer.from(a.d,"base64")),b.unsignedInteger(d.Buffer.from(a.p,"base64")),b.unsignedInteger(d.Buffer.from(a.q,"base64")),b.unsignedInteger(d.Buffer.from(a.dp,"base64")),b.unsignedInteger(d.Buffer.from(a.dq,"base64")),b.unsignedInteger(d.Buffer.from(a.qi,"base64"))):(b.unsignedInteger(f),b.unsignedInteger(g));let h={key:b.end(),format:"der",type:"pkcs1"},k=c?(0,e.createPrivateKey)(h):(0,e.createPublicKey)(h);return(0,i.setModulusLength)(k,f.length<<3),k}case"EC":{let b=new j.default,c=void 0!==a.d,f=d.Buffer.concat([d.Buffer.alloc(1,4),d.Buffer.from(a.x,"base64"),d.Buffer.from(a.y,"base64")]);if(c){b.zero();let c=new j.default;c.oidFor("ecPublicKey"),c.oidFor(a.crv),b.add(c.end());let g=new j.default;g.one(),g.octStr(d.Buffer.from(a.d,"base64"));let i=new j.default;i.bitStr(f);let k=i.end(d.Buffer.from([161]));g.add(k);let l=g.end(),m=new j.default;m.add(l);let n=m.end(d.Buffer.from([4]));b.add(n);let o=b.end(),p=(0,e.createPrivateKey)({key:o,format:"der",type:"pkcs8"});return(0,h.setCurve)(p,a.crv),p}let g=new j.default;g.oidFor("ecPublicKey"),g.oidFor(a.crv),b.add(g.end()),b.bitStr(f);let i=b.end(),k=(0,e.createPublicKey)({key:i,format:"der",type:"spki"});return(0,h.setCurve)(k,a.crv),k}case"OKP":{let b=new j.default;if(void 0!==a.d){b.zero();let c=new j.default;c.oidFor(a.crv),b.add(c.end());let f=new j.default;f.octStr(d.Buffer.from(a.d,"base64"));let g=f.end(d.Buffer.from([4]));b.add(g);let h=b.end();return(0,e.createPrivateKey)({key:h,format:"der",type:"pkcs8"})}let c=new j.default;c.oidFor(a.crv),b.add(c.end()),b.bitStr(d.Buffer.from(a.x,"base64"));let f=b.end();return(0,e.createPublicKey)({key:f,format:"der",type:"spki"})}default:throw new g.JOSENotSupported('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}}},53873:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=j;var d=c(12107),e=c(9545),f=c(8365),g=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=i(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var f in a)if("default"!==f&&({}).hasOwnProperty.call(a,f)){var g=e?Object.getOwnPropertyDescriptor(a,f):null;g&&(g.get||g.set)?Object.defineProperty(d,f,g):d[f]=a[f]}return d.default=a,c&&c.set(a,d),d}(c(50671)),h=c(20113);function i(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(i=function(a){return a?c:b})(a)}async function j(a){var b,c,i,j,l,m;let{options:n,query:o,body:p,method:q,cookies:r}=a,{logger:s,provider:t}=n,u=null!=(b=null==p?void 0:p.error)?b:null==o?void 0:o.error;if(u){let a=Error(u);throw s.error("OAUTH_CALLBACK_HANDLER_ERROR",{error:a,error_description:null==o?void 0:o.error_description,providerId:t.id}),s.debug("OAUTH_CALLBACK_HANDLER_ERROR",{body:p}),a}if(null!=(c=t.version)&&c.startsWith("1."))try{let a=await (0,f.oAuth1Client)(n),{oauth_token:b,oauth_verifier:c}=null!=o?o:{},d=await a.getOAuthAccessToken(b,f.oAuth1TokenStore.get(b),c),e=await a.get(t.profileUrl,d.oauth_token,d.oauth_token_secret);return"string"==typeof e&&(e=JSON.parse(e)),{...await k({profile:e,tokens:d,provider:t,logger:s}),cookies:[]}}catch(a){throw s.error("OAUTH_V1_GET_ACCESS_TOKEN_ERROR",a),a}null!=o&&o.oauth_token&&f.oAuth1TokenStore.delete(o.oauth_token);try{let a,b,c=await (0,e.openidClient)(n),f={},h=[];await g.state.use(r,h,n,f),await g.pkce.use(r,h,n,f),await g.nonce.use(r,h,n,f);let u={...c.callbackParams({url:`http://n?${new URLSearchParams(o)}`,body:p,method:q}),...null==(i=t.token)?void 0:i.params};if(null!=(j=t.token)&&j.request){let b=await t.token.request({provider:t,params:u,checks:f,client:c});a=new d.TokenSet(b.tokens)}else a=t.idToken?await c.callback(t.callbackUrl,u,f):await c.oauthCallback(t.callbackUrl,u,f);return Array.isArray(a.scope)&&(a.scope=a.scope.join(" ")),b=null!=(l=t.userinfo)&&l.request?await t.userinfo.request({provider:t,tokens:a,client:c}):t.idToken?a.claims():await c.userinfo(a,{params:null==(m=t.userinfo)?void 0:m.params}),{...await k({profile:b,provider:t,tokens:a,logger:s}),cookies:h}}catch(a){throw new h.OAuthCallbackError(a)}}async function k({profile:a,tokens:b,provider:c,logger:d}){try{var e;d.debug("PROFILE_DATA",{OAuthProfile:a});let f=await c.profile(a,b);if(f.email=null==(e=f.email)?void 0:e.toLowerCase(),!f.id)throw TypeError(`Profile id is missing in ${c.name} OAuth profile response`);return{profile:f,account:{provider:c.id,type:c.type,providerAccountId:f.id.toString(),...b},OAuthProfile:a}}catch(b){d.error("OAUTH_PARSE_PROFILE_ERROR",{error:b,OAuthProfile:a})}}},54688:(a,b)=>{"use strict";async function c(a){var b,c;let{options:d,sessionStore:e}=a,{adapter:f,events:g,jwt:h,callbackUrl:i,logger:j,session:k}=d,l=null==e?void 0:e.value;if(!l)return{redirect:i};if("jwt"===k.strategy)try{let a=await h.decode({...h,token:l});await (null==(b=g.signOut)?void 0:b.call(g,{token:a}))}catch(a){j.error("SIGNOUT_ERROR",a)}else try{let a=await f.deleteSession(l);await (null==(c=g.signOut)?void 0:c.call(g,{session:a}))}catch(a){j.error("SIGNOUT_ERROR",a)}return{redirect:i,cookies:e.clean()}}Object.defineProperty(b,"__esModule",{value:!0}),b.default=c},55132:a=>{a.exports.keystores=new WeakMap},55437:a=>{a.exports=function(a,b){if(!(a instanceof b))throw TypeError("Cannot call a class as a function")},a.exports.__esModule=!0,a.exports.default=a.exports},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},55920:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.init=r;var e=c(55511),f=d(c(28510)),g=c(20113),h=d(c(64470)),i=c(29837),j=q(c(99632)),k=q(c(15912)),l=c(68588),m=c(89412),n=c(61665),o=d(c(16851));function p(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(p=function(a){return a?c:b})(a)}function q(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=p(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var f in a)if("default"!==f&&({}).hasOwnProperty.call(a,f)){var g=e?Object.getOwnPropertyDescriptor(a,f):null;g&&(g.get||g.set)?Object.defineProperty(d,f,g):d[f]=a[f]}return d.default=a,c&&c.set(a,d),d}async function r({authOptions:a,providerId:b,action:c,origin:d,cookies:p,callbackUrl:q,csrfToken:r,isPost:s}){var t,u;let v=(0,o.default)(d),w=(0,i.createSecret)({authOptions:a,url:v}),{providers:x,provider:y}=(0,h.default)({providers:a.providers,url:v,providerId:b}),z={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...a,url:v,action:c,provider:y,cookies:{...j.defaultCookies(null!=(t=a.useSecureCookies)?t:v.base.startsWith("https://")),...a.cookies},secret:w,providers:x,session:{strategy:a.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>{var a;return null!=(a=null===e.randomUUID||void 0===e.randomUUID?void 0:(0,e.randomUUID)())?a:(0,e.randomBytes)(32).toString("hex")},...a.session},jwt:{secret:w,maxAge:2592e3,encode:k.encode,decode:k.decode,...a.jwt},events:(0,g.eventsErrorHandler)(null!=(u=a.events)?u:{},f.default),adapter:(0,g.adapterErrorHandler)(a.adapter,f.default),callbacks:{...l.defaultCallbacks,...a.callbacks},logger:f.default,callbackUrl:v.origin},A=[],{csrfToken:B,cookie:C,csrfTokenVerified:D}=(0,m.createCSRFToken)({options:z,cookieValue:null==p?void 0:p[z.cookies.csrfToken.name],isPost:s,bodyValue:r});z.csrfToken=B,z.csrfTokenVerified=D,C&&A.push({name:z.cookies.csrfToken.name,value:C,options:z.cookies.csrfToken.options});let{callbackUrl:E,callbackUrlCookie:F}=await (0,n.createCallbackUrl)({options:z,cookieValue:null==p?void 0:p[z.cookies.callbackUrl.name],paramValue:q});return z.callbackUrl=E,F&&A.push({name:z.cookies.callbackUrl.name,value:F,options:z.cookies.callbackUrl.options}),{options:z,cookies:A}}},56913:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.decrypt=b.encrypt=void 0;let d=c(55511),e=c(78328),f=c(17996),g=c(50003),h=c(90061),i=c(78680),j=c(37265),k=(a,b)=>{if("rsa"!==a.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");(0,e.default)(a,b)},l=a=>{switch(a){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return d.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return d.constants.RSA_PKCS1_PADDING;default:return}},m=a=>{switch(a){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function n(a,b,...c){if((0,h.default)(a))return a;if((0,f.isCryptoKey)(a))return(0,g.checkEncCryptoKey)(a,b,...c),d.KeyObject.from(a);throw TypeError((0,i.default)(a,...j.types))}b.encrypt=(a,b,c)=>{let e=l(a),f=m(a),g=n(b,a,"wrapKey","encrypt");return k(g,a),(0,d.publicEncrypt)({key:g,oaepHash:f,padding:e},c)},b.decrypt=(a,b,c)=>{let e=l(a),f=m(a),g=n(b,a,"unwrapKey","decrypt");return k(g,a),(0,d.privateDecrypt)({key:g,oaepHash:f,padding:e},c)}},57502:a=>{function b(c){return a.exports=b=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},a.exports.__esModule=!0,a.exports.default=a.exports,b(c)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},58513:(a,b,c)=>{let d=c(79551),{format:e}=c(28354),f=c(93929),{RPError:g,OPError:h}=c(61408),{BaseClient:i}=c(83872),{random:j,codeChallenge:k}=c(35571),l=c(24740),{resolveResponseType:m,resolveRedirectUri:n}=c(49450);function o(a,b,c={}){a?this.error(a):b?this.success(b,c):this.fail(c)}function p({client:a,params:b={},passReqToCallback:c=!1,sessionKey:e,usePKCE:g=!0,extras:h={}}={},j){if(!(a instanceof i))throw TypeError("client must be an instance of openid-client Client");if("function"!=typeof j)throw TypeError("verify callback must be a function");if(!a.issuer||!a.issuer.issuer)throw TypeError("client must have an issuer with an identifier");if(this._client=a,this._issuer=a.issuer,this._verify=j,this._passReqToCallback=c,this._usePKCE=g,this._key=e||`oidc:${d.parse(this._issuer.issuer).hostname}`,this._params=f(b),delete this._params.state,delete this._params.nonce,this._extras=f(h),this._params.response_type||(this._params.response_type=m.call(a)),this._params.redirect_uri||(this._params.redirect_uri=n.call(a)),this._params.scope||(this._params.scope="openid"),!0===this._usePKCE){let a=!!Array.isArray(this._issuer.code_challenge_methods_supported)&&this._issuer.code_challenge_methods_supported;if(a&&a.includes("S256"))this._usePKCE="S256";else if(a&&a.includes("plain"))this._usePKCE="plain";else if(a)throw TypeError("neither code_challenge_method supported by the client is supported by the issuer");else this._usePKCE="S256"}else if("string"==typeof this._usePKCE&&!["plain","S256"].includes(this._usePKCE))throw TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);this.name=d.parse(a.issuer.issuer).hostname}p.prototype.authenticate=function(a,b){(async()=>{let c=this._client;if(!a.session)throw TypeError("authentication requires session support");let d=c.callbackParams(a),f=this._key,{0:h,length:i}=Object.keys(d);if(0===i||1===i&&"iss"===h){let d={state:j(),...this._params,...b};if(!d.nonce&&d.response_type.includes("id_token")&&(d.nonce=j()),a.session[f]=l(d,"nonce","state","max_age","response_type"),this._usePKCE&&d.response_type.includes("code")){let b=j();switch(a.session[f].code_verifier=b,this._usePKCE){case"S256":d.code_challenge=k(b),d.code_challenge_method="S256";break;case"plain":d.code_challenge=b}}this.redirect(c.authorizationUrl(d));return}let m=a.session[f];if(0===Object.keys(m||{}).length)throw Error(e('did not find expected authorization request details in session, req.session["%s"] is %j',f,m));let{state:n,nonce:p,max_age:q,code_verifier:r,response_type:s}=m;try{delete a.session[f]}catch(a){}let t={redirect_uri:this._params.redirect_uri,...b},u=await c.callback(t.redirect_uri,d,{state:n,nonce:p,max_age:q,code_verifier:r,response_type:s},this._extras),v=this._passReqToCallback,w=this._verify.length>(v?3:2)&&c.issuer.userinfo_endpoint,x=[u,o.bind(this)];if(w){if(!u.access_token)throw new g({message:"expected access_token to be returned when asking for userinfo in verify callback",tokenset:u});let a=await c.userinfo(u);x.splice(1,0,a)}v&&x.unshift(a),this._verify(...x)})().catch(a=>{a instanceof h&&"server_error"!==a.error&&!a.error.startsWith("invalid")||a instanceof g?this.fail(a):this.error(a)})},a.exports=p},58985:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=f;var d=c(55511),e=c(29837);async function f(a,b){var c,f,g,h;let{url:i,adapter:j,provider:k,callbackUrl:l,theme:m}=b,n=null!=(c=await (null==(f=k.generateVerificationToken)?void 0:f.call(k)))?c:(0,d.randomBytes)(32).toString("hex"),o=new Date(Date.now()+(null!=(g=k.maxAge)?g:86400)*1e3),p=new URLSearchParams({callbackUrl:l,token:n,email:a}),q=`${i}/callback/${k.id}?${p}`;return await Promise.all([k.sendVerificationRequest({identifier:a,token:n,expires:o,url:q,provider:k,theme:m}),null==(h=j.createVerificationToken)?void 0:h.call(j,{identifier:a,token:(0,e.hashToken)(n,b),expires:o})]),`${i}/verify-request?${new URLSearchParams({provider:k.id,type:k.type})}`}},59310:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.FlattenedSign=void 0;let d=c(68327),e=c(92562),f=c(65597),g=c(99938),h=c(2594),i=c(80335),j=c(8535);class k{constructor(a){if(!(a instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=a}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}async sign(a,b){let c;if(!this._protectedHeader&&!this._unprotectedHeader)throw new g.JWSInvalid("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,f.default)(this._protectedHeader,this._unprotectedHeader))throw new g.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let k={...this._protectedHeader,...this._unprotectedHeader},l=(0,j.default)(g.JWSInvalid,new Map([["b64",!0]]),null==b?void 0:b.crit,this._protectedHeader,k),m=!0;if(l.has("b64")&&"boolean"!=typeof(m=this._protectedHeader.b64))throw new g.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:n}=k;if("string"!=typeof n||!n)throw new g.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,i.default)(n,a,"sign");let o=this._payload;m&&(o=h.encoder.encode((0,d.encode)(o))),c=this._protectedHeader?h.encoder.encode((0,d.encode)(JSON.stringify(this._protectedHeader))):h.encoder.encode("");let p=(0,h.concat)(c,h.encoder.encode("."),o),q=await (0,e.default)(n,a,p),r={signature:(0,d.encode)(q),payload:""};return m&&(r.payload=h.decoder.decode(o)),this._unprotectedHeader&&(r.header=this._unprotectedHeader),this._protectedHeader&&(r.protected=h.decoder.decode(c)),r}}b.FlattenedSign=k},59487:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"callback",{enumerable:!0,get:function(){return e.default}}),Object.defineProperty(b,"providers",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(b,"session",{enumerable:!0,get:function(){return h.default}}),Object.defineProperty(b,"signin",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(b,"signout",{enumerable:!0,get:function(){return g.default}});var e=d(c(99296)),f=d(c(22429)),g=d(c(54688)),h=d(c(71871)),i=d(c(49701))},59496:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=f;var d=c(20113),e=c(29837);async function f(a){var b,c,f,g,h,i;let{sessionToken:j,profile:k,account:l,options:m}=a;if(!(null!=l&&l.providerAccountId)||!l.type)throw Error("Missing or invalid provider account");if(!["email","oauth"].includes(l.type))throw Error("Provider not supported");let{adapter:n,jwt:o,events:p,session:{strategy:q,generateSessionToken:r}}=m;if(!n)return{user:k,account:l};let{createUser:s,updateUser:t,getUser:u,getUserByAccount:v,getUserByEmail:w,linkAccount:x,createSession:y,getSessionAndUser:z,deleteSession:A}=n,B=null,C=null,D=!1,E="jwt"===q;if(j)if(E)try{(B=await o.decode({...o,token:j}))&&"sub"in B&&B.sub&&(C=await u(B.sub))}catch(a){}else{let a=await z(j);a&&(B=a.session,C=a.user)}if("email"===l.type){let a=await w(k.email);if(a)(null==(b=C)?void 0:b.id)!==a.id&&!E&&j&&await A(j),C=await t({id:a.id,emailVerified:new Date}),await (null==(c=p.updateUser)?void 0:c.call(p,{user:C}));else{let{id:a,...b}={...k,emailVerified:new Date};C=await s(b),await (null==(f=p.createUser)?void 0:f.call(p,{user:C})),D=!0}return{session:B=E?{}:await y({sessionToken:await r(),userId:C.id,expires:(0,e.fromDate)(m.session.maxAge)}),user:C,isNewUser:D}}if("oauth"===l.type){let a=await v({providerAccountId:l.providerAccountId,provider:l.provider});if(a){if(C){if(a.id===C.id)return{session:B,user:C,isNewUser:D};throw new d.AccountNotLinkedError("The account is already associated with another user")}return{session:B=E?{}:await y({sessionToken:await r(),userId:a.id,expires:(0,e.fromDate)(m.session.maxAge)}),user:a,isNewUser:D}}{if(C)return await x({...l,userId:C.id}),await (null==(i=p.linkAccount)?void 0:i.call(p,{user:C,account:l,profile:k})),{session:B,user:C,isNewUser:D};let a=k.email?await w(k.email):null;if(a){let b=m.provider;if(null!=b&&b.allowDangerousEmailAccountLinking)C=a;else throw new d.AccountNotLinkedError("Another account already exists with the same e-mail address")}else{let{id:a,...b}={...k,emailVerified:null};C=await s(b)}return await (null==(g=p.createUser)?void 0:g.call(p,{user:C})),await x({...l,userId:C.id}),await (null==(h=p.linkAccount)?void 0:h.call(p,{user:C,account:l,profile:k})),{session:B=E?{}:await y({sessionToken:await r(),userId:C.id,expires:(0,e.fromDate)(m.session.maxAge)}),user:C,isNewUser:!0}}}throw Error("Unsupported account type")}},60105:a=>{a.exports=function(a){if(void 0===a)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return a},a.exports.__esModule=!0,a.exports.default=a.exports},61325:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>x,POST:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(19854),v=c.n(u),w=c(12909);let x=v()(w.Nh),y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},distDir:".next",projectDir:"",resolvedPagePath:"E:\\aidevcommerce\\src\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/auth/[...nextauth]/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},61408:(a,b,c)=>{let{format:d}=c(28354);class e extends Error{constructor({error_description:a,error:b,error_uri:c,session_state:d,state:e,scope:f},g){super(a?`${b} (${a})`:b),Object.assign(this,{error:b},a&&{error_description:a},c&&{error_uri:c},e&&{state:e},f&&{scope:f},d&&{session_state:d}),g&&Object.defineProperty(this,"response",{value:g}),this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}class f extends Error{constructor(...a){if("string"==typeof a[0])super(d(...a));else{let{message:b,printf:c,response:e,...f}=a[0];c?super(d(...c)):super(b),Object.assign(this,f),e&&Object.defineProperty(this,"response",{value:e})}this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}a.exports={OPError:e,RPError:f}},61665:(a,b)=>{"use strict";async function c({options:a,paramValue:b,cookieValue:c}){let{url:d,callbacks:e}=a,f=d.origin;return b?f=await e.redirect({url:b,baseUrl:d.origin}):c&&(f=await e.redirect({url:c,baseUrl:d.origin})),{callbackUrl:f,callbackUrlCookie:f!==c?f:void 0}}Object.defineProperty(b,"__esModule",{value:!0}),b.createCallbackUrl=c},61689:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.bitLength=void 0;let d=c(99938),e=c(7552);function f(a){switch(a){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new d.JOSENotSupported(`Unsupported JWE Algorithm: ${a}`)}}b.bitLength=f,b.default=a=>(0,e.default)(new Uint8Array(f(a)>>3))},62197:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.decrypt=b.encrypt=void 0;let d=c(28354),e=c(55511),f=c(7552),g=c(2594),h=c(68327),i=c(88062),j=c(92296),k=c(17996),l=c(50003),m=c(90061),n=c(78680),o=c(37265),p=(0,d.promisify)(e.pbkdf2);function q(a,b){if((0,m.default)(a))return a.export();if(a instanceof Uint8Array)return a;if((0,k.isCryptoKey)(a))return(0,l.checkEncCryptoKey)(a,b,"deriveBits","deriveKey"),e.KeyObject.from(a).export();throw TypeError((0,n.default)(a,...o.types,"Uint8Array"))}b.encrypt=async(a,b,c,d=2048,e=(0,f.default)(new Uint8Array(16)))=>{(0,j.default)(e);let k=(0,g.p2s)(a,e),l=parseInt(a.slice(13,16),10)>>3,m=q(b,a),n=await p(m,k,d,l,`sha${a.slice(8,11)}`);return{encryptedKey:await (0,i.wrap)(a.slice(-6),n,c),p2c:d,p2s:(0,h.encode)(e)}},b.decrypt=async(a,b,c,d,e)=>{(0,j.default)(e);let f=(0,g.p2s)(a,e),h=parseInt(a.slice(13,16),10)>>3,k=q(b,a),l=await p(k,f,d,h,`sha${a.slice(8,11)}`);return(0,i.unwrap)(a.slice(-6),l,c)}},62909:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.setCurve=b.weakMap=void 0;let d=c(79428),e=c(55511),f=c(99938),g=c(17996),h=c(90061),i=c(78680),j=c(37265),k=d.Buffer.from([42,134,72,206,61,3,1,7]),l=d.Buffer.from([43,129,4,0,34]),m=d.Buffer.from([43,129,4,0,35]),n=d.Buffer.from([43,129,4,0,10]);b.weakMap=new WeakMap;let o=(a,c)=>{var d;let p;if((0,g.isCryptoKey)(a))p=e.KeyObject.from(a);else if((0,h.default)(a))p=a;else throw TypeError((0,i.default)(a,...j.types));if("secret"===p.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(p.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${p.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${p.asymmetricKeyType.slice(1)}`;case"ec":{if(b.weakMap.has(p))return b.weakMap.get(p);let a=null==(d=p.asymmetricKeyDetails)?void 0:d.namedCurve;if(a||"private"!==p.type){if(!a){let b=p.export({format:"der",type:"spki"}),c=b[1]<128?14:15,d=b[c],e=b.slice(c+1,c+1+d);if(e.equals(k))a="prime256v1";else if(e.equals(l))a="secp384r1";else if(e.equals(m))a="secp521r1";else if(e.equals(n))a="secp256k1";else throw new f.JOSENotSupported("Unsupported key curve for this operation")}}else a=o((0,e.createPublicKey)(p),!0);if(c)return a;let g=(a=>{switch(a){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new f.JOSENotSupported("Unsupported key curve for this operation")}})(a);return b.weakMap.set(p,g),g}default:throw TypeError("Invalid asymmetric key type for this operation")}};b.setCurve=function(a,c){b.weakMap.set(a,c)},b.default=o},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63580:a=>{"use strict";a.exports=function(a){a.prototype[Symbol.iterator]=function*(){for(let a=this.head;a;a=a.next)yield a.value}}},63667:a=>{"use strict";a.exports=JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}')},63898:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938);b.default=function(a){switch(a){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"EdDSA":return;default:throw new d.JOSENotSupported(`alg ${a} is not supported either by JOSE or your javascript runtime`)}}},64127:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.GeneralEncrypt=void 0;let d=c(14988),e=c(99938),f=c(61689),g=c(65597),h=c(22599),i=c(68327),j=c(8535);class k{constructor(a,b,c){this.parent=a,this.key=b,this.options=c}setUnprotectedHeader(a){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=a,this}addRecipient(...a){return this.parent.addRecipient(...a)}encrypt(...a){return this.parent.encrypt(...a)}done(){return this.parent}}class l{constructor(a){this._recipients=[],this._plaintext=a}addRecipient(a,b){let c=new k(this,a,{crit:null==b?void 0:b.crit});return this._recipients.push(c),c}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setSharedUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}setAdditionalAuthenticatedData(a){return this._aad=a,this}async encrypt(a){var b,c,k;let l;if(!this._recipients.length)throw new e.JWEInvalid("at least one recipient must be added");if(a={deflateRaw:null==a?void 0:a.deflateRaw},1===this._recipients.length){let[b]=this._recipients,c=await new d.FlattenedEncrypt(this._plaintext).setAdditionalAuthenticatedData(this._aad).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(b.unprotectedHeader).encrypt(b.key,{...b.options,...a}),e={ciphertext:c.ciphertext,iv:c.iv,recipients:[{}],tag:c.tag};return c.aad&&(e.aad=c.aad),c.protected&&(e.protected=c.protected),c.unprotected&&(e.unprotected=c.unprotected),c.encrypted_key&&(e.recipients[0].encrypted_key=c.encrypted_key),c.header&&(e.recipients[0].header=c.header),e}for(let a=0;a<this._recipients.length;a++){let b=this._recipients[a];if(!(0,g.default)(this._protectedHeader,this._unprotectedHeader,b.unprotectedHeader))throw new e.JWEInvalid("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let c={...this._protectedHeader,...this._unprotectedHeader,...b.unprotectedHeader},{alg:d}=c;if("string"!=typeof d||!d)throw new e.JWEInvalid('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("dir"===d||"ECDH-ES"===d)throw new e.JWEInvalid('"dir" and "ECDH-ES" alg may only be used with a single recipient');if("string"!=typeof c.enc||!c.enc)throw new e.JWEInvalid('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(l){if(l!==c.enc)throw new e.JWEInvalid('JWE "enc" (Encryption Algorithm) Header Parameter must be the same for all recipients')}else l=c.enc;if((0,j.default)(e.JWEInvalid,new Map,b.options.crit,this._protectedHeader,c),void 0!==c.zip&&(!this._protectedHeader||!this._protectedHeader.zip))throw new e.JWEInvalid('JWE "zip" (Compression Algorithm) Header MUST be integrity protected')}let m=(0,f.default)(l),n={ciphertext:"",iv:"",recipients:[],tag:""};for(let e=0;e<this._recipients.length;e++){let f=this._recipients[e],g={};n.recipients.push(g);let j=({...this._protectedHeader,...this._unprotectedHeader,...f.unprotectedHeader}).alg.startsWith("PBES2")?2048+e:void 0;if(0===e){let b=await new d.FlattenedEncrypt(this._plaintext).setAdditionalAuthenticatedData(this._aad).setContentEncryptionKey(m).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(f.unprotectedHeader).setKeyManagementParameters({p2c:j}).encrypt(f.key,{...f.options,...a,[d.unprotected]:!0});n.ciphertext=b.ciphertext,n.iv=b.iv,n.tag=b.tag,b.aad&&(n.aad=b.aad),b.protected&&(n.protected=b.protected),b.unprotected&&(n.unprotected=b.unprotected),g.encrypted_key=b.encrypted_key,b.header&&(g.header=b.header);continue}let{encryptedKey:o,parameters:p}=await (0,h.default)((null==(b=f.unprotectedHeader)?void 0:b.alg)||(null==(c=this._protectedHeader)?void 0:c.alg)||(null==(k=this._unprotectedHeader)?void 0:k.alg),l,f.key,m,{p2c:j});g.encrypted_key=(0,i.encode)(o),(f.unprotectedHeader||p)&&(g.header={...f.unprotectedHeader,...p})}return n}}b.GeneralEncrypt=l},64150:a=>{let b=/^\d+$/;a.exports=function(a){if("string"!=typeof a)throw TypeError("input must be a string");return(!function(a){if(a.includes("://"))return!0;let c=a.replace(/(\/|\?)/g,"#").split("#")[0];if(c.includes(":")){let a=c.indexOf(":"),d=c.slice(a+1);if(!b.test(d))return!0}return!1}(a)?!function(a){if(!a.includes("@"))return!1;let b=a.split("@"),c=b[b.length-1];return!(c.includes(":")||c.includes("/")||c.includes("?"))}(a)?`https://${a}`:`acct:${a}`:a).split("#")[0]}},64470:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){let{url:b,providerId:c}=a,f=a.providers.map(({options:a,...c})=>{var f,g;if("oauth"===c.type){let f=e(c),h=e(a,!0),i=null!=(g=null==h?void 0:h.id)?g:c.id;return(0,d.merge)(f,{...h,signinUrl:`${b}/signin/${i}`,callbackUrl:`${b}/callback/${i}`})}let h=null!=(f=null==a?void 0:a.id)?f:c.id;return(0,d.merge)(c,{...a,signinUrl:`${b}/signin/${h}`,callbackUrl:`${b}/callback/${h}`})});return{providers:f,provider:f.find(({id:a})=>a===c)}};var d=c(6680);function e(a,b=!1){var c,d,f,g,h;if(!a)return;let i=Object.entries(a).reduce((a,[b,c])=>{if(["authorization","token","userinfo"].includes(b)&&"string"==typeof c){var d;let e=new URL(c);a[b]={url:`${e.origin}${e.pathname}`,params:Object.fromEntries(null!=(d=e.searchParams)?d:[])}}else a[b]=c;return a},{});return b||null!=(c=i.version)&&c.startsWith("1.")||(i.idToken=!!(null!=(d=null!=(f=i.idToken)?f:null==(g=i.wellKnown)?void 0:g.includes("openid-configuration"))?d:null==(h=i.authorization)||null==(h=h.params)||null==(h=h.scope)?void 0:h.includes("openid")),i.checks||(i.checks=["state"])),i}},64872:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.AuthHandler=q;var e=n(c(28510)),f=c(42856),g=n(c(59487)),h=d(c(70959)),i=c(55920),j=c(67804),k=c(99632),l=c(25538);function m(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(m=function(a){return a?c:b})(a)}function n(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=m(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var f in a)if("default"!==f&&({}).hasOwnProperty.call(a,f)){var g=e?Object.getOwnPropertyDescriptor(a,f):null;g&&(g.get||g.set)?Object.defineProperty(d,f,g):d[f]=a[f]}return d.default=a,c&&c.set(a,d),d}async function o(a){try{return await a.json()}catch(a){}}async function p(a){var b,c,d,e;if(a instanceof Request){let b=new URL(a.url),g=b.pathname.split("/").slice(3),h=Object.fromEntries(a.headers),i=Object.fromEntries(b.searchParams);return i.nextauth=g,{action:g[0],method:a.method,headers:h,body:await o(a),cookies:(0,l.parse)(null!=(c=a.headers.get("cookie"))?c:""),providerId:g[1],error:null!=(d=b.searchParams.get("error"))?d:g[1],origin:(0,f.detectOrigin)(null!=(e=h["x-forwarded-host"])?e:h.host,h["x-forwarded-proto"]),query:i}}let{headers:g}=a,h=null!=(b=null==g?void 0:g["x-forwarded-host"])?b:null==g?void 0:g.host;return a.origin=(0,f.detectOrigin)(h,null==g?void 0:g["x-forwarded-proto"]),a}async function q(a){var b,c,d,f,l,m,n;let{options:o,req:q}=a,r=await p(q);(0,e.setLogger)(o.logger,o.debug);let s=(0,j.assertConfig)({options:o,req:r});if(Array.isArray(s))s.forEach(e.default.warn);else if(s instanceof Error){if(e.default.error(s.code,s),!["signin","signout","error","verify-request"].includes(r.action)||"GET"!==r.method)return{status:500,headers:[{key:"Content-Type",value:"application/json"}],body:{message:"There is a problem with the server configuration. Check the server logs for more information."}};let{pages:a,theme:b}=o,c=(null==a?void 0:a.error)&&(null==(l=r.query)||null==(l=l.callbackUrl)?void 0:l.startsWith(a.error));return!(null!=a&&a.error)||c?(c&&e.default.error("AUTH_ON_ERROR_PAGE_ERROR",Error(`The error page ${null==a?void 0:a.error} should not require authentication`)),(0,h.default)({theme:b}).error({error:"configuration"})):{redirect:`${a.error}?error=Configuration`}}let{action:t,providerId:u,error:v,method:w="GET"}=r,{options:x,cookies:y}=await (0,i.init)({authOptions:o,action:t,providerId:u,origin:r.origin,callbackUrl:null!=(b=null==(c=r.body)?void 0:c.callbackUrl)?b:null==(d=r.query)?void 0:d.callbackUrl,csrfToken:null==(f=r.body)?void 0:f.csrfToken,cookies:r.cookies,isPost:"POST"===w}),z=new k.SessionStore(x.cookies.sessionToken,r,x.logger);if("GET"===w){let a=(0,h.default)({...x,query:r.query,cookies:y}),{pages:b}=x;switch(t){case"providers":return await g.providers(x.providers);case"session":{let a=await g.session({options:x,sessionStore:z});return a.cookies&&y.push(...a.cookies),{...a,cookies:y}}case"csrf":return{headers:[{key:"Content-Type",value:"application/json"}],body:{csrfToken:x.csrfToken},cookies:y};case"signin":if(b.signIn){let a=`${b.signIn}${b.signIn.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(x.callbackUrl)}`;return v&&(a=`${a}&error=${encodeURIComponent(v)}`),{redirect:a,cookies:y}}return a.signin();case"signout":if(b.signOut)return{redirect:b.signOut,cookies:y};return a.signout();case"callback":if(x.provider){let a=await g.callback({body:r.body,query:r.query,headers:r.headers,cookies:r.cookies,method:w,options:x,sessionStore:z});return a.cookies&&y.push(...a.cookies),{...a,cookies:y}}break;case"verify-request":if(b.verifyRequest)return{redirect:b.verifyRequest,cookies:y};return a.verifyRequest();case"error":if(["Signin","OAuthSignin","OAuthCallback","OAuthCreateAccount","EmailCreateAccount","Callback","OAuthAccountNotLinked","EmailSignin","CredentialsSignin","SessionRequired"].includes(v))return{redirect:`${x.url}/signin?error=${v}`,cookies:y};if(b.error)return{redirect:`${b.error}${b.error.includes("?")?"&":"?"}error=${v}`,cookies:y};return a.error({error:v})}}else if("POST"===w)switch(t){case"signin":if(x.csrfTokenVerified&&x.provider){let a=await g.signin({query:r.query,body:r.body,options:x});return a.cookies&&y.push(...a.cookies),{...a,cookies:y}}return{redirect:`${x.url}/signin?csrf=true`,cookies:y};case"signout":if(x.csrfTokenVerified){let a=await g.signout({options:x,sessionStore:z});return a.cookies&&y.push(...a.cookies),{...a,cookies:y}}return{redirect:`${x.url}/signout?csrf=true`,cookies:y};case"callback":if(x.provider){if("credentials"===x.provider.type&&!x.csrfTokenVerified)return{redirect:`${x.url}/signin?csrf=true`,cookies:y};let a=await g.callback({body:r.body,query:r.query,headers:r.headers,cookies:r.cookies,method:w,options:x,sessionStore:z});return a.cookies&&y.push(...a.cookies),{...a,cookies:y}}break;case"_log":if(o.logger)try{let{code:a,level:b,...c}=null!=(m=r.body)?m:{};e.default[b](a,c)}catch(a){e.default.error("LOGGER_ERROR",a)}return{};case"session":if(x.csrfTokenVerified){let a=await g.session({options:x,sessionStore:z,newSession:null==(n=r.body)?void 0:n.data,isUpdate:!0});return a.cookies&&y.push(...a.cookies),{...a,cookies:y}}return{status:400,body:{},cookies:y}}return{status:400,body:`Error: This action with HTTP ${w} is not supported by NextAuth.js`}}},65597:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0}},65764:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.exportJWK=b.exportPKCS8=b.exportSPKI=void 0;let d=c(93346),e=c(93346),f=c(41193);b.exportSPKI=async function(a){return(0,d.toSPKI)(a)},b.exportPKCS8=async function(a){return(0,e.toPKCS8)(a)},b.exportJWK=async function(a){return(0,f.default)(a)}},66318:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.deflate=b.inflate=void 0;let d=c(28354),e=c(74075),f=c(99938),g=(0,d.promisify)(e.inflateRaw),h=(0,d.promisify)(e.deflateRaw);b.inflate=a=>g(a,{maxOutputLength:25e4}).catch(()=>{throw new f.JWEDecompressionFailed}),b.deflate=a=>h(a)},66446:a=>{a.exports=function(a,b){this.v=a,this.k=b},a.exports.__esModule=!0,a.exports.default=a.exports},66837:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=h;var d=c(9545),e=c(8365),f=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=g(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var f in a)if("default"!==f&&({}).hasOwnProperty.call(a,f)){var h=e?Object.getOwnPropertyDescriptor(a,f):null;h&&(h.get||h.set)?Object.defineProperty(d,f,h):d[f]=a[f]}return d.default=a,c&&c.set(a,d),d}(c(50671));function g(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(g=function(a){return a?c:b})(a)}async function h({options:a,query:b}){var c,g,h;let{logger:i,provider:j}=a,k={};if("string"==typeof j.authorization){let a=Object.fromEntries(new URL(j.authorization).searchParams);k={...k,...a}}else k={...k,...null==(g=j.authorization)?void 0:g.params};if(k={...k,...b},null!=(c=j.version)&&c.startsWith("1.")){let b=(0,e.oAuth1Client)(a),c=await b.getOAuthRequestToken(k),d=`${null==(h=j.authorization)?void 0:h.url}?${new URLSearchParams({oauth_token:c.oauth_token,oauth_token_secret:c.oauth_token_secret,...c.params})}`;return e.oAuth1TokenStore.set(c.oauth_token,c.oauth_token_secret),i.debug("GET_AUTHORIZATION_URL",{url:d,provider:j}),{redirect:d}}let l=await (0,d.openidClient)(a),m=k,n=[];await f.state.create(a,n,m),await f.pkce.create(a,n,m),await f.nonce.create(a,n,m);let o=l.authorizationUrl(m);return i.debug("GET_AUTHORIZATION_URL",{url:o,cookies:n,provider:j}),{redirect:o,cookies:n}}},66946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(30898),e=c(42471),f=c(47912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},67131:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default="node:crypto"},67135:a=>{a.exports=function(a){var b=Object(a),c=[];for(var d in b)c.unshift(d);return function a(){for(;c.length;)if((d=c.pop())in b)return a.value=d,a.done=!1,a;return a.done=!0,a}},a.exports.__esModule=!0,a.exports.default=a.exports},67804:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.assertConfig=function(a){var b,c,d,j,k,l,m;let n,o,p,{options:q,req:r}=a,s=[];if(!h&&(r.origin||s.push("NEXTAUTH_URL"),q.secret,q.debug&&s.push("DEBUG_ENABLED")),!q.secret)return new e.MissingSecret("Please define a `secret` in production.");if(!(null!=(b=r.query)&&b.nextauth)&&!r.action)return new e.MissingAPIRoute("Cannot find [...nextauth].{js,ts} in `/pages/api/auth`. Make sure the filename is written correctly.");let t=null==(c=r.query)?void 0:c.callbackUrl,u=(0,f.default)(r.origin);if(t&&!i(t,u.base))return new e.InvalidCallbackUrl(`Invalid callback URL. Received: ${t}`);let{callbackUrl:v}=(0,g.defaultCookies)(null!=(d=q.useSecureCookies)?d:u.base.startsWith("https://")),w=null==(j=r.cookies)?void 0:j[null!=(k=null==(l=q.cookies)||null==(l=l.callbackUrl)?void 0:l.name)?k:v.name];if(w&&!i(w,u.base))return new e.InvalidCallbackUrl(`Invalid callback URL. Received: ${w}`);for(let a of q.providers)"credentials"===a.type?n=!0:"email"===a.type?o=!0:"twitter"===a.id&&"2.0"===a.version&&(p=!0);if(n){let a=(null==(m=q.session)?void 0:m.strategy)==="database",b=!q.providers.some(a=>"credentials"!==a.type);if(a&&b)return new e.UnsupportedStrategy("Signin in with credentials only supported if JWT strategy is enabled");if(q.providers.some(a=>"credentials"===a.type&&!a.authorize))return new e.MissingAuthorize("Must define an authorize() handler to use credentials authentication provider")}if(o){let{adapter:a}=q;if(!a)return new e.MissingAdapter("E-mail login requires an adapter.");let b=["createVerificationToken","useVerificationToken","getUserByEmail"].filter(b=>!a[b]);if(b.length)return new e.MissingAdapterMethods(`Required adapter methods were missing: ${b.join(", ")}`)}return h||(p&&s.push("TWITTER_OAUTH_2_BETA"),h=!0),s};var e=c(20113),f=d(c(16851)),g=c(99632);let h=!1;function i(a,b){try{return/^https?:/.test(new URL(a,a.startsWith("/")?b:void 0).protocol)}catch(a){return!1}}},67929:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.decode=b.encode=void 0;let d=c(68327);b.encode=d.encode,b.decode=d.decode},68327:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.decode=b.encode=b.encodeBase64=b.decodeBase64=void 0;let d=c(79428),e=c(2594);d.Buffer.isEncoding("base64url")?b.encode=a=>d.Buffer.from(a).toString("base64url"):b.encode=a=>d.Buffer.from(a).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),b.decodeBase64=a=>d.Buffer.from(a,"base64"),b.encodeBase64=a=>d.Buffer.from(a).toString("base64"),b.decode=a=>d.Buffer.from(function(a){let b=a;return b instanceof Uint8Array&&(b=e.decoder.decode(b)),b}(a),"base64")},68588:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.defaultCallbacks=void 0,b.defaultCallbacks={signIn:()=>!0,redirect:({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b,session:({session:a})=>a,jwt:({token:a})=>a}},68907:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.generalDecrypt=void 0;let d=c(36736),e=c(99938),f=c(25110);b.generalDecrypt=async function(a,b,c){if(!(0,f.default)(a))throw new e.JWEInvalid("General JWE must be an object");if(!Array.isArray(a.recipients)||!a.recipients.every(f.default))throw new e.JWEInvalid("JWE Recipients missing or incorrect type");if(!a.recipients.length)throw new e.JWEInvalid("JWE Recipients has no members");for(let e of a.recipients)try{return await (0,d.flattenedDecrypt)({aad:a.aad,ciphertext:a.ciphertext,encrypted_key:e.encrypted_key,header:e.header,iv:a.iv,protected:a.protected,tag:a.tag,unprotected:a.unprotected},b,c)}catch{}throw new e.JWEDecryptionFailed}},68919:(a,b,c)=>{a.exports=c(1889).default},69184:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.decodeProtectedHeader=void 0;let d=c(67929),e=c(2594),f=c(25110);b.decodeProtectedHeader=function(a){let b;if("string"==typeof a){let c=a.split(".");(3===c.length||5===c.length)&&([b]=c)}else if("object"==typeof a&&a)if("protected"in a)b=a.protected;else throw TypeError("Token does not contain a Protected Header");try{if("string"!=typeof b||!b)throw Error();let a=JSON.parse(e.decoder.decode((0,d.decode)(b)));if(!(0,f.default)(a))throw Error();return a}catch{throw TypeError("Invalid Token or Protected Header formatting")}}},69717:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=a=>Math.floor(a.getTime()/1e3)},70885:a=>{let b;b=Buffer.isEncoding("base64url")?(a,b="utf8")=>Buffer.from(a,b).toString("base64url"):(a,b="utf8")=>Buffer.from(a,b).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),a.exports.decode=a=>Buffer.from(a,"base64"),a.exports.encode=b},70959:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a){let{url:b,theme:c,query:d,cookies:k}=a;function l({html:a,title:b,status:d}){var f;return{cookies:k,status:d,headers:[{key:"Content-Type",value:"text/html"}],body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${(0,j.default)()}</style><title>${b}</title></head><body class="__next-auth-theme-${null!=(f=null==c?void 0:c.colorScheme)?f:"auto"}"><div class="page">${(0,e.default)(a)}</div></body></html>`}}return{signin:b=>l({html:(0,f.default)({csrfToken:a.csrfToken,providers:a.providers,callbackUrl:a.callbackUrl,theme:c,...d,...b}),title:"Sign In"}),signout:d=>l({html:(0,g.default)({csrfToken:a.csrfToken,url:b,theme:c,...d}),title:"Sign Out"}),verifyRequest:a=>l({html:(0,h.default)({url:b,theme:c,...a}),title:"Verify Request"}),error:a=>l({...(0,i.default)({url:b,theme:c,...a}),title:"Error"})}};var e=d(c(68919)),f=d(c(8557)),g=d(c(47024)),h=d(c(16974)),i=d(c(27969)),j=d(c(84838))},70997:(a,b,c)=>{var d=c(30994);a.exports=function(a,b){if("function"!=typeof b&&null!==b)throw TypeError("Super expression must either be null or a function");a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),b&&d(a,b)},a.exports.__esModule=!0,a.exports.default=a.exports},71746:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.flattenedVerify=void 0;let d=c(68327),e=c(9534),f=c(99938),g=c(2594),h=c(65597),i=c(25110),j=c(80335),k=c(8535),l=c(93069);b.flattenedVerify=async function(a,b,c){var m;let n,o;if(!(0,i.default)(a))throw new f.JWSInvalid("Flattened JWS must be an object");if(void 0===a.protected&&void 0===a.header)throw new f.JWSInvalid('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==a.protected&&"string"!=typeof a.protected)throw new f.JWSInvalid("JWS Protected Header incorrect type");if(void 0===a.payload)throw new f.JWSInvalid("JWS Payload missing");if("string"!=typeof a.signature)throw new f.JWSInvalid("JWS Signature missing or incorrect type");if(void 0!==a.header&&!(0,i.default)(a.header))throw new f.JWSInvalid("JWS Unprotected Header incorrect type");let p={};if(a.protected)try{let b=(0,d.decode)(a.protected);p=JSON.parse(g.decoder.decode(b))}catch{throw new f.JWSInvalid("JWS Protected Header is invalid")}if(!(0,h.default)(p,a.header))throw new f.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let q={...p,...a.header},r=(0,k.default)(f.JWSInvalid,new Map([["b64",!0]]),null==c?void 0:c.crit,p,q),s=!0;if(r.has("b64")&&"boolean"!=typeof(s=p.b64))throw new f.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:t}=q;if("string"!=typeof t||!t)throw new f.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');let u=c&&(0,l.default)("algorithms",c.algorithms);if(u&&!u.has(t))throw new f.JOSEAlgNotAllowed('"alg" (Algorithm) Header Parameter not allowed');if(s){if("string"!=typeof a.payload)throw new f.JWSInvalid("JWS Payload must be a string")}else if("string"!=typeof a.payload&&!(a.payload instanceof Uint8Array))throw new f.JWSInvalid("JWS Payload must be a string or an Uint8Array instance");let v=!1;"function"==typeof b&&(b=await b(p,a),v=!0),(0,j.default)(t,b,"verify");let w=(0,g.concat)(g.encoder.encode(null!=(m=a.protected)?m:""),g.encoder.encode("."),"string"==typeof a.payload?g.encoder.encode(a.payload):a.payload);try{n=(0,d.decode)(a.signature)}catch{throw new f.JWSInvalid("Failed to base64url decode the signature")}if(!await (0,e.default)(t,b,n,w))throw new f.JWSSignatureVerificationFailed;if(s)try{o=(0,d.decode)(a.payload)}catch{throw new f.JWSInvalid("Failed to base64url decode the payload")}else o="string"==typeof a.payload?g.encoder.encode(a.payload):a.payload;let x={payload:o};return(void 0!==a.protected&&(x.protectedHeader=p),void 0!==a.header&&(x.unprotectedHeader=a.header),v)?{...x,key:b}:x}},71800:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511);b.default=(a,b,c,e,f)=>{let g=parseInt(a.substr(3),10)>>3||20,h=(0,d.createHmac)(a,c.byteLength?c:new Uint8Array(g)).update(b).digest(),i=Math.ceil(f/g),j=new Uint8Array(g*i+e.byteLength+1),k=0,l=0;for(let b=1;b<=i;b++)j.set(e,l),j[l+e.byteLength]=b,j.set((0,d.createHmac)(a,h).update(j.subarray(k,l+e.byteLength+1)).digest(),l),k=l,l+=g;return j.slice(0,f)}},71871:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=e;var d=c(29837);async function e(a){var b,c,e,f,g,h;let{options:i,sessionStore:j,newSession:k,isUpdate:l}=a,{adapter:m,jwt:n,events:o,callbacks:p,logger:q,session:{strategy:r,maxAge:s}}=i,t={body:{},headers:[{key:"Content-Type",value:"application/json"}],cookies:[]},u=j.value;if(!u)return t;if("jwt"===r)try{let a=await n.decode({...n,token:u});if(!a)throw Error("JWT invalid");let e=await p.jwt({token:a,...l&&{trigger:"update"},session:k}),f=(0,d.fromDate)(s),g=await p.session({session:{user:{name:null==a?void 0:a.name,email:null==a?void 0:a.email,image:null==a?void 0:a.picture},expires:f.toISOString()},token:e});t.body=g;let h=await n.encode({...n,token:e,maxAge:i.session.maxAge}),m=j.chunk(h,{expires:f});null==(b=t.cookies)||b.push(...m),await (null==(c=o.session)?void 0:c.call(o,{session:g,token:e}))}catch(a){q.error("JWT_SESSION_ERROR",a),null==(e=t.cookies)||e.push(...j.clean())}else try{let{getSessionAndUser:a,deleteSession:b,updateSession:c}=m,e=await a(u);if(e&&e.session.expires.valueOf()<Date.now()&&(await b(u),e=null),e){let{user:a,session:b}=e,h=i.session.updateAge,j=b.expires.valueOf()-1e3*s+1e3*h,m=(0,d.fromDate)(s);j<=Date.now()&&await c({sessionToken:u,expires:m});let n=await p.session({session:{user:{name:a.name,email:a.email,image:a.image},expires:b.expires.toISOString()},user:a,newSession:k,...l?{trigger:"update"}:{}});t.body=n,null==(f=t.cookies)||f.push({name:i.cookies.sessionToken.name,value:u,options:{...i.cookies.sessionToken.options,expires:m}}),await (null==(g=o.session)?void 0:g.call(o,{session:n}))}else u&&(null==(h=t.cookies)||h.push(...j.clean()))}catch(a){q.error("SESSION_ERROR",a)}return t}},71872:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.UnsecuredJWT=void 0;let d=c(68327),e=c(2594),f=c(99938),g=c(81822),h=c(2754);class i extends h.ProduceJWT{encode(){let a=d.encode(JSON.stringify({alg:"none"})),b=d.encode(JSON.stringify(this._payload));return`${a}.${b}.`}static decode(a,b){let c;if("string"!=typeof a)throw new f.JWTInvalid("Unsecured JWT must be a string");let{0:h,1:i,2:j,length:k}=a.split(".");if(3!==k||""!==j)throw new f.JWTInvalid("Invalid Unsecured JWT");try{if(c=JSON.parse(e.decoder.decode(d.decode(h))),"none"!==c.alg)throw Error()}catch{throw new f.JWTInvalid("Invalid Unsecured JWT")}return{payload:(0,g.default)(c,d.decode(i),b),header:c}}}b.UnsecuredJWT=i},72115:(a,b,c)=>{let d=c(78830);function e(a,b,...c){for(let f of c)if(d(f))for(let[c,g]of Object.entries(f))"__proto__"!==c&&"constructor"!==c&&(void 0===b[c]&&void 0!==g&&(b[c]=g),a&&d(b[c])&&d(g)&&e(!0,b[c],g));return b}a.exports=e.bind(void 0,!1),a.exports.deep=e.bind(void 0,!0)},72443:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});class c{constructor(a){if(48!==a[0]||(this.buffer=a,this.offset=1,this.decodeLength()!==a.length-this.offset))throw TypeError()}decodeLength(){let a=this.buffer[this.offset++];if(128&a){let b=-129&a;a=0;for(let c=0;c<b;c++)a=a<<8|this.buffer[this.offset+c];this.offset+=b}return a}unsignedInteger(){if(2!==this.buffer[this.offset++])throw TypeError();let a=this.decodeLength();0===this.buffer[this.offset]&&(this.offset++,a--);let b=this.buffer.slice(this.offset,this.offset+a);return this.offset+=a,b}end(){if(this.offset!==this.buffer.length)throw TypeError()}}b.default=c},72921:(a,b,c)=>{"use strict";let d,e;c.r(b),c.d(b,{NIL:()=>w,parse:()=>r,stringify:()=>n,v1:()=>q,v3:()=>t,v4:()=>u,v5:()=>v,validate:()=>l,version:()=>x});var f=c(55511),g=c.n(f);let h=new Uint8Array(256),i=h.length;function j(){return i>h.length-16&&(g().randomFillSync(h),i=0),h.slice(i,i+=16)}let k=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,l=function(a){return"string"==typeof a&&k.test(a)},m=[];for(let a=0;a<256;++a)m.push((a+256).toString(16).substr(1));let n=function(a,b=0){let c=(m[a[b+0]]+m[a[b+1]]+m[a[b+2]]+m[a[b+3]]+"-"+m[a[b+4]]+m[a[b+5]]+"-"+m[a[b+6]]+m[a[b+7]]+"-"+m[a[b+8]]+m[a[b+9]]+"-"+m[a[b+10]]+m[a[b+11]]+m[a[b+12]]+m[a[b+13]]+m[a[b+14]]+m[a[b+15]]).toLowerCase();if(!l(c))throw TypeError("Stringified UUID is invalid");return c},o=0,p=0,q=function(a,b,c){let f=b&&c||0,g=b||Array(16),h=(a=a||{}).node||d,i=void 0!==a.clockseq?a.clockseq:e;if(null==h||null==i){let b=a.random||(a.rng||j)();null==h&&(h=d=[1|b[0],b[1],b[2],b[3],b[4],b[5]]),null==i&&(i=e=(b[6]<<8|b[7])&16383)}let k=void 0!==a.msecs?a.msecs:Date.now(),l=void 0!==a.nsecs?a.nsecs:p+1,m=k-o+(l-p)/1e4;if(m<0&&void 0===a.clockseq&&(i=i+1&16383),(m<0||k>o)&&void 0===a.nsecs&&(l=0),l>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");o=k,p=l,e=i;let q=((0xfffffff&(k+=122192928e5))*1e4+l)%0x100000000;g[f++]=q>>>24&255,g[f++]=q>>>16&255,g[f++]=q>>>8&255,g[f++]=255&q;let r=k/0x100000000*1e4&0xfffffff;g[f++]=r>>>8&255,g[f++]=255&r,g[f++]=r>>>24&15|16,g[f++]=r>>>16&255,g[f++]=i>>>8|128,g[f++]=255&i;for(let a=0;a<6;++a)g[f+a]=h[a];return b||n(g)},r=function(a){let b;if(!l(a))throw TypeError("Invalid UUID");let c=new Uint8Array(16);return c[0]=(b=parseInt(a.slice(0,8),16))>>>24,c[1]=b>>>16&255,c[2]=b>>>8&255,c[3]=255&b,c[4]=(b=parseInt(a.slice(9,13),16))>>>8,c[5]=255&b,c[6]=(b=parseInt(a.slice(14,18),16))>>>8,c[7]=255&b,c[8]=(b=parseInt(a.slice(19,23),16))>>>8,c[9]=255&b,c[10]=(b=parseInt(a.slice(24,36),16))/0x10000000000&255,c[11]=b/0x100000000&255,c[12]=b>>>24&255,c[13]=b>>>16&255,c[14]=b>>>8&255,c[15]=255&b,c};function s(a,b,c){function d(a,d,e,f){if("string"==typeof a&&(a=function(a){a=unescape(encodeURIComponent(a));let b=[];for(let c=0;c<a.length;++c)b.push(a.charCodeAt(c));return b}(a)),"string"==typeof d&&(d=r(d)),16!==d.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let g=new Uint8Array(16+a.length);if(g.set(d),g.set(a,d.length),(g=c(g))[6]=15&g[6]|b,g[8]=63&g[8]|128,e){f=f||0;for(let a=0;a<16;++a)e[f+a]=g[a];return e}return n(g)}try{d.name=a}catch(a){}return d.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",d.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",d}let t=s("v3",48,function(a){return Array.isArray(a)?a=Buffer.from(a):"string"==typeof a&&(a=Buffer.from(a,"utf8")),g().createHash("md5").update(a).digest()}),u=function(a,b,c){let d=(a=a||{}).random||(a.rng||j)();if(d[6]=15&d[6]|64,d[8]=63&d[8]|128,b){c=c||0;for(let a=0;a<16;++a)b[c+a]=d[a];return b}return n(d)},v=s("v5",80,function(a){return Array.isArray(a)?a=Buffer.from(a):"string"==typeof a&&(a=Buffer.from(a,"utf8")),g().createHash("sha1").update(a).digest()}),w="00000000-0000-0000-0000-000000000000",x=function(a){if(!l(a))throw TypeError("Invalid UUID");return parseInt(a.substr(14,1),16)}},73602:(a,b,c)=>{let d=c(8494),e=c(42142),{RPError:f}=c(61408),{assertIssuerConfiguration:g}=c(2279),h=c(28577),{keystores:i}=c(55132),j=c(48160),k=c(9552),l=new WeakMap,m=new WeakMap,n=a=>(m.has(a)||m.set(a,new e({max:100})),m.get(a));async function o(a=!1){g(this,"jwks_uri");let b=i.get(this),c=n(this);return a||!b?(l.has(this)||(c.reset(),l.set(this,(async()=>{let a=j(await k.call(this,{method:"GET",responseType:"json",url:this.jwks_uri,headers:{Accept:"application/json, application/jwk-set+json"}}).finally(()=>{l.delete(this)})),b=h.fromJWKS(a,{onlyPublic:!0});return c.set("throttle",!0,6e4),i.set(this,b),b})())),l.get(this)):b}async function p({kid:a,kty:b,alg:c,use:e},{allowMulti:g=!1}={}){let h=n(this),i={kid:a,kty:b,alg:c,use:e},j=d(i,{algorithm:"sha256",ignoreUnknown:!0,unorderedArrays:!0,unorderedSets:!0,respectType:!1}),k=h.get(j)||h.get("throttle"),l=await o.call(this,!k),m=l.all(i);if(delete i.use,0===m.length)throw new f({printf:["no valid key found in issuer's jwks_uri for key parameters %j",i],jwks:l});if(!g&&m.length>1&&!a)throw new f({printf:["multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case",i],jwks:l});return h.set(j,!0),m}a.exports.queryKeyStore=p,a.exports.keystore=o},73913:(a,b,c)=>{"use strict";Object.defineProperty(b,"r",{enumerable:!0,get:function(){return k}});let d=c(63033),e=c(29294),f=c(84971),g=c(76926),h=c(80023),i=c(98479),j=c(71617);function k(){let a=e.workAsyncStorage.getStore(),b=d.workUnitAsyncStorage.getStore();switch((!a||!b)&&(0,d.throwForMissingRequestStore)("draftMode"),b.type){case"request":return l(b.draftMode,a);case"cache":case"unstable-cache":let c=(0,d.getDraftModeProviderForCacheScope)(a,b);if(c)return l(c,a);case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return l(null,a);default:return b}}function l(a,b){let c,d=a??m,e=n.get(d);return e||(c=function(a){let b=new o(a),c=Promise.resolve(b);return Object.defineProperty(c,"isEnabled",{get:()=>b.isEnabled,enumerable:!0,configurable:!0}),c.enable=b.enable.bind(b),c.disable=b.disable.bind(b),c}(a),n.set(d,c),c)}c(43763);let m={},n=new WeakMap;class o{constructor(a){this._provider=a}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){p("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){p("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}function p(a){let b=e.workAsyncStorage.getStore(),c=d.workUnitAsyncStorage.getStore();if(b){if(c){if("cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "${a}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "${a}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===c.phase)throw Object.defineProperty(Error(`Route ${b.route} used "${a}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":let d=Object.defineProperty(Error(`Route ${b.route} used ${a} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,f.abortAndThrowOnSynchronousRequestDataAccess)(b.route,a,d,c);break;case"prerender-client":let e="`draftMode`";throw Object.defineProperty(new j.InvariantError(`${e} must not be used within a client component. Next.js should be preventing ${e} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,f.postponeWithTracking)(b.route,a,c.dynamicTracking);break;case"prerender-legacy":c.revalidate=0;let g=Object.defineProperty(new i.DynamicServerError(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw b.dynamicUsageDescription=a,b.dynamicUsageStack=g.stack,g}}}(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},74075:a=>{"use strict";a.exports=require("zlib")},74113:(a,b,c)=>{var d=c(66446),e=c(18040);a.exports=function a(b,c){var f;this.next||(e(a.prototype),e(a.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),e(this,"_invoke",function(a,e,g){function h(){return new c(function(e,f){!function a(e,f,g,h){try{var i=b[e](f),j=i.value;return j instanceof d?c.resolve(j.v).then(function(b){a("next",b,g,h)},function(b){a("throw",b,g,h)}):c.resolve(j).then(function(a){i.value=a,g(i)},function(b){return a("throw",b,g,h)})}catch(a){h(a)}}(a,g,e,f)})}return f=f?f.then(h,h):h()},!0)},a.exports.__esModule=!0,a.exports.default=a.exports},75722:(a,b,c)=>{var d=c(24956).default,e=c(60105);a.exports=function(a,b){if(b&&("object"==d(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");return e(a)},a.exports.__esModule=!0,a.exports.default=a.exports},76926:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61120));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},76964:(a,b,c)=>{let d,{strict:e}=c(12412),{createHash:f}=c(55511),{format:g}=c(28354),h=c(7161);function i(a,b,c){let e=(function(a,b){switch(a){case"HS256":case"RS256":case"PS256":case"ES256":case"ES256K":return f("sha256");case"HS384":case"RS384":case"PS384":case"ES384":return f("sha384");case"HS512":case"RS512":case"PS512":case"ES512":case"Ed25519":return f("sha512");case"Ed448":if(!h)throw TypeError("Ed448 *_hash calculation is not supported in your Node.js runtime version");return f("shake256",{outputLength:114});case"EdDSA":switch(b){case"Ed25519":return f("sha512");case"Ed448":if(!h)throw TypeError("Ed448 *_hash calculation is not supported in your Node.js runtime version");return f("shake256",{outputLength:114});default:throw TypeError("unrecognized or invalid EdDSA curve provided")}default:throw TypeError("unrecognized or invalid JWS algorithm provided")}})(b,c).update(a).digest();return d(e.slice(0,e.length/2))}d=Buffer.isEncoding("base64url")?a=>a.toString("base64url"):a=>a.toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),a.exports={validate:function(a,b,c,d,f){let h,j;if("string"!=typeof a.claim||!a.claim)throw TypeError("names.claim must be a non-empty string");if("string"!=typeof a.source||!a.source)throw TypeError("names.source must be a non-empty string");e("string"==typeof b&&b,`${a.claim} must be a non-empty string`),e("string"==typeof c&&c,`${a.source} must be a non-empty string`);try{h=i(c,d,f)}catch(b){j=g("%s could not be validated (%s)",a.claim,b.message)}j=j||g("%s mismatch, expected %s, got: %s",a.claim,h,b),e.equal(h,b,j)},generate:i}},78328:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.setModulusLength=b.weakMap=void 0,b.weakMap=new WeakMap;let c=(a,b)=>{let d=a.readUInt8(1);if((128&d)==0)return 0===b?d:c(a.subarray(2+d),b-1);let e=127&d;d=0;for(let b=0;b<e;b++)d<<=8,d|=a.readUInt8(2+b);return 0===b?d:c(a.subarray(2+d),b-1)};b.setModulusLength=(a,c)=>{b.weakMap.set(a,c)},b.default=(a,d)=>{if(2048>(a=>{var d,e;if(b.weakMap.has(a))return b.weakMap.get(a);let f=null!=(e=null==(d=a.asymmetricKeyDetails)?void 0:d.modulusLength)?e:((a,b)=>{let d=a.readUInt8(1);return(128&d)==0?c(a.subarray(2),b):c(a.subarray(2+(127&d)),b)})(a.export({format:"der",type:"pkcs1"}),+("private"===a.type))-1<<3;return b.weakMap.set(a,f),f})(a))throw TypeError(`${d} requires key modulusLength to be 2048 bits or larger`)}},78335:()=>{},78680:(a,b)=>{"use strict";function c(a,b,...d){if(d.length>2){let b=d.pop();a+=`one of type ${d.join(", ")}, or ${b}.`}else 2===d.length?a+=`one of type ${d[0]} or ${d[1]}.`:a+=`of type ${d[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor&&b.constructor.name&&(a+=` Received an instance of ${b.constructor.name}`),a}Object.defineProperty(b,"__esModule",{value:!0}),b.withAlg=void 0,b.default=(a,...b)=>c("Key must be ",a,...b),b.withAlg=function(a,b,...d){return c(`Key for the ${a} algorithm must be `,b,...d)}},78830:a=>{a.exports=a=>!!a&&a.constructor===Object},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79963:(a,b,c)=>{"use strict";let d;Object.defineProperty(b,"__esModule",{value:!0});let e=c(55511);b.default=a=>(d||(d=new Set((0,e.getCiphers)())),d.has(a))},80028:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.jwkImport=b.jwkExport=b.rsaPssParams=b.oneShotCallback=void 0;let[c,d]=process.versions.node.split(".").map(a=>parseInt(a,10));b.oneShotCallback=c>=16||15===c&&d>=13,b.rsaPssParams=!("electron"in process.versions)&&(c>=17||16===c&&d>=9),b.jwkExport=c>=16||15===c&&d>=9,b.jwkImport=c>=16||15===c&&d>=12},80335:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(78680),e=c(37265);b.default=(a,b,c)=>{a.startsWith("HS")||"dir"===a||a.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(a)?((a,b)=>{if(!(b instanceof Uint8Array)){if(!(0,e.default)(b))throw TypeError((0,d.withAlg)(a,b,...e.types,"Uint8Array"));if("secret"!==b.type)throw TypeError(`${e.types.join(" or ")} instances for symmetric algorithms must be of type "secret"`)}})(a,b):((a,b,c)=>{if(!(0,e.default)(b))throw TypeError((0,d.withAlg)(a,b,...e.types));if("secret"===b.type)throw TypeError(`${e.types.join(" or ")} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===c&&"public"===b.type)throw TypeError(`${e.types.join(" or ")} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===c&&"public"===b.type)throw TypeError(`${e.types.join(" or ")} instances for asymmetric algorithm decryption must be of type "private"`);if(b.algorithm&&"verify"===c&&"private"===b.type)throw TypeError(`${e.types.join(" or ")} instances for asymmetric algorithm verifying must be of type "public"`);if(b.algorithm&&"encrypt"===c&&"private"===b.type)throw TypeError(`${e.types.join(" or ")} instances for asymmetric algorithm encryption must be of type "public"`)})(a,b,c)}},81454:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.compactDecrypt=void 0;let d=c(36736),e=c(99938),f=c(2594);b.compactDecrypt=async function(a,b,c){if(a instanceof Uint8Array&&(a=f.decoder.decode(a)),"string"!=typeof a)throw new e.JWEInvalid("Compact JWE must be a string or Uint8Array");let{0:g,1:h,2:i,3:j,4:k,length:l}=a.split(".");if(5!==l)throw new e.JWEInvalid("Invalid Compact JWE");let m=await (0,d.flattenedDecrypt)({ciphertext:j,iv:i||void 0,protected:g||void 0,tag:k||void 0,encrypted_key:h||void 0},b,c),n={plaintext:m.plaintext,protectedHeader:m.protectedHeader};return"function"==typeof b?{...n,key:m.key}:n}},81630:a=>{"use strict";a.exports=require("http")},81822:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938),e=c(2594),f=c(69717),g=c(86852),h=c(25110),i=a=>a.toLowerCase().replace(/^application\//,"");b.default=(a,b,c={})=>{let j,k,{typ:l}=c;if(l&&("string"!=typeof a.typ||i(a.typ)!==i(l)))throw new d.JWTClaimValidationFailed('unexpected "typ" JWT header value',"typ","check_failed");try{j=JSON.parse(e.decoder.decode(b))}catch{}if(!(0,h.default)(j))throw new d.JWTInvalid("JWT Claims Set must be a top-level JSON object");let{requiredClaims:m=[],issuer:n,subject:o,audience:p,maxTokenAge:q}=c;for(let a of(void 0!==q&&m.push("iat"),void 0!==p&&m.push("aud"),void 0!==o&&m.push("sub"),void 0!==n&&m.push("iss"),new Set(m.reverse())))if(!(a in j))throw new d.JWTClaimValidationFailed(`missing required "${a}" claim`,a,"missing");if(n&&!(Array.isArray(n)?n:[n]).includes(j.iss))throw new d.JWTClaimValidationFailed('unexpected "iss" claim value',"iss","check_failed");if(o&&j.sub!==o)throw new d.JWTClaimValidationFailed('unexpected "sub" claim value',"sub","check_failed");if(p&&!((a,b)=>"string"==typeof a?b.includes(a):!!Array.isArray(a)&&b.some(Set.prototype.has.bind(new Set(a))))(j.aud,"string"==typeof p?[p]:p))throw new d.JWTClaimValidationFailed('unexpected "aud" claim value',"aud","check_failed");switch(typeof c.clockTolerance){case"string":k=(0,g.default)(c.clockTolerance);break;case"number":k=c.clockTolerance;break;case"undefined":k=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:r}=c,s=(0,f.default)(r||new Date);if((void 0!==j.iat||q)&&"number"!=typeof j.iat)throw new d.JWTClaimValidationFailed('"iat" claim must be a number',"iat","invalid");if(void 0!==j.nbf){if("number"!=typeof j.nbf)throw new d.JWTClaimValidationFailed('"nbf" claim must be a number',"nbf","invalid");if(j.nbf>s+k)throw new d.JWTClaimValidationFailed('"nbf" claim timestamp check failed',"nbf","check_failed")}if(void 0!==j.exp){if("number"!=typeof j.exp)throw new d.JWTClaimValidationFailed('"exp" claim must be a number',"exp","invalid");if(j.exp<=s-k)throw new d.JWTExpired('"exp" claim timestamp check failed',"exp","check_failed")}if(q){let a=s-j.iat;if(a-k>("number"==typeof q?q:(0,g.default)(q)))throw new d.JWTExpired('"iat" claim timestamp check failed (too far in the past)',"iat","check_failed");if(a<0-k)throw new d.JWTClaimValidationFailed('"iat" claim timestamp check failed (it should be in the past)',"iat","check_failed")}return j}},83872:(a,b,c)=>{let d,{inspect:e}=c(28354),f=c(81630),g=c(55511),{strict:h}=c(12412),i=c(11723),j=c(79551),{URL:k,URLSearchParams:l}=c(79551),m=c(37544),n=c(76964),o=c(97843),p=c(913),q=c(70885),r=c(72115),s=c(38700),{assertSigningAlgValuesSupport:t,assertIssuerConfiguration:u}=c(2279),v=c(24740),w=c(78830),x=c(48160),y=c(24453),{OPError:z,RPError:A}=c(61408),B=c(16156),{random:C}=c(35571),D=c(9552),{CLOCK_TOLERANCE:E}=c(43325),{keystores:F}=c(55132),G=c(28577),H=c(93929),{authenticatedPost:I,resolveResponseType:J,resolveRedirectUri:K}=c(49450),{queryKeyStore:L}=c(73602),M=c(30641),[N,O]=process.version.slice(1).split(".").map(a=>parseInt(a,10)),P=N>=17||16===N&&O>=9,Q=Symbol(),R=Symbol(),S=Symbol();function T(a){return v(a,"access_token","code","error_description","error_uri","error","expires_in","id_token","iss","response","session_state","state","token_type")}function U(a,b="Bearer"){return`${b} ${a}`}function V(a){let b=j.parse(a);return b.search?i.parse(b.search.substring(1)):{}}function W(a,b,c){if(void 0===a[c])throw new A({message:`missing required JWT property ${c}`,jwt:b})}function X(a){let b={client_id:this.client_id,scope:"openid",response_type:J.call(this),redirect_uri:K.call(this),...a};return Object.entries(b).forEach(([a,c])=>{null==c?delete b[a]:"claims"===a&&"object"==typeof c?b[a]=JSON.stringify(c):"resource"===a&&Array.isArray(c)?b[a]=c:"string"!=typeof c&&(b[a]=String(c))}),b}function Y(a){if(!w(a)||!Array.isArray(a.keys)||a.keys.some(a=>!w(a)||!("kty"in a)))throw TypeError("jwks must be a JSON Web Key Set formatted object");return G.fromJWKS(a,{onlyPrivate:!0})}class Z{#a;#l;#m;#n;constructor(a,b,c={},d,e){if(this.#a=new Map,this.#l=a,this.#m=b,"string"!=typeof c.client_id||!c.client_id)throw TypeError("client_id is required");let f={grant_types:["authorization_code"],id_token_signed_response_alg:"RS256",authorization_signed_response_alg:"RS256",response_types:["code"],token_endpoint_auth_method:"client_secret_basic",...this.fapi1()?{grant_types:["authorization_code","implicit"],id_token_signed_response_alg:"PS256",authorization_signed_response_alg:"PS256",response_types:["code id_token"],tls_client_certificate_bound_access_tokens:!0,token_endpoint_auth_method:void 0}:void 0,...this.fapi2()?{id_token_signed_response_alg:"PS256",authorization_signed_response_alg:"PS256",token_endpoint_auth_method:void 0}:void 0,...c};if(this.fapi())switch(f.token_endpoint_auth_method){case"self_signed_tls_client_auth":case"tls_client_auth":break;case"private_key_jwt":if(!d)throw TypeError("jwks is required");break;case void 0:throw TypeError("token_endpoint_auth_method is required");default:throw TypeError("invalid or unsupported token_endpoint_auth_method")}if(this.fapi2()&&(f.tls_client_certificate_bound_access_tokens&&f.dpop_bound_access_tokens||!f.tls_client_certificate_bound_access_tokens&&!f.dpop_bound_access_tokens))throw TypeError("either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true");if(!function(a,b,c){if(b.token_endpoint_auth_method||function(a,b){try{let c=a.issuer.token_endpoint_auth_methods_supported;!c.includes(b.token_endpoint_auth_method)&&c.includes("client_secret_post")&&(b.token_endpoint_auth_method="client_secret_post")}catch(a){}}(a,c),b.redirect_uri){if(b.redirect_uris)throw TypeError("provide a redirect_uri or redirect_uris, not both");c.redirect_uris=[b.redirect_uri],delete c.redirect_uri}if(b.response_type){if(b.response_types)throw TypeError("provide a response_type or response_types, not both");c.response_types=[b.response_type],delete c.response_type}}(this,c,f),t("token",this.issuer,f),["introspection","revocation"].forEach(a=>{!function(a,b,c){if(!b[`${a}_endpoint`])return;let d=c.token_endpoint_auth_method,e=c.token_endpoint_auth_signing_alg,f=`${a}_endpoint_auth_method`,g=`${a}_endpoint_auth_signing_alg`;void 0===c[f]&&void 0===c[g]&&(void 0!==d&&(c[f]=d),void 0!==e&&(c[g]=e))}(a,this.issuer,f),t(a,this.issuer,f)}),Object.entries(f).forEach(([a,b])=>{this.#a.set(a,b),this[a]||Object.defineProperty(this,a,{get(){return this.#a.get(a)},enumerable:!0})}),void 0!==d){let a=Y.call(this,d);F.set(this,a)}null!=e&&e.additionalAuthorizedParties&&(this.#n=H(e.additionalAuthorizedParties)),this[E]=0}authorizationUrl(a={}){if(!w(a))throw TypeError("params must be a plain object");u(this.issuer,"authorization_endpoint");let b=new k(this.issuer.authorization_endpoint);for(let[c,d]of Object.entries(X.call(this,a)))if(Array.isArray(d))for(let a of(b.searchParams.delete(c),d))b.searchParams.append(c,a);else b.searchParams.set(c,d);return b.href.replace(/\+/g,"%20")}authorizationPost(a={}){if(!w(a))throw TypeError("params must be a plain object");let b=X.call(this,a),c=Object.keys(b).map(a=>`<input type="hidden" name="${a}" value="${b[a]}"/>`).join("\n");return`<!DOCTYPE html>
<head>
<title>Requesting Authorization</title>
</head>
<body onload="javascript:document.forms[0].submit()">
<form method="post" action="${this.issuer.authorization_endpoint}">
  ${c}
</form>
</body>
</html>`}endSessionUrl(a={}){let b;u(this.issuer,"end_session_endpoint");let{0:c,length:d}=this.post_logout_redirect_uris||[],{post_logout_redirect_uri:e=1===d?c:void 0}=a;if({id_token_hint:b,...a}=a,b instanceof y){if(!b.id_token)throw TypeError("id_token not present in TokenSet");b=b.id_token}let f=j.parse(this.issuer.end_session_endpoint),g=r(V(this.issuer.end_session_endpoint),a,{post_logout_redirect_uri:e,client_id:this.client_id},{id_token_hint:b});return Object.entries(g).forEach(([a,b])=>{null==b&&delete g[a]}),f.search=null,f.query=g,j.format(f)}callbackParams(a){let b=a instanceof f.IncomingMessage||a&&a.method&&a.url;if("string"!=typeof a&&!b)throw TypeError("#callbackParams only accepts string urls, http.IncomingMessage or a lookalike");if(!b)return T(V(a));switch(a.method){case"GET":return T(V(a.url));case"POST":if(void 0===a.body)throw TypeError("incoming message body missing, include a body parser prior to this method call");switch(typeof a.body){case"object":case"string":if(Buffer.isBuffer(a.body))return T(i.parse(a.body.toString("utf-8")));if("string"==typeof a.body)return T(i.parse(a.body));return T(a.body);default:throw TypeError("invalid IncomingMessage body object")}default:throw TypeError("invalid IncomingMessage method")}}async callback(a,b,c={},{exchangeBody:d,clientAssertionPayload:e,DPoP:f}={}){let g=T(b);if(!c.jarm||"response"in b){if("response"in b){let a=await this.decryptJARM(g.response);g=await this.validateJARM(a)}}else throw new A({message:"expected a JARM response",checks:c,params:g});if(this.default_max_age&&!c.max_age&&(c.max_age=this.default_max_age),g.state&&!c.state)throw TypeError("checks.state argument is missing");if(!g.state&&c.state)throw new A({message:"state missing from the response",checks:c,params:g});if(c.state!==g.state)throw new A({printf:["state mismatch, expected %s, got: %s",c.state,g.state],checks:c,params:g});if("iss"in g){if(u(this.issuer,"issuer"),g.iss!==this.issuer.issuer)throw new A({printf:["iss mismatch, expected %s, got: %s",this.issuer.issuer,g.iss],params:g})}else if(this.issuer.authorization_response_iss_parameter_supported&&!("id_token"in g)&&!("response"in b))throw new A({message:"iss missing from the response",params:g});if(g.error)throw new z(g);let h={code:["code"],id_token:["id_token"],token:["access_token","token_type"]};if(c.response_type){for(let a of c.response_type.split(" "))if("none"===a){if(g.code||g.id_token||g.access_token)throw new A({message:'unexpected params encountered for "none" response',checks:c,params:g})}else for(let b of h[a])if(!g[b])throw new A({message:`${b} missing from response`,checks:c,params:g})}if(g.id_token){let a=new y(g);if(await this.decryptIdToken(a),await this.validateIdToken(a,c.nonce,"authorization",c.max_age,c.state),!g.code)return a}if(g.code){let b=await this.grant({...d,grant_type:"authorization_code",code:g.code,redirect_uri:a,code_verifier:c.code_verifier},{clientAssertionPayload:e,DPoP:f});return await this.decryptIdToken(b),await this.validateIdToken(b,c.nonce,"token",c.max_age),g.session_state&&(b.session_state=g.session_state),b}return new y(g)}async oauthCallback(a,b,c={},{exchangeBody:d,clientAssertionPayload:e,DPoP:f}={}){let g=T(b);if(!c.jarm||"response"in b){if("response"in b){let a=await this.decryptJARM(g.response);g=await this.validateJARM(a)}}else throw new A({message:"expected a JARM response",checks:c,params:g});if(g.state&&!c.state)throw TypeError("checks.state argument is missing");if(!g.state&&c.state)throw new A({message:"state missing from the response",checks:c,params:g});if(c.state!==g.state)throw new A({printf:["state mismatch, expected %s, got: %s",c.state,g.state],checks:c,params:g});if("iss"in g){if(u(this.issuer,"issuer"),g.iss!==this.issuer.issuer)throw new A({printf:["iss mismatch, expected %s, got: %s",this.issuer.issuer,g.iss],params:g})}else if(this.issuer.authorization_response_iss_parameter_supported&&!("id_token"in g)&&!("response"in b))throw new A({message:"iss missing from the response",params:g});if(g.error)throw new z(g);if("string"==typeof g.id_token&&g.id_token.length)throw new A({message:"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()",params:g});delete g.id_token;let h={code:["code"],token:["access_token","token_type"]};if(c.response_type)for(let a of c.response_type.split(" ")){if("none"===a&&(g.code||g.id_token||g.access_token))throw new A({message:'unexpected params encountered for "none" response',checks:c,params:g});if(h[a]){for(let b of h[a])if(!g[b])throw new A({message:`${b} missing from response`,checks:c,params:g})}}if(g.code){let b=await this.grant({...d,grant_type:"authorization_code",code:g.code,redirect_uri:a,code_verifier:c.code_verifier},{clientAssertionPayload:e,DPoP:f});if("string"==typeof b.id_token&&b.id_token.length)throw new A({message:"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()",params:g});return delete b.id_token,b}return new y(g)}async decryptIdToken(a){if(!this.id_token_encrypted_response_alg)return a;let b=a;if(b instanceof y){if(!b.id_token)throw TypeError("id_token not present in TokenSet");b=b.id_token}let c=this.id_token_encrypted_response_alg,d=this.id_token_encrypted_response_enc,e=await this.decryptJWE(b,c,d);return a instanceof y?(a.id_token=e,a):e}async validateJWTUserinfo(a){let b=this.userinfo_signed_response_alg;return this.validateJWT(a,b,[])}async decryptJARM(a){if(!this.authorization_encrypted_response_alg)return a;let b=this.authorization_encrypted_response_alg,c=this.authorization_encrypted_response_enc;return this.decryptJWE(a,b,c)}async decryptJWTUserinfo(a){if(!this.userinfo_encrypted_response_alg)return a;let b=this.userinfo_encrypted_response_alg,c=this.userinfo_encrypted_response_enc;return this.decryptJWE(a,b,c)}async decryptJWE(a,b,c="A128CBC-HS256"){let d,e=JSON.parse(q.decode(a.split(".")[0]));if(e.alg!==b)throw new A({printf:["unexpected JWE alg received, expected %s, got: %s",b,e.alg],jwt:a});if(e.enc!==c)throw new A({printf:["unexpected JWE enc received, expected %s, got: %s",c,e.enc],jwt:a});let f=a=>new TextDecoder().decode(a.plaintext);if(b.match(/^(?:RSA|ECDH)/)){let b=await F.get(this),c=m.decodeProtectedHeader(a);for(let e of b.all({...c,use:"enc"}))if(d=await m.compactDecrypt(a,await e.keyObject(c.alg)).then(f,()=>{}))break}else d=await m.compactDecrypt(a,this.secretForAlg("dir"===b?c:b)).then(f,()=>{});if(!d)throw new A({message:"failed to decrypt JWE",jwt:a});return d}async validateIdToken(a,b,c,d,e){let f=a,g=this.id_token_signed_response_alg;if(f instanceof y){if(!f.id_token)throw TypeError("id_token not present in TokenSet");f=f.id_token}f=String(f);let h=B(),{protected:i,payload:j,key:k}=await this.validateJWT(f,g);if("number"==typeof d||d!==S&&this.require_auth_time){if(!j.auth_time)throw new A({message:"missing required JWT property auth_time",jwt:f});if("number"!=typeof j.auth_time)throw new A({message:"JWT auth_time claim must be a JSON numeric value",jwt:f})}if("number"==typeof d&&j.auth_time+d<h-this[E])throw new A({printf:["too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i",d,j.auth_time,h-this[E]],now:h,tolerance:this[E],auth_time:j.auth_time,jwt:f});if(b!==R&&(j.nonce||void 0!==b)&&j.nonce!==b)throw new A({printf:["nonce mismatch, expected %s, got: %s",b,j.nonce],jwt:f});if("authorization"===c){if(!j.at_hash&&a.access_token)throw new A({message:"missing required property at_hash",jwt:f});if(!j.c_hash&&a.code)throw new A({message:"missing required property c_hash",jwt:f});if(this.fapi1()&&!j.s_hash&&(a.state||e))throw new A({message:"missing required property s_hash",jwt:f});if(j.s_hash){if(!e)throw TypeError('cannot verify s_hash, "checks.state" property not provided');try{n.validate({claim:"s_hash",source:"state"},j.s_hash,e,i.alg,k.jwk&&k.jwk.crv)}catch(a){throw new A({message:a.message,jwt:f})}}}if(this.fapi()&&j.iat<h-3600)throw new A({printf:["JWT issued too far in the past, now %i, iat %i",h,j.iat],now:h,tolerance:this[E],iat:j.iat,jwt:f});if(a.access_token&&void 0!==j.at_hash)try{n.validate({claim:"at_hash",source:"access_token"},j.at_hash,a.access_token,i.alg,k.jwk&&k.jwk.crv)}catch(a){throw new A({message:a.message,jwt:f})}if(a.code&&void 0!==j.c_hash)try{n.validate({claim:"c_hash",source:"code"},j.c_hash,a.code,i.alg,k.jwk&&k.jwk.crv)}catch(a){throw new A({message:a.message,jwt:f})}return a}async validateJWT(a,b,c=["iss","sub","aud","exp","iat"]){let d,e,f,g="https://self-issued.me"===this.issuer.issuer,i=B();try{({header:d,payload:e}=p(a,{complete:!0}))}catch(b){throw new A({printf:["failed to decode JWT (%s: %s)",b.name,b.message],jwt:a})}if(d.alg!==b)throw new A({printf:["unexpected JWT alg received, expected %s, got: %s",b,d.alg],jwt:a});if(g&&(c=[...c,"sub_jwk"]),c.forEach(W.bind(void 0,e,a)),void 0!==e.iss){let b=this.issuer.issuer;if(this.#m&&(b=this.issuer.issuer.replace("{tenantid}",e.tid)),e.iss!==b)throw new A({printf:["unexpected iss value, expected %s, got: %s",b,e.iss],jwt:a})}if(void 0!==e.iat&&"number"!=typeof e.iat)throw new A({message:"JWT iat claim must be a JSON numeric value",jwt:a});if(void 0!==e.nbf){if("number"!=typeof e.nbf)throw new A({message:"JWT nbf claim must be a JSON numeric value",jwt:a});if(e.nbf>i+this[E])throw new A({printf:["JWT not active yet, now %i, nbf %i",i+this[E],e.nbf],now:i,tolerance:this[E],nbf:e.nbf,jwt:a})}if(void 0!==e.exp){if("number"!=typeof e.exp)throw new A({message:"JWT exp claim must be a JSON numeric value",jwt:a});if(i-this[E]>=e.exp)throw new A({printf:["JWT expired, now %i, exp %i",i-this[E],e.exp],now:i,tolerance:this[E],exp:e.exp,jwt:a})}if(void 0!==e.aud){if(Array.isArray(e.aud)){if(e.aud.length>1&&!e.azp)throw new A({message:"missing required JWT property azp",jwt:a});if(!e.aud.includes(this.client_id))throw new A({printf:["aud is missing the client_id, expected %s to be included in %j",this.client_id,e.aud],jwt:a})}else if(e.aud!==this.client_id)throw new A({printf:["aud mismatch, expected %s, got: %s",this.client_id,e.aud],jwt:a})}if(void 0!==e.azp){let b=this.#n;if(!(b="string"==typeof b?[this.client_id,b]:Array.isArray(b)?[this.client_id,...b]:[this.client_id]).includes(e.azp))throw new A({printf:["azp mismatch, got: %s",e.azp],jwt:a})}if(g){try{h(w(e.sub_jwk));let a=await m.importJWK(e.sub_jwk,d.alg);h.equal(a.type,"public"),f=[{keyObject:()=>a}]}catch(b){throw new A({message:"failed to use sub_jwk claim as an asymmetric JSON Web Key",jwt:a})}if(await m.calculateJwkThumbprint(e.sub_jwk)!==e.sub)throw new A({message:"failed to match the subject with sub_jwk",jwt:a})}else d.alg.startsWith("HS")?f=[this.secretForAlg(d.alg)]:"none"!==d.alg&&(f=await L.call(this.issuer,{...d,use:"sig"}));if(!f&&"none"===d.alg)return{protected:d,payload:e};for(let b of f){let c=await m.compactVerify(a,b instanceof Uint8Array?b:await b.keyObject(d.alg)).catch(()=>{});if(c)return{payload:e,protected:c.protectedHeader,key:b}}throw new A({message:"failed to validate JWT signature",jwt:a})}async refresh(a,{exchangeBody:b,clientAssertionPayload:c,DPoP:d}={}){let e=a;if(e instanceof y){if(!e.refresh_token)throw TypeError("refresh_token not present in TokenSet");e=e.refresh_token}let f=await this.grant({...b,grant_type:"refresh_token",refresh_token:String(e)},{clientAssertionPayload:c,DPoP:d});if(f.id_token&&(await this.decryptIdToken(f),await this.validateIdToken(f,R,"token",S),a instanceof y&&a.id_token)){let b=a.claims().sub,c=f.claims().sub;if(c!==b)throw new A({printf:["sub mismatch, expected %s, got: %s",b,c],jwt:f.id_token})}return f}async requestResource(a,b,{method:c,headers:d,body:e,DPoP:f,tokenType:g=f?"DPoP":b instanceof y?b.token_type:"Bearer"}={},h){if(b instanceof y){if(!b.access_token)throw TypeError("access_token not present in TokenSet");b=b.access_token}if(b){if("string"!=typeof b)throw TypeError("invalid access token provided")}else throw TypeError("no access token provided");let i={headers:{Authorization:U(b,g),...d},body:e},j=!!this.tls_client_certificate_bound_access_tokens,k=await D.call(this,{...i,responseType:"buffer",method:c,url:a},{accessToken:b,mTLS:j,DPoP:f}),l=k.headers["www-authenticate"];return h!==Q&&l&&l.toLowerCase().startsWith("dpop ")&&"use_dpop_nonce"===s(l).error?this.requestResource(a,b,{method:c,headers:d,body:e,DPoP:f,tokenType:g}):k}async userinfo(a,{method:b="GET",via:c="header",tokenType:d,params:e,DPoP:f}={}){let g;u(this.issuer,"userinfo_endpoint");let i={tokenType:d,method:String(b).toUpperCase(),DPoP:f};if("GET"!==i.method&&"POST"!==i.method)throw TypeError("#userinfo() method can only be POST or a GET");if("body"===c&&"POST"!==i.method)throw TypeError("can only send body on POST");let j=!!(this.userinfo_signed_response_alg||this.userinfo_encrypted_response_alg);j?i.headers={Accept:"application/jwt"}:i.headers={Accept:"application/json"},this.tls_client_certificate_bound_access_tokens&&this.issuer.mtls_endpoint_aliases&&(g=this.issuer.mtls_endpoint_aliases.userinfo_endpoint),g=new k(g||this.issuer.userinfo_endpoint),"body"===c&&(i.headers.Authorization=void 0,i.headers["Content-Type"]="application/x-www-form-urlencoded",i.body=new l,i.body.append("access_token",a instanceof y?a.access_token:a)),e&&("GET"===i.method?Object.entries(e).forEach(([a,b])=>{g.searchParams.append(a,b)}):i.body?Object.entries(e).forEach(([a,b])=>{i.body.append(a,b)}):(i.body=new l,i.headers["Content-Type"]="application/x-www-form-urlencoded",Object.entries(e).forEach(([a,b])=>{i.body.append(a,b)}))),i.body&&(i.body=i.body.toString());let m=await this.requestResource(g,a,i),n=x(m,{bearer:!0});if(j){if(!/^application\/jwt/.test(m.headers["content-type"]))throw new A({message:"expected application/jwt response from the userinfo_endpoint",response:m});let a=m.body.toString(),b=await this.decryptJWTUserinfo(a);if(this.userinfo_signed_response_alg)({payload:n}=await this.validateJWTUserinfo(b));else try{n=JSON.parse(b),h(w(n))}catch(a){throw new A({message:"failed to parse userinfo JWE payload as JSON",jwt:b})}}else try{n=JSON.parse(m.body)}catch(a){throw Object.defineProperty(a,"response",{value:m}),a}if(a instanceof y&&a.id_token){let b=a.claims().sub;if(n.sub!==b)throw new A({printf:["userinfo sub mismatch, expected %s, got: %s",b,n.sub],body:n,jwt:a.id_token})}return n}encryptionSecret(a){let b=a<=256?"sha256":a<=384?"sha384":a<=512&&"sha512";if(!b)throw Error("unsupported symmetric encryption key derivation");return g.createHash(b).update(this.client_secret).digest().slice(0,a/8)}secretForAlg(a){if(!this.client_secret)throw TypeError("client_secret is required");return/^A(\d{3})(?:GCM)?KW$/.test(a)?this.encryptionSecret(parseInt(RegExp.$1,10)):/^A(\d{3})(?:GCM|CBC-HS(\d{3}))$/.test(a)?this.encryptionSecret(parseInt(RegExp.$2||RegExp.$1,10)):new TextEncoder().encode(this.client_secret)}async grant(a,{clientAssertionPayload:b,DPoP:c}={},d){let e;u(this.issuer,"token_endpoint");let f=await I.call(this,"token",{form:a,responseType:"json"},{clientAssertionPayload:b,DPoP:c});try{e=x(f)}catch(e){if(d!==Q&&e instanceof z&&"use_dpop_nonce"===e.error)return this.grant(a,{clientAssertionPayload:b,DPoP:c},Q);throw e}return new y(e)}async deviceAuthorization(a={},{exchangeBody:b,clientAssertionPayload:c,DPoP:d}={}){u(this.issuer,"device_authorization_endpoint"),u(this.issuer,"token_endpoint");let e=X.call(this,{client_id:this.client_id,redirect_uri:null,response_type:null,...a}),f=x(await I.call(this,"device_authorization",{responseType:"json",form:e},{clientAssertionPayload:c,endpointAuthMethod:"token"}));return new M({client:this,exchangeBody:b,clientAssertionPayload:c,response:f,maxAge:a.max_age,DPoP:d})}async revoke(a,b,{revokeBody:c,clientAssertionPayload:d}={}){if(u(this.issuer,"revocation_endpoint"),void 0!==b&&"string"!=typeof b)throw TypeError("hint must be a string");let e={...c,token:a};b&&(e.token_type_hint=b),x(await I.call(this,"revocation",{form:e},{clientAssertionPayload:d}),{body:!1})}async introspect(a,b,{introspectBody:c,clientAssertionPayload:d}={}){if(u(this.issuer,"introspection_endpoint"),void 0!==b&&"string"!=typeof b)throw TypeError("hint must be a string");let e={...c,token:a};return b&&(e.token_type_hint=b),x(await I.call(this,"introspection",{form:e,responseType:"json"},{clientAssertionPayload:d}))}static async register(a,b={}){let{initialAccessToken:c,jwks:d,...e}=b;return u(this.issuer,"registration_endpoint"),void 0===d||a.jwks||a.jwks_uri||(a.jwks=(await Y.call(this,d)).toJWKS()),new this(x(await D.call(this,{headers:{Accept:"application/json",...c?{Authorization:U(c)}:void 0},responseType:"json",json:a,url:this.issuer.registration_endpoint,method:"POST"}),{statusCode:201,bearer:!0}),d,e)}get metadata(){return H(Object.fromEntries(this.#a.entries()))}static async fromUri(a,b,c,d){return new this(x(await D.call(this,{method:"GET",url:a,responseType:"json",headers:{Authorization:U(b),Accept:"application/json"}}),{bearer:!0}),c,d)}async requestObject(a={},{sign:b=this.request_object_signing_alg||"none",encrypt:{alg:c=this.request_object_encryption_alg,enc:d=this.request_object_encryption_enc||"A128CBC-HS256"}={}}={}){let e,f;if(!w(a))throw TypeError("requestObject must be a plain object");let g=B(),h={alg:b,typ:"oauth-authz-req+jwt"},i=JSON.stringify(r({},a,{iss:this.client_id,aud:this.issuer.issuer,client_id:this.client_id,jti:C(),iat:g,exp:g+300,...this.fapi()?{nbf:g}:void 0}));if("none"===b)e=[q.encode(JSON.stringify(h)),q.encode(i),""].join(".");else{let a=b.startsWith("HS");if(a)f=this.secretForAlg(b);else{let a=await F.get(this);if(!a)throw TypeError(`no keystore present for client, cannot sign using alg ${b}`);if(!(f=a.get({alg:b,use:"sig"})))throw TypeError(`no key to sign with found for alg ${b}`)}e=await new m.CompactSign(new TextEncoder().encode(i)).setProtectedHeader({...h,kid:a?void 0:f.jwk.kid}).sign(a?f:await f.keyObject(b))}if(!c)return e;let j={alg:c,enc:d,cty:"oauth-authz-req+jwt"};return j.alg.match(/^(RSA|ECDH)/)?[f]=await L.call(this.issuer,{alg:j.alg,use:"enc"},{allowMulti:!0}):f=this.secretForAlg("dir"===j.alg?j.enc:j.alg),new m.CompactEncrypt(new TextEncoder().encode(e)).setProtectedHeader({...j,kid:f instanceof Uint8Array?void 0:f.jwk.kid}).encrypt(f instanceof Uint8Array?f:await f.keyObject(j.alg))}async pushedAuthorizationRequest(a={},{clientAssertionPayload:b}={}){u(this.issuer,"pushed_authorization_request_endpoint");let c={..."request"in a?a:X.call(this,a),client_id:this.client_id},d=await I.call(this,"pushed_authorization_request",{responseType:"json",form:c},{clientAssertionPayload:b,endpointAuthMethod:"token"}),e=x(d,{statusCode:201});if(!("expires_in"in e))throw new A({message:"expected expires_in in Pushed Authorization Successful Response",response:d});if("number"!=typeof e.expires_in)throw new A({message:"invalid expires_in value in Pushed Authorization Successful Response",response:d});if(!("request_uri"in e))throw new A({message:"expected request_uri in Pushed Authorization Successful Response",response:d});if("string"!=typeof e.request_uri)throw new A({message:"invalid request_uri value in Pushed Authorization Successful Response",response:d});return e}get issuer(){return this.#l}[e.custom](){return`${this.constructor.name} ${e(this.metadata,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}fapi(){return this.fapi1()||this.fapi2()}fapi1(){return"FAPI1Client"===this.constructor.name}fapi2(){return"FAPI2Client"===this.constructor.name}async validateJARM(a){let b=this.authorization_signed_response_alg,{payload:c}=await this.validateJWT(a,b,["iss","exp","aud"]);return T(c)}async dpopProof(a,b,c){let e;if(!w(a))throw TypeError("payload must be a plain object");if(o(b))e=b;else if("CryptoKey"===b[Symbol.toStringTag])e=b;else if("node:crypto"===m.cryptoRuntime)e=g.createPrivateKey(b);else throw TypeError("unrecognized crypto runtime");if("private"!==e.type)throw TypeError('"DPoP" option must be a private key');let f=d.call(this,e,b);if(!f)throw TypeError("could not determine DPoP JWS Algorithm");return new m.SignJWT({ath:c?q.encode(g.createHash("sha256").update(c).digest()):void 0,...a}).setProtectedHeader({alg:f,typ:"dpop+jwt",jwk:await aa(e,b)}).setIssuedAt().setJti(C()).sign(e)}}function $(a){switch(a.algorithm.name){case"Ed25519":case"Ed448":return"EdDSA";case"ECDSA":switch(a.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512"}break;case"RSASSA-PKCS1-v1_5":return`RS${a.algorithm.hash.name.slice(4)}`;case"RSA-PSS":return`PS${a.algorithm.hash.name.slice(4)}`;default:throw TypeError("unsupported DPoP private key")}}if("node:crypto"===m.cryptoRuntime){d=function(d,g){if("CryptoKey"===g[Symbol.toStringTag])return $(d);switch(d.asymmetricKeyType){case"ed25519":case"ed448":return"EdDSA";case"ec":var h=d,i=g;switch("object"==typeof i&&"object"==typeof i.key&&i.key.crv){case"P-256":return"ES256";case"secp256k1":return"ES256K";case"P-384":return"ES384";case"P-512":return"ES512"}let j=h.export({format:"der",type:"pkcs8"}),k=j[1]<128?17:18,l=j[k],m=j.slice(k+1,k+1+l);if(m.equals(b))return"ES256";if(m.equals(c))return"ES384";if(m.equals(e))return"ES512";if(m.equals(f))return"ES256K";throw TypeError("unsupported DPoP private key curve");case"rsa":case P&&"rsa-pss":var n=d,o=g,p=this.issuer.dpop_signing_alg_values_supported;if("object"==typeof o&&"jwk"===o.format&&o.key&&o.key.alg)return o.key.alg;if(Array.isArray(p)){let b=p.filter(RegExp.prototype.test.bind(a));return"rsa-pss"===n.asymmetricKeyType&&(b=b.filter(a=>a.startsWith("PS"))),["PS256","PS384","PS512","RS256","RS384","RS384"].find(a=>b.includes(a))}return"PS256";default:throw TypeError("unsupported DPoP private key")}};let a=/^(?:RS|PS)(?:256|384|512)$/,b=Buffer.from([42,134,72,206,61,3,1,7]),c=Buffer.from([43,129,4,0,34]),e=Buffer.from([43,129,4,0,35]),f=Buffer.from([43,129,4,0,10])}else d=$;let _=new WeakMap;async function aa(a,b){if("node:crypto"===m.cryptoRuntime&&"object"==typeof b&&"object"==typeof b.key&&"jwk"===b.format)return v(b.key,"kty","crv","x","y","e","n");if(_.has(b))return _.get(b);let c=v(await m.exportJWK(a),"kty","crv","x","y","e","n");return(o(b)||"WebCryptoAPI"===m.cryptoRuntime)&&_.set(b,c),c}a.exports=(a,b=!1)=>class extends Z{constructor(...c){super(a,b,...c)}static get issuer(){return a}},a.exports.BaseClient=Z},84838:a=>{a.exports=function(){return':root{--border-width:1px;--border-radius:0.5rem;--color-error:#c94b4b;--color-info:#157efb;--color-info-hover:#0f6ddb;--color-info-text:#fff}.__next-auth-theme-auto,.__next-auth-theme-light{--color-background:#ececec;--color-background-hover:hsla(0,0%,93%,.8);--color-background-card:#fff;--color-text:#000;--color-primary:#444;--color-control-border:#bbb;--color-button-active-background:#f9f9f9;--color-button-active-border:#aaa;--color-separator:#ccc}.__next-auth-theme-dark{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}@media (prefers-color-scheme:dark){.__next-auth-theme-auto{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}a.button,button{background-color:var(--provider-dark-bg,var(--color-background));color:var(--provider-dark-color,var(--color-primary))}a.button:hover,button:hover{background-color:var(--provider-dark-bg-hover,var(--color-background-hover))!important}#provider-logo{display:none!important}#provider-logo-dark{display:block!important;width:25px}}html{box-sizing:border-box}*,:after,:before{box-sizing:inherit;margin:0;padding:0}body{background-color:var(--color-background);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;margin:0;padding:0}h1{font-weight:400}h1,p{color:var(--color-text);margin-bottom:1.5rem;padding:0 1rem}form{margin:0;padding:0}label{font-weight:500;margin-bottom:.25rem;text-align:left}input[type],label{color:var(--color-text);display:block}input[type]{background:var(--color-background-card);border:var(--border-width) solid var(--color-control-border);border-radius:var(--border-radius);box-sizing:border-box;font-size:1rem;padding:.5rem 1rem;width:100%}input[type]:focus{box-shadow:none}p{font-size:1.1rem;line-height:2rem}a.button{line-height:1rem;text-decoration:none}a.button:link,a.button:visited{background-color:var(--color-background);color:var(--color-primary)}button span{flex-grow:1}a.button,button{align-items:center;background-color:var(--provider-bg);border-color:rgba(0,0,0,.1);border-radius:var(--border-radius);color:var(--provider-color,var(--color-primary));display:flex;font-size:1.1rem;font-weight:500;justify-content:center;min-height:62px;padding:.75rem 1rem;position:relative;transition:all .1s ease-in-out}a.button:hover,button:hover{background-color:var(--provider-bg-hover,var(--color-background-hover));cursor:pointer}a.button:active,button:active{cursor:pointer}a.button #provider-logo,button #provider-logo{display:block;width:25px}a.button #provider-logo-dark,button #provider-logo-dark{display:none}#submitButton{background-color:var(--brand-color,var(--color-info));color:var(--button-text-color,var(--color-info-text));width:100%}#submitButton:hover{background-color:var(--button-hover-bg,var(--color-info-hover))!important}a.site{color:var(--color-primary);font-size:1rem;line-height:2rem;text-decoration:none}a.site:hover{text-decoration:underline}.page{box-sizing:border-box;display:grid;height:100%;margin:0;padding:0;place-items:center;position:absolute;width:100%}.page>div{text-align:center}.error a.button{margin-top:.5rem;padding-left:2rem;padding-right:2rem}.error .message{margin-bottom:1.5rem}.signin input[type=text]{display:block;margin-left:auto;margin-right:auto}.signin hr{border:0;border-top:1px solid var(--color-separator);display:block;margin:2rem auto 1rem;overflow:visible}.signin hr:before{background:var(--color-background-card);color:#888;content:"or";padding:0 .4rem;position:relative;top:-.7rem}.signin .error{background:#f5f5f5;background:var(--color-error);border-radius:.3rem;font-weight:500}.signin .error p{color:var(--color-info-text);font-size:.9rem;line-height:1.2rem;padding:.5rem 1rem;text-align:left}.signin form,.signin>div{display:block}.signin form input[type],.signin>div input[type]{margin-bottom:.5rem}.signin form button,.signin>div button{width:100%}.signin .provider+.provider{margin-top:1rem}.logo{display:inline-block;margin:1.25rem 0;max-height:70px;max-width:150px}.card{background-color:var(--color-background-card);border-radius:2rem;padding:1.25rem 2rem}.card .header{color:var(--color-primary)}.section-header{color:var(--color-text)}@media screen and (min-width:450px){.card{margin:2rem 0;width:368px}}@media screen and (max-width:450px){.card{margin:1rem 0;width:343px}}'}},85634:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(42891),f=c(51800),g=c(2594),h=c(99938),i=c(21606),j=c(34010),k=c(17996),l=c(50003),m=c(90061),n=c(78680),o=c(79963),p=c(37265);b.default=(a,b,c,q,r,s)=>{let t;if((0,k.isCryptoKey)(b))(0,l.checkEncCryptoKey)(b,a,"decrypt"),t=d.KeyObject.from(b);else if(b instanceof Uint8Array||(0,m.default)(b))t=b;else throw TypeError((0,n.default)(b,...p.types,"Uint8Array"));switch((0,f.default)(a,t),(0,e.default)(a,q),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(a,b,c,e,f,k){let l,n,p=parseInt(a.slice(1,4),10);(0,m.default)(b)&&(b=b.export());let q=b.subarray(p>>3),r=b.subarray(0,p>>3),s=parseInt(a.slice(-3),10),t=`aes-${p}-cbc`;if(!(0,o.default)(t))throw new h.JOSENotSupported(`alg ${a} is not supported by your javascript runtime`);let u=(0,j.default)(k,e,c,s,r,p);try{l=(0,i.default)(f,u)}catch{}if(!l)throw new h.JWEDecryptionFailed;try{let a=(0,d.createDecipheriv)(t,q,e);n=(0,g.concat)(a.update(c),a.final())}catch{}if(!n)throw new h.JWEDecryptionFailed;return n}(a,t,c,q,r,s);case"A128GCM":case"A192GCM":case"A256GCM":return function(a,b,c,e,f,g){let i=parseInt(a.slice(1,4),10),j=`aes-${i}-gcm`;if(!(0,o.default)(j))throw new h.JOSENotSupported(`alg ${a} is not supported by your javascript runtime`);try{let a=(0,d.createDecipheriv)(j,b,e,{authTagLength:16});a.setAuthTag(f),g.byteLength&&a.setAAD(g,{plaintextLength:c.length});let h=a.update(c);return a.final(),h}catch{throw new h.JWEDecryptionFailed}}(a,t,c,q,r,s);default:throw new h.JOSENotSupported("Unsupported JWE Content Encryption Algorithm")}}},86280:(a,b,c)=>{"use strict";Object.defineProperty(b,"b",{enumerable:!0,get:function(){return m}});let d=c(92584),e=c(29294),f=c(63033),g=c(84971),h=c(80023),i=c(68388),j=c(76926);c(44523);let k=c(8719),l=c(71617);function m(){let a=e.workAsyncStorage.getStore(),b=f.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,k.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(a.forceStatic)return o(d.HeadersAdapter.seal(new Headers({})));if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender":var c=b;let e=n.get(c);if(e)return e;let f=(0,i.makeHangingPromise)(c.renderSignal,"`headers()`");return n.set(c,f),f;case"prerender-client":let j="`headers`";throw Object.defineProperty(new l.InvariantError(`${j} must not be used within a client component. Next.js should be preventing ${j} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,g.postponeWithTracking)(a.route,"headers",b.dynamicTracking);break;case"prerender-legacy":(0,g.throwToInterruptStaticGeneration)("headers",a,b)}(0,g.trackDynamicDataInDynamicRender)(a,b)}return o((0,f.getExpectedRequestStore)("headers").headers)}c(43763);let n=new WeakMap;function o(a){let b=n.get(a);if(b)return b;let c=Promise.resolve(a);return n.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},86305:(a,b,c)=>{var d=c(28339),e=c(74113);a.exports=function(a,b,c,f,g){return new e(d().w(a,b,c,f),g||Promise)},a.exports.__esModule=!0,a.exports.default=a.exports},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86505:(a,b,c)=>{let d=c(78830);a.exports=function a(b,...c){for(let e of c)if(d(e))for(let[c,f]of Object.entries(e))"__proto__"!==c&&"constructor"!==c&&(d(b[c])&&d(f)?b[c]=a(b[c],f):void 0!==f&&(b[c]=f));return b}},86852:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let c=/^(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i;b.default=a=>{let b=c.exec(a);if(!b)throw TypeError("Invalid time period format");let d=parseFloat(b[1]);switch(b[2].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":return Math.round(d);case"minute":case"minutes":case"min":case"mins":case"m":return Math.round(60*d);case"hour":case"hours":case"hr":case"hrs":case"h":return Math.round(3600*d);case"day":case"days":case"d":return Math.round(86400*d);case"week":case"weeks":case"w":return Math.round(604800*d);default:return Math.round(0x1e187e0*d)}}},87451:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.getBody=e,b.setCookie=function(a,b){var c;let e=null!=(c=a.getHeader("Set-Cookie"))?c:[];Array.isArray(e)||(e=[e]);let{name:f,value:g,options:h}=b,i=(0,d.serialize)(f,g,h);e.push(i),a.setHeader("Set-Cookie",e)},b.toResponse=function(a){var b,c,e;let f=new Headers(null==(b=a.headers)?void 0:b.reduce((a,{key:b,value:c})=>(a[b]=c,a),{}));null==(c=a.cookies)||c.forEach(a=>{let{name:b,value:c,options:e}=a,g=(0,d.serialize)(b,c,e);f.has("Set-Cookie")?f.append("Set-Cookie",g):f.set("Set-Cookie",g)});let g=a.body;"application/json"===f.get("content-type")?g=JSON.stringify(a.body):"application/x-www-form-urlencoded"===f.get("content-type")&&(g=new URLSearchParams(a.body).toString());let h=new Response(g,{headers:f,status:a.redirect?302:null!=(e=a.status)?e:200});return a.redirect&&h.headers.set("Location",a.redirect),h};var d=c(25538);async function e(a){if(!("body"in a)||!a.body||"POST"!==a.method)return;let b=a.headers.get("content-type");return null!=b&&b.includes("application/json")?await a.json():null!=b&&b.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await a.text())):void 0}},88062:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.unwrap=b.wrap=void 0;let d=c(79428),e=c(55511),f=c(99938),g=c(2594),h=c(17996),i=c(50003),j=c(90061),k=c(78680),l=c(79963),m=c(37265);function n(a,b){if(a.symmetricKeySize<<3!==parseInt(b.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${b}`)}function o(a,b,c){if((0,j.default)(a))return a;if(a instanceof Uint8Array)return(0,e.createSecretKey)(a);if((0,h.isCryptoKey)(a))return(0,i.checkEncCryptoKey)(a,b,c),e.KeyObject.from(a);throw TypeError((0,k.default)(a,...m.types,"Uint8Array"))}b.wrap=(a,b,c)=>{let h=parseInt(a.slice(1,4),10),i=`aes${h}-wrap`;if(!(0,l.default)(i))throw new f.JOSENotSupported(`alg ${a} is not supported either by JOSE or your javascript runtime`);let j=o(b,a,"wrapKey");n(j,a);let k=(0,e.createCipheriv)(i,j,d.Buffer.alloc(8,166));return(0,g.concat)(k.update(c),k.final())},b.unwrap=(a,b,c)=>{let h=parseInt(a.slice(1,4),10),i=`aes${h}-wrap`;if(!(0,l.default)(i))throw new f.JOSENotSupported(`alg ${a} is not supported either by JOSE or your javascript runtime`);let j=o(b,a,"unwrapKey");n(j,a);let k=(0,e.createDecipheriv)(i,j,d.Buffer.alloc(8,166));return(0,g.concat)(k.update(c),k.final())}},89227:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938);b.default=function(a){switch(a){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new d.JOSENotSupported(`alg ${a} is not supported either by JOSE or your javascript runtime`)}}},89412:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.createCSRFToken=function({options:a,cookieValue:b,isPost:c,bodyValue:e}){if(b){let[f,g]=b.split("|");if(g===(0,d.createHash)("sha256").update(`${f}${a.secret}`).digest("hex"))return{csrfTokenVerified:c&&f===e,csrfToken:f}}let f=(0,d.randomBytes)(32).toString("hex"),g=(0,d.createHash)("sha256").update(`${f}${a.secret}`).digest("hex");return{cookie:`${f}|${g}`,csrfToken:f}};var d=c(55511)},89831:(a,b,c)=>{var d=c(86305);a.exports=function(a,b,c,e,f){var g=d(a,b,c,e,f);return g.next().then(function(a){return a.done?a.value:g.next()})},a.exports.__esModule=!0,a.exports.default=a.exports},90061:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(55511),e=c(28354);b.default=e.types.isKeyObject?a=>e.types.isKeyObject(a):a=>null!=a&&a instanceof d.KeyObject},90253:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0})},91428:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.createRemoteJWKSet=void 0;let d=c(95339),e=c(99938),f=c(2055);class g extends f.LocalJWKSet{constructor(a,b){if(super({keys:[]}),this._jwks=void 0,!(a instanceof URL))throw TypeError("url must be an instance of URL");this._url=new URL(a.href),this._options={agent:null==b?void 0:b.agent,headers:null==b?void 0:b.headers},this._timeoutDuration="number"==typeof(null==b?void 0:b.timeoutDuration)?null==b?void 0:b.timeoutDuration:5e3,this._cooldownDuration="number"==typeof(null==b?void 0:b.cooldownDuration)?null==b?void 0:b.cooldownDuration:3e4,this._cacheMaxAge="number"==typeof(null==b?void 0:b.cacheMaxAge)?null==b?void 0:b.cacheMaxAge:6e5}coolingDown(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cooldownDuration}fresh(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cacheMaxAge}async getKey(a,b){this._jwks&&this.fresh()||await this.reload();try{return await super.getKey(a,b)}catch(c){if(c instanceof e.JWKSNoMatchingKey&&!1===this.coolingDown())return await this.reload(),super.getKey(a,b);throw c}}async reload(){this._pendingFetch&&("undefined"!=typeof WebSocketPair||"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||"undefined"!=typeof EdgeRuntime&&"vercel"===EdgeRuntime)&&(this._pendingFetch=void 0),this._pendingFetch||(this._pendingFetch=(0,d.default)(this._url,this._timeoutDuration,this._options).then(a=>{if(!(0,f.isJWKSLike)(a))throw new e.JWKSInvalid("JSON Web Key Set malformed");this._jwks={keys:a.keys},this._jwksTimestamp=Date.now(),this._pendingFetch=void 0}).catch(a=>{throw this._pendingFetch=void 0,a})),await this._pendingFetch}}b.createRemoteJWKSet=function(a,b){let c=new g(a,b);return async function(a,b){return c.getKey(a,b)}}},92296:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(99938);b.default=function(a){if(!(a instanceof Uint8Array)||a.length<8)throw new d.JWEInvalid("PBES2 Salt Input must be 8 or more octets")}},92562:(a,b,c)=>{"use strict";let d;Object.defineProperty(b,"__esModule",{value:!0});let e=c(55511),f=c(28354),g=c(63898),h=c(89227),i=c(42567),j=c(15055);d=e.sign.length>3?(0,f.promisify)(e.sign):e.sign,b.default=async(a,b,c)=>{let f=(0,j.default)(a,b,"sign");if(a.startsWith("HS")){let b=e.createHmac((0,h.default)(a),f);return b.update(c),b.digest()}return d((0,g.default)(a),c,(0,i.default)(a,f))}},93069:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)}},93346:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.fromX509=b.fromSPKI=b.fromPKCS8=b.toPKCS8=b.toSPKI=void 0;let d=c(55511),e=c(79428),f=c(17996),g=c(90061),h=c(78680),i=c(37265),j=(a,b,c)=>{let e;if((0,f.isCryptoKey)(c)){if(!c.extractable)throw TypeError("CryptoKey is not extractable");e=d.KeyObject.from(c)}else if((0,g.default)(c))e=c;else throw TypeError((0,h.default)(c,...i.types));if(e.type!==a)throw TypeError(`key is not a ${a} key`);return e.export({format:"pem",type:b})};b.toSPKI=a=>j("public","spki",a),b.toPKCS8=a=>j("private","pkcs8",a),b.fromPKCS8=a=>(0,d.createPrivateKey)({key:e.Buffer.from(a.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\s)/g,""),"base64"),type:"pkcs8",format:"der"}),b.fromSPKI=a=>(0,d.createPublicKey)({key:e.Buffer.from(a.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\s)/g,""),"base64"),type:"spki",format:"der"}),b.fromX509=a=>(0,d.createPublicKey)({key:a,type:"spki",format:"pem"})},93466:(a,b,c)=>{"use strict";function d(a){var b=this;if(b instanceof d||(b=new d),b.tail=null,b.head=null,b.length=0,a&&"function"==typeof a.forEach)a.forEach(function(a){b.push(a)});else if(arguments.length>0)for(var c=0,e=arguments.length;c<e;c++)b.push(arguments[c]);return b}function e(a,b,c,d){if(!(this instanceof e))return new e(a,b,c,d);this.list=d,this.value=a,b?(b.next=this,this.prev=b):this.prev=null,c?(c.prev=this,this.next=c):this.next=null}a.exports=d,d.Node=e,d.create=d,d.prototype.removeNode=function(a){if(a.list!==this)throw Error("removing node which does not belong to this list");var b=a.next,c=a.prev;return b&&(b.prev=c),c&&(c.next=b),a===this.head&&(this.head=b),a===this.tail&&(this.tail=c),a.list.length--,a.next=null,a.prev=null,a.list=null,b},d.prototype.unshiftNode=function(a){if(a!==this.head){a.list&&a.list.removeNode(a);var b=this.head;a.list=this,a.next=b,b&&(b.prev=a),this.head=a,this.tail||(this.tail=a),this.length++}},d.prototype.pushNode=function(a){if(a!==this.tail){a.list&&a.list.removeNode(a);var b=this.tail;a.list=this,a.prev=b,b&&(b.next=a),this.tail=a,this.head||(this.head=a),this.length++}},d.prototype.push=function(){for(var a,b,c=0,d=arguments.length;c<d;c++){a=this,b=arguments[c],a.tail=new e(b,a.tail,null,a),a.head||(a.head=a.tail),a.length++}return this.length},d.prototype.unshift=function(){for(var a,b,c=0,d=arguments.length;c<d;c++){a=this,b=arguments[c],a.head=new e(b,null,a.head,a),a.tail||(a.tail=a.head),a.length++}return this.length},d.prototype.pop=function(){if(this.tail){var a=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,a}},d.prototype.shift=function(){if(this.head){var a=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,a}},d.prototype.forEach=function(a,b){b=b||this;for(var c=this.head,d=0;null!==c;d++)a.call(b,c.value,d,this),c=c.next},d.prototype.forEachReverse=function(a,b){b=b||this;for(var c=this.tail,d=this.length-1;null!==c;d--)a.call(b,c.value,d,this),c=c.prev},d.prototype.get=function(a){for(var b=0,c=this.head;null!==c&&b<a;b++)c=c.next;if(b===a&&null!==c)return c.value},d.prototype.getReverse=function(a){for(var b=0,c=this.tail;null!==c&&b<a;b++)c=c.prev;if(b===a&&null!==c)return c.value},d.prototype.map=function(a,b){b=b||this;for(var c=new d,e=this.head;null!==e;)c.push(a.call(b,e.value,this)),e=e.next;return c},d.prototype.mapReverse=function(a,b){b=b||this;for(var c=new d,e=this.tail;null!==e;)c.push(a.call(b,e.value,this)),e=e.prev;return c},d.prototype.reduce=function(a,b){var c,d=this.head;if(arguments.length>1)c=b;else if(this.head)d=this.head.next,c=this.head.value;else throw TypeError("Reduce of empty list with no initial value");for(var e=0;null!==d;e++)c=a(c,d.value,e),d=d.next;return c},d.prototype.reduceReverse=function(a,b){var c,d=this.tail;if(arguments.length>1)c=b;else if(this.tail)d=this.tail.prev,c=this.tail.value;else throw TypeError("Reduce of empty list with no initial value");for(var e=this.length-1;null!==d;e--)c=a(c,d.value,e),d=d.prev;return c},d.prototype.toArray=function(){for(var a=Array(this.length),b=0,c=this.head;null!==c;b++)a[b]=c.value,c=c.next;return a},d.prototype.toArrayReverse=function(){for(var a=Array(this.length),b=0,c=this.tail;null!==c;b++)a[b]=c.value,c=c.prev;return a},d.prototype.slice=function(a,b){(b=b||this.length)<0&&(b+=this.length),(a=a||0)<0&&(a+=this.length);var c=new d;if(b<a||b<0)return c;a<0&&(a=0),b>this.length&&(b=this.length);for(var e=0,f=this.head;null!==f&&e<a;e++)f=f.next;for(;null!==f&&e<b;e++,f=f.next)c.push(f.value);return c},d.prototype.sliceReverse=function(a,b){(b=b||this.length)<0&&(b+=this.length),(a=a||0)<0&&(a+=this.length);var c=new d;if(b<a||b<0)return c;a<0&&(a=0),b>this.length&&(b=this.length);for(var e=this.length,f=this.tail;null!==f&&e>b;e--)f=f.prev;for(;null!==f&&e>a;e--,f=f.prev)c.push(f.value);return c},d.prototype.splice=function(a,b,...c){a>this.length&&(a=this.length-1),a<0&&(a=this.length+a);for(var d=0,f=this.head;null!==f&&d<a;d++)f=f.next;for(var g=[],d=0;f&&d<b;d++)g.push(f.value),f=this.removeNode(f);null===f&&(f=this.tail),f!==this.head&&f!==this.tail&&(f=f.prev);for(var d=0;d<c.length;d++)f=function(a,b,c){var d=b===a.head?new e(c,null,b,a):new e(c,b,b.next,a);return null===d.next&&(a.tail=d),null===d.prev&&(a.head=d),a.length++,d}(this,f,c[d]);return g},d.prototype.reverse=function(){for(var a=this.head,b=this.tail,c=a;null!==c;c=c.prev){var d=c.prev;c.prev=c.next,c.next=d}return this.head=b,this.tail=a,this};try{c(63580)(d)}catch(a){}},93929:a=>{a.exports=globalThis.structuredClone||(a=>JSON.parse(JSON.stringify(a)))},94069:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return h},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return o},getModifiedCookieValues:function(){return k},responseCookiesToRequestCookies:function(){return q},wrapWithMutableAccessCheck:function(){return n}});let d=c(23158),e=c(43763),f=c(29294),g=c(63033);class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new h}}class i{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return h.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let j=Symbol.for("next.mutated.cookies");function k(a){let b=a[j];return b&&Array.isArray(b)&&0!==b.length?b:[]}function l(a,b){let c=k(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class m{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,i=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case j:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{i()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{i()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function n(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return p("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return p("cookies().set"),a.set(...c),b};default:return e.ReflectAdapter.get(a,c,d)}}});return b}function o(a){return"action"===a.phase}function p(a){if(!o((0,g.getExpectedRequestStore)(a)))throw new h}function q(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},94387:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=b.hkdf=void 0;let d=c(46821);function e(a,b){if("string"==typeof a)return new TextEncoder().encode(a);if(!(a instanceof Uint8Array))throw TypeError(`"${b}"" must be an instance of Uint8Array or a string`);return a}async function f(a,b,c,f,g){return(0,d.default)(function(a){switch(a){case"sha256":case"sha384":case"sha512":case"sha1":return a;default:throw TypeError('unsupported "digest" value')}}(a),function(a){let b=e(a,"ikm");if(!b.byteLength)throw TypeError('"ikm" must be at least one byte in length');return b}(b),e(c,"salt"),function(a){let b=e(a,"info");if(b.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return b}(f),function(a,b){if("number"!=typeof a||!Number.isInteger(a)||a<1)throw TypeError('"keylen" must be a positive integer');if(a>255*(parseInt(b.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return a}(g,a))}b.hkdf=f,b.default=f},94735:a=>{"use strict";a.exports=require("events")},94747:(a,b,c)=>{"use strict";c.d(b,{zR:()=>e});let d=require("@prisma/client"),e=globalThis.prisma??new d.PrismaClient({log:["error"],errorFormat:"pretty"})},95339:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});let d=c(81630),e=c(55591),f=c(94735),g=c(99938),h=c(2594);b.default=async(a,b,c)=>{let i;switch(a.protocol){case"https:":i=e.get;break;case"http:":i=d.get;break;default:throw TypeError("Unsupported URL protocol.")}let{agent:j,headers:k}=c,l=i(a.href,{agent:j,timeout:b,headers:k}),[m]=await Promise.race([(0,f.once)(l,"response"),(0,f.once)(l,"timeout")]);if(!m)throw l.destroy(),new g.JWKSTimeout;if(200!==m.statusCode)throw new g.JOSEError("Expected 200 OK from the JSON Web Key Set HTTP response");let n=[];for await(let a of m)n.push(a);try{return JSON.parse(h.decoder.decode((0,h.concat)(...n)))}catch{throw new g.JOSEError("Failed to parse the JSON Web Key Set HTTP response as JSON")}}},96376:a=>{function b(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(a.exports=b=function(){return!!c},a.exports.__esModule=!0,a.exports.default=a.exports)()}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},96487:()=>{},96559:(a,b,c)=>{"use strict";a.exports=c(44870)},97528:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.unwrap=b.wrap=void 0;let d=c(22122),e=c(85634),f=c(35749),g=c(68327);b.wrap=async function(a,b,c,e){let h=a.slice(0,7);e||(e=(0,f.default)(h));let{ciphertext:i,tag:j}=await (0,d.default)(h,c,b,e,new Uint8Array(0));return{encryptedKey:i,iv:(0,g.encode)(e),tag:(0,g.encode)(j)}},b.unwrap=async function(a,b,c,d,f){let g=a.slice(0,7);return(0,e.default)(g,b,c,d,f,new Uint8Array(0))}},97843:(a,b,c)=>{let d=c(28354),e=c(55511);a.exports=d.types.isKeyObject||(a=>a&&a instanceof e.KeyObject)},97920:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.importJWK=b.importPKCS8=b.importX509=b.importSPKI=void 0;let d=c(68327),e=c(93346),f=c(53687),g=c(99938),h=c(25110);b.importSPKI=async function(a,b,c){if("string"!=typeof a||0!==a.indexOf("-----BEGIN PUBLIC KEY-----"))throw TypeError('"spki" must be SPKI formatted string');return(0,e.fromSPKI)(a,b,c)},b.importX509=async function(a,b,c){if("string"!=typeof a||0!==a.indexOf("-----BEGIN CERTIFICATE-----"))throw TypeError('"x509" must be X.509 formatted string');return(0,e.fromX509)(a,b,c)},b.importPKCS8=async function(a,b,c){if("string"!=typeof a||0!==a.indexOf("-----BEGIN PRIVATE KEY-----"))throw TypeError('"pkcs8" must be PKCS#8 formatted string');return(0,e.fromPKCS8)(a,b,c)},b.importJWK=async function(a,b,c){var e;if(!(0,h.default)(a))throw TypeError("JWK must be an object");switch(b||(b=a.alg),a.kty){case"oct":if("string"!=typeof a.k||!a.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=c||(c=!0!==a.ext),c)return(0,f.default)({...a,alg:b,ext:null!=(e=a.ext)&&e});return(0,d.decode)(a.k);case"RSA":if(void 0!==a.oth)throw new g.JOSENotSupported('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return(0,f.default)({...a,alg:b});default:throw new g.JOSENotSupported('Unsupported "kty" (Key Type) Parameter value')}}},98905:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.jwtVerify=void 0;let d=c(29132),e=c(81822),f=c(99938);b.jwtVerify=async function(a,b,c){var g;let h=await (0,d.compactVerify)(a,b,c);if((null==(g=h.protectedHeader.crit)?void 0:g.includes("b64"))&&!1===h.protectedHeader.b64)throw new f.JWTInvalid("JWTs MUST NOT use unencoded payload");let i={payload:(0,e.default)(h.protectedHeader,h.payload,c),protectedHeader:h.protectedHeader};return"function"==typeof b?{...i,key:h.key}:i}},99296:(a,b,c)=>{"use strict";var d=c(9168);Object.defineProperty(b,"__esModule",{value:!0}),b.default=i;var e=d(c(53873)),f=d(c(59496)),g=c(29837),h=d(c(11714));async function i(a){var b,c,d,i,j,k;let{options:l,query:m,body:n,method:o,headers:p,sessionStore:q}=a,{provider:r,adapter:s,url:t,callbackUrl:u,pages:v,jwt:w,events:x,callbacks:y,session:{strategy:z,maxAge:A},logger:B}=l,C=[],D="jwt"===z;if("oauth"===r.type)try{let{profile:d,account:g,OAuthProfile:h,cookies:i}=await (0,e.default)({query:m,body:n,method:o,options:l,cookies:a.cookies});i.length&&C.push(...i);try{if(B.debug("OAUTH_CALLBACK_RESPONSE",{profile:d,account:g,OAuthProfile:h}),!d||!g||!h)return{redirect:`${t}/signin`,cookies:C};let a=d;if(s){let{getUserByAccount:b}=s,c=await b({providerAccountId:g.providerAccountId,provider:r.id});c&&(a=c)}try{let b=await y.signIn({user:a,account:g,profile:h});if(!b)return{redirect:`${t}/error?error=AccessDenied`,cookies:C};if("string"==typeof b)return{redirect:b,cookies:C}}catch(a){return{redirect:`${t}/error?error=${encodeURIComponent(a.message)}`,cookies:C}}let{user:e,session:i,isNewUser:j}=await (0,f.default)({sessionToken:q.value,profile:d,account:g,options:l});if(D){let a={name:e.name,email:e.email,picture:e.image,sub:null==(c=e.id)?void 0:c.toString()},b=await y.jwt({token:a,user:e,account:g,profile:h,isNewUser:j,trigger:j?"signUp":"signIn"}),d=await w.encode({...w,token:b}),f=new Date;f.setTime(f.getTime()+1e3*A);let i=q.chunk(d,{expires:f});C.push(...i)}else C.push({name:l.cookies.sessionToken.name,value:i.sessionToken,options:{...l.cookies.sessionToken.options,expires:i.expires}});if(await (null==(b=x.signIn)?void 0:b.call(x,{user:e,account:g,profile:d,isNewUser:j})),j&&v.newUser)return{redirect:`${v.newUser}${v.newUser.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(u)}`,cookies:C};return{redirect:u,cookies:C}}catch(a){if("AccountNotLinkedError"===a.name)return{redirect:`${t}/error?error=OAuthAccountNotLinked`,cookies:C};if("CreateUserError"===a.name)return{redirect:`${t}/error?error=OAuthCreateAccount`,cookies:C};return B.error("OAUTH_CALLBACK_HANDLER_ERROR",a),{redirect:`${t}/error?error=Callback`,cookies:C}}}catch(a){if("OAuthCallbackError"===a.name)return B.error("OAUTH_CALLBACK_ERROR",{error:a,providerId:r.id}),{redirect:`${t}/error?error=OAuthCallback`,cookies:C};return B.error("OAUTH_CALLBACK_ERROR",a),{redirect:`${t}/error?error=Callback`,cookies:C}}if("email"===r.type)try{let a=null==m?void 0:m.token,b=null==m?void 0:m.email;if(!a)return{redirect:`${t}/error?error=configuration`,cookies:C};let c=await s.useVerificationToken({identifier:b,token:(0,g.hashToken)(a,l)});if(!c||c.expires.valueOf()<Date.now()||b&&c.identifier!==b)return{redirect:`${t}/error?error=Verification`,cookies:C};let e=await (0,h.default)({email:c.identifier,adapter:s}),j={providerAccountId:e.email,type:"email",provider:r.id};try{let a=await y.signIn({user:e,account:j});if(!a)return{redirect:`${t}/error?error=AccessDenied`,cookies:C};if("string"==typeof a)return{redirect:a,cookies:C}}catch(a){return{redirect:`${t}/error?error=${encodeURIComponent(a.message)}`,cookies:C}}let{user:k,session:n,isNewUser:o}=await (0,f.default)({sessionToken:q.value,profile:e,account:j,options:l});if(D){let a={name:k.name,email:k.email,picture:k.image,sub:null==(i=k.id)?void 0:i.toString()},b=await y.jwt({token:a,user:k,account:j,isNewUser:o,trigger:o?"signUp":"signIn"}),c=await w.encode({...w,token:b}),d=new Date;d.setTime(d.getTime()+1e3*A);let e=q.chunk(c,{expires:d});C.push(...e)}else C.push({name:l.cookies.sessionToken.name,value:n.sessionToken,options:{...l.cookies.sessionToken.options,expires:n.expires}});if(await (null==(d=x.signIn)?void 0:d.call(x,{user:k,account:j,isNewUser:o})),o&&v.newUser)return{redirect:`${v.newUser}${v.newUser.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(u)}`,cookies:C};return{redirect:u,cookies:C}}catch(a){if("CreateUserError"===a.name)return{redirect:`${t}/error?error=EmailCreateAccount`,cookies:C};return B.error("CALLBACK_EMAIL_ERROR",a),{redirect:`${t}/error?error=Callback`,cookies:C}}if("credentials"===r.type&&"POST"===o){let a;try{if(!(a=await r.authorize(n,{query:m,body:n,headers:p,method:o})))return{status:401,redirect:`${t}/error?${new URLSearchParams({error:"CredentialsSignin",provider:r.id})}`,cookies:C}}catch(a){return{status:401,redirect:`${t}/error?error=${encodeURIComponent(a.message)}`,cookies:C}}let b={providerAccountId:a.id,type:"credentials",provider:r.id};try{let c=await y.signIn({user:a,account:b,credentials:n});if(!c)return{status:403,redirect:`${t}/error?error=AccessDenied`,cookies:C};if("string"==typeof c)return{redirect:c,cookies:C}}catch(a){return{redirect:`${t}/error?error=${encodeURIComponent(a.message)}`,cookies:C}}let c={name:a.name,email:a.email,picture:a.image,sub:null==(j=a.id)?void 0:j.toString()},d=await y.jwt({token:c,user:a,account:b,isNewUser:!1,trigger:"signIn"}),e=await w.encode({...w,token:d}),f=new Date;f.setTime(f.getTime()+1e3*A);let g=q.chunk(e,{expires:f});return C.push(...g),await (null==(k=x.signIn)?void 0:k.call(x,{user:a,account:b})),{redirect:u,cookies:C}}return{status:500,body:`Error: Callback for provider type ${r.type} not supported`,cookies:C}}},99312:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.CompactSign=void 0;let d=c(59310);class e{constructor(a){this._flattened=new d.FlattenedSign(a)}setProtectedHeader(a){return this._flattened.setProtectedHeader(a),this}async sign(a,b){let c=await this._flattened.sign(a,b);if(void 0===c.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${c.protected}.${c.payload}.${c.signature}`}}b.CompactSign=e},99607:(a,b,c)=>{var d=c(66446),e=c(28339),f=c(89831),g=c(86305),h=c(74113),i=c(67135),j=c(49305);function k(){"use strict";var b=e(),c=b.m(k),l=(Object.getPrototypeOf?Object.getPrototypeOf(c):c.__proto__).constructor;function m(a){var b="function"==typeof a&&a.constructor;return!!b&&(b===l||"GeneratorFunction"===(b.displayName||b.name))}var n={throw:1,return:2,break:3,continue:3};function o(a){var b,c;return function(d){b||(b={stop:function(){return c(d.a,2)},catch:function(){return d.v},abrupt:function(a,b){return c(d.a,n[a],b)},delegateYield:function(a,e,f){return b.resultName=e,c(d.d,j(a),f)},finish:function(a){return c(d.f,a)}},c=function(a,c,e){d.p=b.prev,d.n=b.next;try{return a(c,e)}finally{b.next=d.n}}),b.resultName&&(b[b.resultName]=d.v,b.resultName=void 0),b.sent=d.v,b.next=d.n;try{return a.call(this,b)}finally{d.p=b.prev,d.n=b.next}}}return(a.exports=k=function(){return{wrap:function(a,c,d,e){return b.w(o(a),c,d,e&&e.reverse())},isGeneratorFunction:m,mark:b.m,awrap:function(a,b){return new d(a,b)},AsyncIterator:h,async:function(a,b,c,d,e){return(m(b)?g:f)(o(a),b,c,d,e)},keys:i,values:j}},a.exports.__esModule=!0,a.exports.default=a.exports)()}a.exports=k,a.exports.__esModule=!0,a.exports.default=a.exports},99632:(a,b)=>{"use strict";function c(a,b,c){d(a,b),b.set(a,c)}function d(a,b){if(b.has(a))throw TypeError("Cannot initialize the same private elements twice on an object")}function e(a,b){return a.get(g(a,b))}function f(a,b,c){return a.set(g(a,b),c),c}function g(a,b,c){if("function"==typeof a?a===b:a.has(b))return arguments.length<3?b:c;throw TypeError("Private element is not present on this object")}Object.defineProperty(b,"__esModule",{value:!0}),b.SessionStore=void 0,b.defaultCookies=function(a){let b=a?"__Secure-":"";return{sessionToken:{name:`${b}next-auth.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},callbackUrl:{name:`${b}next-auth.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},csrfToken:{name:`${a?"__Host-":""}next-auth.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},pkceCodeVerifier:{name:`${b}next-auth.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},state:{name:`${b}next-auth.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},nonce:{name:`${b}next-auth.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}}}};var h=new WeakMap,i=new WeakMap,j=new WeakMap,k=new WeakSet;class l{constructor(a,b,g){!function(a,b){d(a,b),b.add(a)}(this,k),c(this,h,{}),c(this,i,void 0),c(this,j,void 0),f(j,this,g),f(i,this,a);let{cookies:l}=b,{name:m}=a;if("function"==typeof(null==l?void 0:l.getAll))for(let{name:a,value:b}of l.getAll())a.startsWith(m)&&(e(h,this)[a]=b);else if(l instanceof Map)for(let a of l.keys())a.startsWith(m)&&(e(h,this)[a]=l.get(a));else for(let a in l)a.startsWith(m)&&(e(h,this)[a]=l[a])}get value(){return Object.keys(e(h,this)).sort((a,b)=>{var c,d;return parseInt(null!=(c=a.split(".").pop())?c:"0")-parseInt(null!=(d=b.split(".").pop())?d:"0")}).map(a=>e(h,this)[a]).join("")}chunk(a,b){let c=g(k,this,n).call(this);for(let d of g(k,this,m).call(this,{name:e(i,this).name,value:a,options:{...e(i,this).options,...b}}))c[d.name]=d;return Object.values(c)}clean(){return Object.values(g(k,this,n).call(this))}}function m(a){let b=Math.ceil(a.value.length/3933);if(1===b)return e(h,this)[a.name]=a.value,[a];let c=[];for(let d=0;d<b;d++){let b=`${a.name}.${d}`,f=a.value.substr(3933*d,3933);c.push({...a,name:b,value:f}),e(h,this)[b]=f}return e(j,this).debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:163,valueSize:a.value.length,chunks:c.map(a=>a.value.length+163)}),c}function n(){let a={};for(let c in e(h,this)){var b;null==(b=e(h,this))||delete b[c],a[c]={name:c,value:"",options:{...e(i,this).options,maxAge:0}}}return a}b.SessionStore=l},99933:(a,b,c)=>{"use strict";Object.defineProperty(b,"B",{value:!0}),Object.defineProperty(b,"U",{enumerable:!0,get:function(){return n}});let d=c(94069),e=c(23158),f=c(29294),g=c(63033),h=c(84971),i=c(80023),j=c(68388),k=c(76926);c(44523);let l=c(8719),m=c(71617);function n(){let a="cookies",b=f.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return p(d.RequestCookiesAdapter.seal(new e.RequestCookies(new Headers({}))));if(c){if("cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":var k=c;let f=o.get(k);if(f)return f;let g=(0,j.makeHangingPromise)(k.renderSignal,"`cookies()`");return o.set(k,g),g;case"prerender-client":let n="`cookies`";throw Object.defineProperty(new m.InvariantError(`${n} must not be used within a client component. Next.js should be preventing ${n} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,h.postponeWithTracking)(b.route,a,c.dynamicTracking);break;case"prerender-legacy":(0,h.throwToInterruptStaticGeneration)(a,b,c)}(0,h.trackDynamicDataInDynamicRender)(b,c)}let n=(0,g.getExpectedRequestStore)(a);return p((0,d.areCookiesMutableInCurrentPhase)(n)?n.userspaceMutableCookies:n.cookies)}c(43763);let o=new WeakMap;function p(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):q.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):r.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function q(){return this.getAll().map(a=>[a.name,a]).values()}function r(a){for(let a of this.getAll())this.delete(a.name);return a}(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})},99938:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.JWSSignatureVerificationFailed=b.JWKSTimeout=b.JWKSMultipleMatchingKeys=b.JWKSNoMatchingKey=b.JWKSInvalid=b.JWKInvalid=b.JWTInvalid=b.JWSInvalid=b.JWEInvalid=b.JWEDecompressionFailed=b.JWEDecryptionFailed=b.JOSENotSupported=b.JOSEAlgNotAllowed=b.JWTExpired=b.JWTClaimValidationFailed=b.JOSEError=void 0;class c extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(a){var b;super(a),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null==(b=Error.captureStackTrace)||b.call(Error,this,this.constructor)}}b.JOSEError=c;class d extends c{static get code(){return"ERR_JWT_CLAIM_VALIDATION_FAILED"}constructor(a,b="unspecified",c="unspecified"){super(a),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=b,this.reason=c}}b.JWTClaimValidationFailed=d;class e extends c{static get code(){return"ERR_JWT_EXPIRED"}constructor(a,b="unspecified",c="unspecified"){super(a),this.code="ERR_JWT_EXPIRED",this.claim=b,this.reason=c}}b.JWTExpired=e;class f extends c{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}static get code(){return"ERR_JOSE_ALG_NOT_ALLOWED"}}b.JOSEAlgNotAllowed=f;class g extends c{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}b.JOSENotSupported=g;class h extends c{constructor(){super(...arguments),this.code="ERR_JWE_DECRYPTION_FAILED",this.message="decryption operation failed"}static get code(){return"ERR_JWE_DECRYPTION_FAILED"}}b.JWEDecryptionFailed=h;class i extends c{constructor(){super(...arguments),this.code="ERR_JWE_DECOMPRESSION_FAILED",this.message="decompression operation failed"}static get code(){return"ERR_JWE_DECOMPRESSION_FAILED"}}b.JWEDecompressionFailed=i;class j extends c{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}static get code(){return"ERR_JWE_INVALID"}}b.JWEInvalid=j;class k extends c{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}static get code(){return"ERR_JWS_INVALID"}}b.JWSInvalid=k;class l extends c{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}static get code(){return"ERR_JWT_INVALID"}}b.JWTInvalid=l;class m extends c{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}static get code(){return"ERR_JWK_INVALID"}}b.JWKInvalid=m;class n extends c{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}b.JWKSInvalid=n;class o extends c{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}b.JWKSNoMatchingKey=o;class p extends c{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}b.JWKSMultipleMatchingKeys=p,Symbol.asyncIterator;class q extends c{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}b.JWKSTimeout=q;class r extends c{constructor(){super(...arguments),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED",this.message="signature verification failed"}static get code(){return"ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}b.JWSSignatureVerificationFailed=r}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,262],()=>b(b.s=61325));module.exports=c})();