(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{416:(e,s,t)=>{Promise.resolve().then(t.bind(t,6117))},1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>o});var a=t(5155),r=t(2115),l=t(9434);let o=r.forwardRef((e,s)=>{let{className:t,type:r,...o}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...o})});o.displayName="Input"},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var a=t(5155),r=t(2115),l=t(968),o=t(2085),d=t(9434);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,d.cn)(c(),t),...r})});n.displayName=l.b.displayName},5169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"notFound")&&t.d(s,{notFound:function(){return a.notFound}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6117:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(5155),r=t(2115),l=t(6874),o=t.n(l),d=t(5695),c=t(2177),n=t(221),i=t(8309),m=t(285),u=t(2523),p=t(5057),h=t(6695),x=t(7481),f=t(4817),y=t(5169),j=t(1007),N=t(8883),w=t(9420),v=t(2919),g=t(8749),b=t(2657);let k=i.Ik({name:i.Yj().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters"),email:i.Yj().email("Invalid email address"),phone:i.Yj().optional(),password:i.Yj().min(8,"Password must be at least 8 characters").max(128,"Password must be less than 128 characters"),confirmPassword:i.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function A(){let[e,s]=(0,r.useState)(!1),[t,l]=(0,r.useState)(!1),[i,A]=(0,r.useState)(!1),P=(0,d.useRouter)(),{toast:C}=(0,x.dj)(),{t:R}=(0,f.o)(),{register:F,handleSubmit:M,formState:{errors:E}}=(0,c.mN)({resolver:(0,n.u)(k)}),J=async e=>{A(!0);try{let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,email:e.email,phone:e.phone,password:e.password})}),t=await s.json();t.success?(C({title:R("common.success"),description:"Account created successfully! Please login."}),P.push("/auth/login")):C({title:R("common.error"),description:t.error||"Failed to create account",variant:"destructive"})}catch(e){C({title:R("common.error"),description:"An error occurred during registration",variant:"destructive"})}finally{A(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(o(),{href:"/",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),(0,a.jsxs)(h.Zp,{className:"w-full",children:[(0,a.jsxs)(h.aR,{className:"space-y-1",children:[(0,a.jsx)(h.ZB,{className:"text-2xl text-center",children:R("auth.register")}),(0,a.jsx)(h.BT,{className:"text-center",children:"Create your account to get started"})]}),(0,a.jsx)(h.Wu,{className:"space-y-4",children:(0,a.jsxs)("form",{onSubmit:M(J),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"name",children:"Full Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"name",type:"text",placeholder:"Enter your full name",className:"pl-10",...F("name")})]}),E.name&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.name.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"email",children:"Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...F("email")})]}),E.email&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"phone",children:"Phone (Optional)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"phone",type:"tel",placeholder:"Enter your phone number",className:"pl-10",...F("phone")})]}),E.phone&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.phone.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"password",type:e?"text":"password",placeholder:"Create a password",className:"pl-10 pr-10",...F("password")}),(0,a.jsx)("button",{type:"button",onClick:()=>s(!e),className:"absolute right-3 top-3 text-muted-foreground hover:text-foreground",children:e?(0,a.jsx)(g.A,{className:"h-4 w-4"}):(0,a.jsx)(b.A,{className:"h-4 w-4"})})]}),E.password&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.password.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{id:"confirmPassword",type:t?"text":"password",placeholder:"Confirm your password",className:"pl-10 pr-10",...F("confirmPassword")}),(0,a.jsx)("button",{type:"button",onClick:()=>l(!t),className:"absolute right-3 top-3 text-muted-foreground hover:text-foreground",children:t?(0,a.jsx)(g.A,{className:"h-4 w-4"}):(0,a.jsx)(b.A,{className:"h-4 w-4"})})]}),E.confirmPassword&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.confirmPassword.message})]}),(0,a.jsx)(m.$,{type:"submit",className:"w-full",disabled:i,children:i?"Creating account...":R("auth.register")})]})}),(0,a.jsx)(h.wL,{children:(0,a.jsxs)("p",{className:"text-center text-sm text-muted-foreground w-full",children:["Already have an account?"," ",(0,a.jsx)(o(),{href:"/auth/login",className:"text-primary hover:underline",children:R("auth.login")})]})})]})]})})}},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>n,Wu:()=>i,ZB:()=>c,Zp:()=>o,aR:()=>d,wL:()=>m});var a=t(5155),r=t(2115),l=t(9434);let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});o.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});d.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});n.displayName="CardDescription";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});i.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},8749:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}},e=>{e.O(0,[455,874,804,197,441,964,358],()=>e(e.s=416)),_N_E=e.O()}]);