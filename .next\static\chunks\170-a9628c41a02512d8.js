"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[170],{1366:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},5169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6516:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},8564:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9255:(e,t,r)=>{r.d(t,{UC:()=>ee,B8:()=>X,bL:()=>W,l9:()=>Y});var n=r(2115),a=r.t(n,2),o=r(5185),i=r(6081),l=r(7328),s=r(6101),u=r(2712),c=a[" useId ".trim().toString()]||(()=>void 0),d=0;function f(e){let[t,r]=n.useState(c());return(0,u.N)(()=>{e||r(e=>e??String(d++))},[e]),e||(t?`radix-${t}`:"")}var v=r(3655),p=r(9033),m=r(5845),y=r(5155),b=n.createContext(void 0);function h(e){let t=n.useContext(b);return e||t||"ltr"}var w="rovingFocusGroup.onEntryFocus",g={bubbles:!1,cancelable:!0},x="RovingFocusGroup",[A,k,C]=(0,l.N)(x),[I,F]=(0,i.A)(x,[C]),[R,D]=I(x),T=n.forwardRef((e,t)=>(0,y.jsx)(A.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,y.jsx)(A.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,y.jsx)(j,{...e,ref:t})})}));T.displayName=x;var j=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:l,currentTabStopId:u,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:f,preventScrollOnEntryFocus:b=!1,...A}=e,C=n.useRef(null),I=(0,s.s)(t,C),F=h(l),[D,T]=(0,m.i)({prop:u,defaultProp:null!=c?c:null,onChange:d,caller:x}),[j,E]=n.useState(!1),S=(0,p.c)(f),G=k(r),M=n.useRef(!1),[K,N]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(w,S),()=>e.removeEventListener(w,S)},[S]),(0,y.jsx)(R,{scope:r,orientation:a,dir:F,loop:i,currentTabStopId:D,onItemFocus:n.useCallback(e=>T(e),[T]),onItemShiftTab:n.useCallback(()=>E(!0),[]),onFocusableItemAdd:n.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>N(e=>e-1),[]),children:(0,y.jsx)(v.sG.div,{tabIndex:j||0===K?-1:0,"data-orientation":a,...A,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(w,g);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=G().filter(e=>e.focusable);L([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),b)}}M.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),E="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:s,...u}=e,c=f(),d=l||c,p=D(E,r),m=p.currentTabStopId===d,b=k(r),{onFocusableItemAdd:h,onFocusableItemRemove:w,currentTabStopId:g}=p;return n.useEffect(()=>{if(a)return h(),()=>w()},[a,h,w]),(0,y.jsx)(A.ItemSlot,{scope:r,id:d,focusable:a,active:i,children:(0,y.jsx)(v.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?p.onItemFocus(d):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return G[a]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>L(r))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=g}):s})})});S.displayName=E;var G={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var M=r(8905),K="Tabs",[N,_]=(0,i.A)(K,[F]),P=F(),[z,V]=N(K),B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:s="automatic",...u}=e,c=h(l),[d,p]=(0,m.i)({prop:n,onChange:a,defaultProp:null!=o?o:"",caller:K});return(0,y.jsx)(z,{scope:r,baseId:f(),value:d,onValueChange:p,orientation:i,dir:c,activationMode:s,children:(0,y.jsx)(v.sG.div,{dir:c,"data-orientation":i,...u,ref:t})})});B.displayName=K;var U="TabsList",q=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,o=V(U,r),i=P(r);return(0,y.jsx)(T,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:n,children:(0,y.jsx)(v.sG.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});q.displayName=U;var H="TabsTrigger",O=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,l=V(H,r),s=P(r),u=J(l.baseId,n),c=Q(l.baseId,n),d=n===l.value;return(0,y.jsx)(S,{asChild:!0,...s,focusable:!a,active:d,children:(0,y.jsx)(v.sG.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":c,"data-state":d?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:u,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;d||a||!e||l.onValueChange(n)})})})});O.displayName=H;var Z="TabsContent",$=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:o,children:i,...l}=e,s=V(Z,r),u=J(s.baseId,a),c=Q(s.baseId,a),d=a===s.value,f=n.useRef(d);return n.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,y.jsx)(M.C,{present:o||d,children:r=>{let{present:n}=r;return(0,y.jsx)(v.sG.div,{"data-state":d?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:c,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:n&&i})}})});function J(e,t){return"".concat(e,"-trigger-").concat(t)}function Q(e,t){return"".concat(e,"-content-").concat(t)}$.displayName=Z;var W=B,X=q,Y=O,ee=$}}]);