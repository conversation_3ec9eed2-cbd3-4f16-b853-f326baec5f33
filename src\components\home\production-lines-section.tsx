"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Factory,
  Cpu,
  Zap,
  ArrowRight,
  Play,
  TrendingUp,
  Award,
  Users,
  Clock,
  CheckCircle,
  Settings,
} from "lucide-react"

const productionLines = [
  {
    id: "automated-assembly",
    title: "Automated Assembly Line",
    description: "High-precision automated assembly systems for electronics and automotive industries",
    image: "/production-line-1.jpg",
    category: "Automation",
    capacity: "10,000 units/day",
    efficiency: "99.5%",
    industries: ["Electronics", "Automotive", "Medical Devices"],
    features: ["AI-Powered Quality Control", "Real-time Monitoring", "Predictive Maintenance", "Zero-Defect Production"],
    price: "Starting at $2.5M",
    leadTime: "12-16 weeks",
    color: "from-blue-500 to-cyan-500",
    popular: true,
    specs: {
      power: "500kW",
      footprint: "2000 sq ft",
      operators: "2-3 people",
      uptime: "99.5%"
    }
  },
  {
    id: "smart-packaging",
    title: "Smart Packaging Line",
    description: "Intelligent packaging solutions with IoT integration and sustainability focus",
    image: "/production-line-2.jpg",
    category: "Packaging",
    capacity: "15,000 packages/hour",
    efficiency: "98.2%",
    industries: ["Food & Beverage", "Pharmaceuticals", "Consumer Goods"],
    features: ["Eco-Friendly Materials", "Smart Labeling", "Batch Tracking", "Quality Assurance"],
    price: "Starting at $1.8M",
    leadTime: "10-14 weeks",
    color: "from-green-500 to-emerald-500",
    popular: false,
    specs: {
      power: "300kW",
      footprint: "1500 sq ft",
      operators: "1-2 people",
      uptime: "98.2%"
    }
  },
  {
    id: "precision-manufacturing",
    title: "Precision Manufacturing Cell",
    description: "Ultra-precise manufacturing for aerospace and defense applications",
    image: "/production-line-3.jpg",
    category: "Precision",
    capacity: "500 parts/day",
    efficiency: "99.9%",
    industries: ["Aerospace", "Defense", "Medical"],
    features: ["Micron-Level Precision", "Clean Room Compatible", "Advanced Metrology", "Certified Quality"],
    price: "Starting at $5.2M",
    leadTime: "20-24 weeks",
    color: "from-purple-500 to-pink-500",
    popular: false,
    specs: {
      power: "800kW",
      footprint: "3000 sq ft",
      operators: "3-4 people",
      uptime: "99.9%"
    }
  },
  {
    id: "flexible-robotics",
    title: "Flexible Robotics System",
    description: "Adaptable robotic systems for multi-product manufacturing flexibility",
    image: "/production-line-4.jpg",
    category: "Robotics",
    capacity: "Variable",
    efficiency: "97.8%",
    industries: ["General Manufacturing", "Custom Products", "Prototyping"],
    features: ["Multi-Product Capability", "Quick Changeover", "AI Learning", "Collaborative Robots"],
    price: "Starting at $3.2M",
    leadTime: "14-18 weeks",
    color: "from-orange-500 to-red-500",
    popular: true,
    specs: {
      power: "400kW",
      footprint: "2500 sq ft",
      operators: "2-3 people",
      uptime: "97.8%"
    }
  }
]

export function ProductionLinesSection() {
  const [selectedLine, setSelectedLine] = useState(productionLines[0])
  const [hoveredCard, setHoveredCard] = useState<string | null>(null)
  const { t } = useLanguage()

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-white/10 text-white border-white/20">
            <Factory className="h-4 w-4 mr-2" />
            Industrial Solutions
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Advanced Production Lines
            </span>
            <br />
            <span className="text-white">Built for the Future</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            State-of-the-art manufacturing solutions designed for efficiency, reliability, 
            and scalability in modern industrial environments.
          </p>
        </div>

        {/* Featured Production Line */}
        <div className="mb-16">
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
              {/* Image */}
              <div className="relative h-96 lg:h-auto">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 z-10" />
                <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                  <Factory className="h-24 w-24 text-gray-400" />
                </div>
                
                {/* Play Button Overlay */}
                <div className="absolute inset-0 flex items-center justify-center z-20">
                  <Button 
                    size="lg" 
                    className="rounded-full w-16 h-16 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30"
                  >
                    <Play className="h-6 w-6 text-white fill-current" />
                  </Button>
                </div>

                {/* Popular Badge */}
                {selectedLine.popular && (
                  <div className="absolute top-4 left-4 z-30">
                    <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
                      <Award className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-8 lg:p-12">
                <div className="space-y-6">
                  <div>
                    <Badge className={`mb-4 bg-gradient-to-r ${selectedLine.color} text-white`}>
                      {selectedLine.category}
                    </Badge>
                    <h3 className="text-3xl font-bold text-white mb-4">
                      {selectedLine.title}
                    </h3>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      {selectedLine.description}
                    </p>
                  </div>

                  {/* Key Specs */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white/5 rounded-lg p-4">
                      <div className="text-2xl font-bold text-white">{selectedLine.capacity}</div>
                      <div className="text-sm text-gray-400">Daily Capacity</div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-4">
                      <div className="text-2xl font-bold text-green-400">{selectedLine.efficiency}</div>
                      <div className="text-sm text-gray-400">Efficiency Rate</div>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    {selectedLine.features.slice(0, 4).map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <div className="flex flex-col sm:flex-row gap-4 pt-4">
                    <Link href={`/production-lines/${selectedLine.id}`}>
                      <Button size="lg" className="group bg-gradient-to-r from-blue-500 to-cyan-500 hover:shadow-lg">
                        View Details
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </Link>
                    <Button variant="outline" size="lg" className="border-white/20 text-white hover:bg-white/10">
                      Request Quote
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Production Lines Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {productionLines.map((line) => (
            <Card 
              key={line.id}
              className={`bg-white/5 border-white/10 backdrop-blur-sm cursor-pointer transition-all duration-300 hover:bg-white/10 hover:scale-105 ${
                selectedLine.id === line.id ? 'ring-2 ring-blue-400' : ''
              } ${hoveredCard === line.id ? 'shadow-2xl' : ''}`}
              onClick={() => setSelectedLine(line)}
              onMouseEnter={() => setHoveredCard(line.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <CardHeader className="pb-4">
                {/* Image Placeholder */}
                <div className="relative h-32 mb-4 rounded-lg overflow-hidden">
                  <div className={`w-full h-full bg-gradient-to-br ${line.color} opacity-20`} />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Factory className="h-12 w-12 text-white" />
                  </div>
                  {line.popular && (
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-orange-500 text-white text-xs">
                        Popular
                      </Badge>
                    </div>
                  )}
                </div>

                <CardTitle className="text-white text-lg">{line.title}</CardTitle>
                <CardDescription className="text-gray-400 text-sm">
                  {line.description.substring(0, 80)}...
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Capacity:</span>
                  <span className="text-white font-medium">{line.capacity}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Efficiency:</span>
                  <span className="text-green-400 font-medium">{line.efficiency}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Lead Time:</span>
                  <span className="text-white font-medium">{line.leadTime}</span>
                </div>
                
                <div className="pt-2 border-t border-white/10">
                  <div className="text-lg font-bold text-white">{line.price}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-400 mb-2">500+</div>
            <div className="text-gray-400">Installations Worldwide</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-green-400 mb-2">99.2%</div>
            <div className="text-gray-400">Average Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-purple-400 mb-2">15+</div>
            <div className="text-gray-400">Years Experience</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-orange-400 mb-2">24/7</div>
            <div className="text-gray-400">Support Available</div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-white/5 to-white/10 rounded-2xl p-8 backdrop-blur-sm border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Revolutionize Your Manufacturing?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Our expert team will work with you to design and implement the perfect production line 
              solution for your specific needs and requirements.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/production-lines">
                <Button size="lg" className="px-8 py-4 text-lg font-semibold bg-gradient-to-r from-blue-500 to-cyan-500 hover:shadow-lg group">
                  Explore All Lines
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-white/20 text-white hover:bg-white/10">
                  Get Custom Quote
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
