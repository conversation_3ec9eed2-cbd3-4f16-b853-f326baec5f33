(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6232],{2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var r=a(5155),t=a(2115),l=a(9434);let i=t.forwardRef((e,s)=>{let{className:a,type:t,...i}=e;return(0,r.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"},3487:(e,s,a)=>{Promise.resolve().then(a.bind(a,4344))},4344:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>F});var r=a(5155),t=a(2115),l=a(2177),i=a(221),d=a(8309),n=a(4997),c=a(285),o=a(2523),m=a(5057),x=a(9434);let h=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("textarea",{className:(0,x.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...t})});h.displayName="Textarea";var p=a(4011);let u=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p.bL,{ref:s,className:(0,x.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...t})});u.displayName=p.bL.displayName;let f=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p._V,{ref:s,className:(0,x.cn)("aspect-square h-full w-full",a),...t})});f.displayName=p._V.displayName;let g=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p.H4,{ref:s,className:(0,x.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...t})});g.displayName=p.H4.displayName;var j=a(6695),N=a(7481),y=a(4817),b=a(1007),v=a(4355),w=a(8883),k=a(9420),A=a(9074),R=a(4516),B=a(4229);let C=d.Ik({firstName:d.Yj().min(2,"First name must be at least 2 characters"),lastName:d.Yj().min(2,"Last name must be at least 2 characters"),email:d.Yj().email("Invalid email address"),phone:d.Yj().optional(),dateOfBirth:d.Yj().optional(),bio:d.Yj().max(500,"Bio must be less than 500 characters").optional(),website:d.Yj().url("Invalid website URL").optional().or(d.eu("")),location:d.Yj().optional()});function F(){let[e,s]=(0,t.useState)(!1),[a,d]=(0,t.useState)(null),{toast:x}=(0,N.dj)(),{t:p}=(0,y.o)(),{register:F,handleSubmit:P,formState:{errors:J,isDirty:O},reset:S}=(0,l.mN)({resolver:(0,i.u)(C),defaultValues:{firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",dateOfBirth:"1990-01-01",bio:"Software developer passionate about e-commerce and technology.",website:"https://johndoe.dev",location:"New York, NY"}}),Y=async e=>{s(!0);try{await new Promise(e=>setTimeout(e,1e3)),console.log("Profile update:",e),x({title:p("common.success"),description:"Profile updated successfully"}),S(e)}catch(e){x({title:p("common.error"),description:"Failed to update profile",variant:"destructive"})}finally{s(!1)}};return(0,r.jsx)(n.O,{children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Profile Settings"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage your personal information and preferences"})]}),(0,r.jsxs)("form",{onSubmit:P(Y),className:"space-y-8",children:[(0,r.jsxs)(j.Zp,{children:[(0,r.jsxs)(j.aR,{children:[(0,r.jsxs)(j.ZB,{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 mr-2"}),"Profile Picture"]}),(0,r.jsx)(j.BT,{children:"Upload a profile picture to personalize your account"})]}),(0,r.jsx)(j.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)(u,{className:"h-24 w-24",children:[(0,r.jsx)(f,{src:a||"/placeholder-avatar.jpg"}),(0,r.jsx)(g,{className:"text-lg",children:"JD"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("label",{htmlFor:"avatar-upload",children:[(0,r.jsxs)(c.$,{type:"button",variant:"outline",className:"cursor-pointer",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Change Photo"]}),(0,r.jsx)("input",{id:"avatar-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(a){let e=new FileReader;e.onloadend=()=>{d(e.result)},e.readAsDataURL(a)}}})]}),(0,r.jsx)(c.$,{type:"button",variant:"ghost",onClick:()=>d(null),children:"Remove"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"JPG, PNG or GIF. Max size 2MB."})]})]})})]}),(0,r.jsxs)(j.Zp,{children:[(0,r.jsxs)(j.aR,{children:[(0,r.jsx)(j.ZB,{children:"Personal Information"}),(0,r.jsx)(j.BT,{children:"Update your personal details and contact information"})]}),(0,r.jsxs)(j.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"firstName",children:"First Name"}),(0,r.jsx)(o.p,{id:"firstName",placeholder:"Enter your first name",...F("firstName")}),J.firstName&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.firstName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"lastName",children:"Last Name"}),(0,r.jsx)(o.p,{id:"lastName",placeholder:"Enter your last name",...F("lastName")}),J.lastName&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.lastName.message})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"email",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(w.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(o.p,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...F("email")})]}),J.email&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.email.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(k.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(o.p,{id:"phone",type:"tel",placeholder:"Enter your phone number",className:"pl-10",...F("phone")})]}),J.phone&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.phone.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(A.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(o.p,{id:"dateOfBirth",type:"date",className:"pl-10",...F("dateOfBirth")})]}),J.dateOfBirth&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.dateOfBirth.message})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"location",children:"Location"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(R.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(o.p,{id:"location",placeholder:"Enter your location",className:"pl-10",...F("location")})]}),J.location&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.location.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"website",children:"Website"}),(0,r.jsx)(o.p,{id:"website",type:"url",placeholder:"https://your-website.com",...F("website")}),J.website&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.website.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"bio",children:"Bio"}),(0,r.jsx)(h,{id:"bio",placeholder:"Tell us about yourself...",rows:4,...F("bio")}),J.bio&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:J.bio.message}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Brief description for your profile. Max 500 characters."})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>S(),disabled:!O||e,children:"Reset Changes"}),(0,r.jsx)(c.$,{type:"submit",disabled:!O||e,className:"min-w-[120px]",children:e?"Saving...":(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]})]})})}},4997:(e,s,a)=>{"use strict";a.d(s,{O:()=>N});var r=a(5155),t=a(6874),l=a.n(t),i=a(5695),d=a(9434),n=a(4817),c=a(1007),o=a(4516),m=a(7108),x=a(1976),h=a(3861),p=a(1586),u=a(5525),f=a(381),g=a(9947),j=a(4835);function N(e){let{children:s}=e,a=(0,i.usePathname)(),{t}=(0,n.o)(),N=[{name:"Profile",href:"/account/profile",icon:c.A,description:"Manage your personal information"},{name:"Addresses",href:"/account/addresses",icon:o.A,description:"Manage shipping and billing addresses"},{name:"Orders",href:"/account/orders",icon:m.A,description:"View your order history and track shipments"},{name:"Wishlist",href:"/account/wishlist",icon:x.A,description:"Items you've saved for later"},{name:"Notifications",href:"/account/notifications",icon:h.A,description:"Manage your notification preferences"},{name:"Payment Methods",href:"/account/payment-methods",icon:p.A,description:"Manage your saved payment methods"},{name:"Security",href:"/account/security",icon:u.A,description:"Password and security settings"},{name:"Settings",href:"/account/settings",icon:f.A,description:"Account preferences and settings"}],y=[{name:"Help Center",href:"/help",icon:g.A},{name:"Contact Support",href:"/contact",icon:g.A}];return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})}),(0,r.jsxs)("nav",{className:"p-2",children:[(0,r.jsx)("div",{className:"space-y-1",children:N.map(e=>{let s=a===e.href;return(0,r.jsxs)(l(),{href:e.href,className:(0,d.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",s?"bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,r.jsx)(e.icon,{className:(0,d.cn)("mr-3 h-5 w-5",s?"text-primary-500":"text-gray-400 dark:text-gray-500")}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{children:e.name}),(0,r.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:e.description})]})]},e.href)})}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"space-y-1",children:[y.map(e=>(0,r.jsxs)(l(),{href:e.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)(e.icon,{className:"mr-3 h-4 w-4 text-gray-400 dark:text-gray-500"}),e.name]},e.href)),(0,r.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",children:[(0,r.jsx)(j.A,{className:"mr-3 h-4 w-4"}),"Sign Out"]})]})})]})]})}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:s})})]})})})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var r=a(5155),t=a(2115),l=a(968),i=a(2085),d=a(9434);let n=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(l.b,{ref:s,className:(0,d.cn)(n(),a),...t})});c.displayName=l.b.displayName},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>d,wL:()=>m});var r=a(5155),t=a(2115),l=a(9434);let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});i.displayName="Card";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...t})});d.displayName="CardHeader";let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});n.displayName="CardTitle";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...t})});c.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",a),...t})});o.displayName="CardContent";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",a),...t})});m.displayName="CardFooter"}},e=>{e.O(0,[3274,2804,5811,9197,8441,5964,7358],()=>e(e.s=3487)),_N_E=e.O()}]);